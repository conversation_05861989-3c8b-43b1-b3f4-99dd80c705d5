<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.yy.hdzk</groupId>
		<artifactId>hdpt_activity_xlogic_parent</artifactId>
		<version>1.0.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>hdpt_activity_xlogic_base</artifactId>
	<packaging>jar</packaging>
	<name>HDPT Activity Xlogic - Base</name>

	<dependencies>

		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
			<version>3.6.1</version>
		</dependency>

		<dependency>
			<groupId>com.googlecode.protobuf-java-format</groupId>
			<artifactId>protobuf-java-format</artifactId>
			<version>1.2</version>
		</dependency>

		<dependency>
			<groupId>com.yy.hdzk</groupId>
			<artifactId>hdpt_activity_xlogic_common</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>log4j-over-slf4j</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
			<scope>test</scope>
		</dependency>
        <dependency>
            <groupId>com.yy.hdzk</groupId>
            <artifactId>hdpt_activity_xlogic_handler</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.28</version>
        </dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>

		<dependency>
			<groupId>io.github.mweirauch</groupId>
			<artifactId>micrometer-jvm-extras</artifactId>
			<version>0.2.2</version>
		</dependency>

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-core</artifactId>
			<version>1.8.13</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.30</version>
		</dependency>

		<dependency>
			<groupId>com.yy.ent.commons</groupId>
			<artifactId>commons-protopack</artifactId>
			<version>2.6.0</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>

		<dependency>
			<groupId>com.baidu.yy</groupId>
			<artifactId>fostress-retry</artifactId>
			<version>${fostress.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.yy.ent.mobile</groupId>
					<artifactId>mobile-kafka-client</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.yy.g</groupId>
					<artifactId>yysoa-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.baidu.yy</groupId>
			<artifactId>fostress-api</artifactId>
			<version>${fostress.version}</version>
		</dependency>

		<dependency>
			<groupId>jdk.tools</groupId>
			<artifactId>jdk.tools</artifactId>
			<version>1.7</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba.csp</groupId>
			<artifactId>sentinel-core</artifactId>
			<version>${ali-sentinel.version}</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba.csp</groupId>
			<artifactId>sentinel-transport-simple-http</artifactId>
			<version>${ali-sentinel.version}</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba.csp</groupId>
			<artifactId>sentinel-web-servlet</artifactId>
			<version>${ali-sentinel.version}</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba.csp</groupId>
			<artifactId>sentinel-apache-dubbo-adapter</artifactId>
			<version>${ali-sentinel.version}</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
					<encoding>${project.build.sourceEncoding}</encoding>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
