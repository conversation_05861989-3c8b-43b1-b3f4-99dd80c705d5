<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true" scan="true">
	<property name="PROGRESS_GROUP" value="${group:-}"/>
	<property name="PROGRESS_NAME" value="activity${PROGRESS_GROUP}.gameecology.yy.com"/>

	<property name="application" value="${MY_PROJECT_NAME:-activity}"/>
	<property name="jsonLogFile" value="json.log"/>
	<property name="basePath" value="/data/weblog/java"/>
	<property name="jsonFileName" value="${basePath}/${application}/${jsonLogFile}"/>

	<appender name="JSON_ROLL_FILE_SYNC"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${jsonFileName}</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${jsonFileName}_%d{yyyy-MM-dd}_%i.gz</fileNamePattern>
			<maxHistory>5</maxHistory>
			<maxFileSize>400MB</maxFileSize>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
		</rollingPolicy>
		<encoder class="net.logstash.logback.encoder.LogstashEncoder" >
			<customFields>{"application":"${application}"}</customFields>
		</encoder>
	</appender>

	<appender name="JSON_ROLL_FILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="JSON_ROLL_FILE_SYNC"/>
		<includeCallerData>true</includeCallerData>
	</appender>

	<appender name="GAME_ROLL_FILE_SYNC"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>/data/weblog/business/${PROGRESS_NAME}/all.log2</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>/data/weblog/business/${PROGRESS_NAME}/all_%d{yyyy-MM-dd}_%i.log.zip
			</fileNamePattern>
			<maxHistory>30</maxHistory>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>5120MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<charset>UTF-8</charset>
			<pattern>
				<![CDATA[
					%date %level [%X{origin} %X{trace_id}] [%thread] %logger{0}:%line - %msg%n
				]]>
			</pattern>
		</encoder>
	</appender>

	<appender name="GAME_ROLL_FILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="GAME_ROLL_FILE_SYNC" />
		<includeCallerData>true</includeCallerData>
	</appender>

	<appender name="ERROR_WARN_ROLL_FILE_SYNC"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>WARN</level>
		</filter>
		<file>/data/weblog/business/${PROGRESS_NAME}/error.log2
		</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>/data/weblog/business/${PROGRESS_NAME}/error_%d{yyyy-MM-dd}_%i.log.zip
			</fileNamePattern>
			<maxHistory>30</maxHistory>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>5120MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<charset>UTF-8</charset>
			<pattern>
				<![CDATA[
					%date %level [%X{origin} %X{trace_id}] [%thread] %logger{0}:%line - %msg%n
				]]>
			</pattern>
		</encoder>
	</appender>

	<appender name="ERROR_WARN_ROLL_FILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="ERROR_WARN_ROLL_FILE_SYNC" />
		<includeCallerData>true</includeCallerData>
	</appender>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="com.yy.gameecology.common.utils.ConsoleFilter"/>
		<encoder>
			<charset>UTF-8</charset>
			<pattern>
				<![CDATA[
					%date %level [%X{origin} %X{trace_id}] [%thread] %logger{0}:%line - %msg%n
				]]>
			</pattern>
		</encoder>
	</appender>

	<appender name="monitor"
		class="com.yy.ent.clients.bam.logback.AsyncLogCollectAppender">
		<srvname>java.gameecology.activity${PROGRESS_GROUP}</srvname>
		<filter class="com.yy.ent.clients.bam.logback.EntryConditionFilter"/>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
	</appender>

	<appender name="HAR_ROLL_FILE_SYNC" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>/data/hiido/${MY_POD_NAME:-business}/ge_activity_report/hdzt_activity_report${group}.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>/data/hiido/${MY_POD_NAME:-business}/ge_activity_report/hdzt_activity_report${group}.log.%d{yyyyMMddHH}</fileNamePattern>
			<maxHistory>1000</maxHistory>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
		</rollingPolicy>
		<encoder>
			<charset>UTF-8</charset>
			<pattern>
				<![CDATA[
					%msg%n
				]]>
			</pattern>
		</encoder>
	</appender>

	<appender name="HAR_ROLL_FILE" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="HAR_ROLL_FILE_SYNC" />
		<includeCallerData>true</includeCallerData>
	</appender>

    <logger name="com.yy.ent.client.s2s.service" additivity="false" level="WARN">
        <appender-ref ref="GAME_ROLL_FILE" />
    </logger>

	<!-- 这个是打海猫上报文件专用的日志器，千万不能用来打其它日志，否则海猫数据入库解析可能失败！！！ - added by guoliping / 20201-04-12 -->
	<logger name="HDZT_ACTIVITY_REPORT" level="INFO" additivity="false">
		<appender-ref ref="GAME_ROLL_FILE" />
		<appender-ref ref="HAR_ROLL_FILE" />
	</logger>

	<if condition='!property("deploy.profile").equals("release1")'>
		<then>
			<root>
			</root>
		</then>
	</if>

	<root level="INFO">
		<appender-ref ref="GAME_ROLL_FILE" />
		<appender-ref ref="JSON_ROLL_FILE"/>
		<appender-ref ref="ERROR_WARN_ROLL_FILE" />
		<appender-ref ref="monitor" />
		<appender-ref ref="STDOUT" />
	</root>

</configuration>
