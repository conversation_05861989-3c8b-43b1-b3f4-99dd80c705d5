local key = KEYS[1]
local seqKey = KEYS[2]
local score = ARGV[1]
local seqExpireSeconds = tonumber(ARGV[2] or 0)
local seqOldValueKey = seqKey .. '_old_val'

local hasSet = tonumber(redis.call('SETNX', seqKey, 1) or 0)
if hasSet == 0 then
	return redis.call('GET', seqOldValueKey)
end

if seqExpireSeconds > 0 then
    redis.call('EXPIRE', seqKey, seqExpireSeconds)
end

local newScore = tonumber(redis.call('INCRBY', key, score) or 0)
redis.call('SET', seqOldValueKey, cjson.encode({newScore, 0}))

if (seqExpireSeconds > 0) then
    redis.call('EXPIRE', seqOldValueKey, seqExpireSeconds)
end

return cjson.encode({newScore, 1})