local key = KEYS[1]
local seqKey = KEYS[2]
local field = ARGV[1]
local step = tonumber(ARGV[2])
local limit = tonumber(ARGV[3])
local canPartialAdd = tonumber(ARGV[4])
local seqExpireSeconds = tonumber(ARGV[5] or 0)

local seqOldValueKey = seqKey .. '_old_val'

local hasSet = tonumber(redis.call('SETNX', seqKey, 1) or 0)
if hasSet == 0 then
    local oldValue = redis.call('GET', seqOldValueKey)
    if oldValue then
        local oldResult = cjson.decode(oldValue)
        -- 区分重复seq的返回
        oldResult[3] = 0
        return cjson.encode(oldResult)
    else
        return cjson.encode({-1, 0, 0})
    end
end

local partialAdd = 0;

if seqExpireSeconds > 0 then
    redis.call('EXPIRE', seqKey, seqExpireSeconds)
end

local oldScore = tonumber(redis.call('HGET', key, field) or 0)

if step >= 0 then
    if oldScore >= limit then
        return cjson.encode({ -1, oldScore, 1 })
        -- 当 step为正时， incr 后不能超过 limit，
    elseif oldScore + step > limit and canPartialAdd == 0 then
        return cjson.encode({ -1, oldScore, 1 })
    elseif oldScore + step > limit and canPartialAdd == 1 then
        -- 部分增加
        partialAdd = limit - oldScore;
    end
else
    if oldScore <= limit then
        return cjson.encode({ -2, oldScore, 1 })
        -- 当 step 为负时 incr 后不能低于 limit
    elseif oldScore + step < limit and canPartialAdd == 0 then
        return cjson.encode({ -2, oldScore, 1 })
    elseif oldScore + step < limit and canPartialAdd == 1 then
        -- 部分增加
        partialAdd = limit - oldScore;
    end
end

local result

if partialAdd == 0 then
    local newScore = tonumber(redis.call('HINCRBY', KEYS[1], field, step) or 0)
    result = cjson.encode({ 1, newScore, 1 })
else
    tonumber(redis.call('HINCRBY', KEYS[1], field, partialAdd) or 0)
    result = cjson.encode({ 2, partialAdd, 1 })
end

--暂存当前结果旧值，保证相同seq来请求的时候返回的数值一致
redis.call('SET', seqOldValueKey, result)
if (seqExpireSeconds > 0) then
    redis.call('EXPIRE', seqOldValueKey, seqExpireSeconds)
end

return result