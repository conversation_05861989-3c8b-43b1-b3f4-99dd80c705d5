---------------------------------------------------------------------------------
-- 通用排队倒计时，用于排队开宝箱等玩法  - guoliping/2021-11-13
-- 核心逻辑：
--  1）list元素结构为：${倒计时开始时刻}|${排队条目ID}，${倒计时开始时刻}初始为0，开始后为 yyyyMMddHHmmss（为便于维护不用秒数）
--  2）先验证现场是否被破坏（队首元素 和 将要处理的倒计时条目是否H相等）
--  3）若 队首元素的头部为 0，则初始化启动数据
--  4）已启动的倒计时为0以下则移除倒计时条目
---------------------------------------------------------------------------------

-- 条目列表KEY， list 结构
local listKey =  KEYS[1]

-- 标记条目倒计时开始的 string key（一般用于控制用户报名等，若这个key不存在，就不能报名）， 配合 expiration 工作
local itemKey =  KEYS[2]

-- 倒计时剩余的秒数
local left = tonumber(ARGV[1])

-- 宝箱ID
local itemId = ARGV[2]

-- 宝箱到时时的开始时刻，数据格式： 0 or yyyyMMddHHmmss
local startTime = tonumber(ARGV[3])

-- 当前时刻，格式只有： yyyyMMddHHmmss
local nowTime = tonumber(ARGV[4])

-- itemKey 的过期时间，小于等于0的不过期
local expireSeconds =  tonumber(ARGV[5])

-- 判断字符串是否为空
local function empty(s)
    return s == nil or s == ''
end

-- 1. 验证现场：若首元素 != 老元素，说明现场已经破坏， 直接返回
local oldItem = startTime .. '|' .. itemId
local currItem = redis.call('LINDEX', listKey, 0)
if currItem ~= oldItem then
    return -1
end

-- 2. 若宝箱倒计时还没开始，初始化宝箱倒计时数据
if startTime == 0 then
    if not empty(itemKey) then
        -- 倒计时标记key已经存在，失败返回
        if redis.call('SETNX', itemKey, nowTime) == 0 then
            return -2
        end

        -- 设置倒计时标记key的过期时间
        if expireSeconds > 0 then
            redis.call('EXPIRE', itemKey, expireSeconds)
        end
    end

    -- 更新首元素，设置倒计时标记key的过期时间，这个操作必须成功，否则数据可能不一致
    local newItem = nowTime .. '|' .. itemId
    redis.call('LSET', listKey, 0, newItem)
end

-- 3. 若倒计时结束了，需要去掉首元素
if left <= 0 then
    redis.call('LPOP', listKey)
end

return 1