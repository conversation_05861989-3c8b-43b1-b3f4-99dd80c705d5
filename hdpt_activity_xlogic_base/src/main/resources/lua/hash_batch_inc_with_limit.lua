local para = ARGV[1]
local paras = cjson.decode(para);

for i = 1, #paras do
    local key = paras[i].key
    local field = paras[i].field
    local inc = tonumber(paras[i].inc or 0)
    local limitType = tonumber(paras[i].limitType or 0)
    local limit = tonumber(paras[i].limit or 0)

    local oldScore = tonumber(redis.call('HGET', key, field) or 0)
    local afterInc = oldScore + inc
    if limitType == 1 and afterInc > limit then
        return false
    elseif limitType == -1 and afterInc < limit then
        return false
    end

end

for i = 1, #paras do
    local key = paras[i].key
    local field = paras[i].field
    local inc = paras[i].inc

    redis.call('HINCRBY', key, field, inc)

end

return true