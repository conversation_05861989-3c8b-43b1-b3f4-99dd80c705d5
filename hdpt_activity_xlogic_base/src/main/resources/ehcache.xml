<?xml version="1.0" encoding="UTF-8"?>
<ehcache>

	<diskStore path="java.io.tmpdir/3g.500wan.com/appcache" />

	<defaultCache maxElementsInMemory="150000" eternal="false"
		overflowToDisk="false" diskPersistent="false" timeToLiveSeconds="86400"
		timeToIdleSeconds="86400" diskExpiryThreadIntervalSeconds="1200" />

    <!--测试环境特意不缓存，方便更新数据立刻生效，提高测试效率-->
	<cache name="com.yy.gameecology.activity.METHOD_CACHE" maxElementsInMemory="150000"
		eternal="false" overflowToDisk="false" diskPersistent="false"
		timeToIdleSeconds="86400" timeToLiveSeconds="86400"
		diskExpiryThreadIntervalSeconds="1200" />

	<cache name="save_snapshot_request" maxElementsInMemory="50000"
		   eternal="false" overflowToDisk="false" diskPersistent="false"
		   timeToIdleSeconds="86400" timeToLiveSeconds="86400"
		   diskExpiryThreadIntervalSeconds="1200" />

</ehcache>