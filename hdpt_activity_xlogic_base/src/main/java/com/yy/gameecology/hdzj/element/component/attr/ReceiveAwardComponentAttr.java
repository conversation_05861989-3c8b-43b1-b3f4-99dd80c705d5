package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.activity.bean.hdzt.HdztAwardConfig;
import com.yy.gameecology.hdzj.bean.ReceiveAwardConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-08-10 16:49
 **/
public class ReceiveAwardComponentAttr extends ComponentAttr {

    // 每个发放错误尝试的次数，总共最多调用 1 + retry 次， 让发放尽量成功
    @ComponentAttrField(labelText = "重试次数", remark = "每个发放错误尝试的次数，总共最多调用 1 + retry 次， 让发放尽量成功")
    private int retry = 2;

    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private int busiId = 800;

    /**
     * 奖励资格配置
     */
    @ComponentAttrField(labelText = "任务配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "奖励代码"),
                    @SubField(fieldName = Constant.VALUE, type = ReceiveAwardConfig.class)
            })
    private LinkedHashMap<String, ReceiveAwardConfig> receiveAwardConfigMap;

    /**
     * 奖励配置
     **/
    @ComponentAttrField(labelText = "中台奖励配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "奖励代码"),
                    @SubField(fieldName = Constant.VALUE, type = Object.class, skip = true),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = HdztAwardConfig.class)
            })
    private Map<String, List<HdztAwardConfig>> hdztAwardConfigs;

    public Map<String, ReceiveAwardConfig> getReceiveAwardConfigMap() {
        return receiveAwardConfigMap;
    }

    public void setReceiveAwardConfigMap(LinkedHashMap<String, ReceiveAwardConfig> receiveAwardConfigMap) {
        this.receiveAwardConfigMap = receiveAwardConfigMap;
    }

    public int getRetry() {
        return retry;
    }

    public void setRetry(int retry) {
        this.retry = retry;
    }

    public int getBusiId() {
        return busiId;
    }

    public void setBusiId(int busiId) {
        this.busiId = busiId;
    }

    public Map<String, List<HdztAwardConfig>> getHdztAwardConfigs() {
        return hdztAwardConfigs;
    }

    public void setHdztAwardConfigs(Map<String, List<HdztAwardConfig>> hdztAwardConfigs) {
        this.hdztAwardConfigs = hdztAwardConfigs;
    }
}
