package com.yy.gameecology.hdzj.element;

import com.alibaba.fastjson.JSON;

/**
 * 组件、部件 等元素配置的基础类
 */
public class ComponentAttr {
    protected long actId;

    protected long cmptId;

    protected long cmptUseInx;

    // 扩展参数（见表字段：hdzj_component.extjson）
    protected String extjson;

    public long getActId() {
        return actId;
    }

    public void setActId(long actId) {
        this.actId = actId;
    }

    public long getCmptId() {
        return cmptId;
    }

    public void setCmptId(long cmptId) {
        this.cmptId = cmptId;
    }

    public long getCmptUseInx() {
        return cmptUseInx;
    }

    public void setCmptUseInx(long cmptUseInx) {
        this.cmptUseInx = cmptUseInx;
    }

    public String getExtjson() {
        return extjson;
    }

    public void setExtjson(String extjson) {
        this.extjson = extjson;
    }

    public String toString() {
        return JSON.toJSONString(this);
    }
}
