package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * rank 2 rank
 *
 * <AUTHOR>
 * @since 2021/10/17
 */
@Getter
@Setter
public class Rank2RankComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "来源榜单", remark = "使用榜单结束事件驱动,则阶段为0;使用阶段结束事件驱动,则阶段id为具体阶段"
            , subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = RankAndPhaseId.class)})
    private List<RankAndPhaseId> sources = Collections.emptyList();

    @ComponentAttrField(labelText = "累榜itemId")
    private String sinkItemId = StringUtils.EMPTY;

    @ComponentAttrField(labelText = "分值奖励", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "排名")
            , @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖励值")
    })
    private Map<Long, Long> rankScoreAwardMap = new HashMap<>();

    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private Long bizId;

    @ComponentAttrField(labelText = "累榜角色id")
    private Long actorId;

    @Getter
    @Setter
    public static class RankAndPhaseId {
        @ComponentAttrField(labelText = "榜单id")
        private long rankId;
        @ComponentAttrField(labelText = "榜单阶段")
        private long phaseId;
    }

}


