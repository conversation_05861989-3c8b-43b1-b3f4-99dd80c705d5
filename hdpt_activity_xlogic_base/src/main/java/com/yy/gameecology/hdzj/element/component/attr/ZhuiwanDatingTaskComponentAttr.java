package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.annotation.SkipCheck;
import com.yy.gameecology.hdzj.bean.TaskConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;

import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-08-11 16:05
 **/
@SkipCheck
public class ZhuiwanDatingTaskComponentAttr extends ComponentAttr {

    private int awardRetry = 2;

    private long rankId;

    private long receiveAwardComponentIndex = 1;


    /**
     * 每日累积累榜得分任务编码
     */
    private String scoreDayTaskCode = "score_day_task";

    /**
     * 每日累积送主播任务数任务编码
     */
    private String sendAnchorAmountDayTaskCode = "send_anchor_amount_day_task";

    /**
     * 完成首次赠送任务任务编码
     */
    private String firstSendActTaskCode = "first_send";

    /**
     * 完成日任务累积天数任务编码，%s累积完成天数
     */
    private String completeDayTaskCode = "complete_day_task_%s";


    /**
     * 日任务子任务数 数量
     */
    private int subDayTaskAmount = 2;


    /**
     * key 任务编码，value 完成任务配置
     */
    private Map<String, TaskConfig> taskConfigMap;


    public int getAwardRetry() {
        return awardRetry;
    }

    public void setAwardRetry(int awardRetry) {
        this.awardRetry = awardRetry;
    }

    public long getReceiveAwardComponentIndex() {
        return receiveAwardComponentIndex;
    }

    public void setReceiveAwardComponentIndex(long receiveAwardComponentIndex) {
        this.receiveAwardComponentIndex = receiveAwardComponentIndex;
    }

    public Map<String, TaskConfig> getTaskConfigMap() {
        return taskConfigMap;
    }

    public void setTaskConfigMap(Map<String, TaskConfig> taskConfigMap) {
        this.taskConfigMap = taskConfigMap;
    }

    public String getScoreDayTaskCode() {
        return scoreDayTaskCode;
    }

    public void setScoreDayTaskCode(String scoreDayTaskCode) {
        this.scoreDayTaskCode = scoreDayTaskCode;
    }

    public String getSendAnchorAmountDayTaskCode() {
        return sendAnchorAmountDayTaskCode;
    }

    public void setSendAnchorAmountDayTaskCode(String sendAnchorAmountDayTaskCode) {
        this.sendAnchorAmountDayTaskCode = sendAnchorAmountDayTaskCode;
    }

    public String getFirstSendActTaskCode() {
        return firstSendActTaskCode;
    }

    public void setFirstSendActTaskCode(String firstSendActTaskCode) {
        this.firstSendActTaskCode = firstSendActTaskCode;
    }

    public String getCompleteDayTaskCode() {
        return completeDayTaskCode;
    }

    public void setCompleteDayTaskCode(String completeDayTaskCode) {
        this.completeDayTaskCode = completeDayTaskCode;
    }

    public int getSubDayTaskAmount() {
        return subDayTaskAmount;
    }

    public void setSubDayTaskAmount(int subDayTaskAmount) {
        this.subDayTaskAmount = subDayTaskAmount;
    }

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public boolean isMyDuty(long rankId) {
        return rankId < 1 ? false : this.rankId == rankId;
    }
}
