package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-07-25 18:28
 **/
@Data
public class CounterComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "区分用户", remark = "key按用户去重")
    private boolean userDistinct = true;

    @ComponentAttrField(labelText = "按时间分", remark = "如按日分，填 yyyyMMdd")
    private String timeFormat;
}
