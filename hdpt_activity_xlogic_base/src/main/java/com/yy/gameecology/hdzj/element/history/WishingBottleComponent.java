package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.RankDataEvent;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.exception.LotteryException;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.PBCommonBannerId;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.LotteryUtils;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.CommonPBOperateRequest;
import com.yy.gameecology.hdzj.bean.CommonPBOperateResp;
import com.yy.gameecology.hdzj.bean.InvokeSendBox;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.WishingBottleComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * desc:许愿瓶组件-一种用户送礼触发特效，点击手工抽奖组件
 * <p>
 * 应用场景：
 * 元旦活动许愿瓶玩法（抽奖+过任务+集卡+瓜分）
 *
 * @createBy 曾文帜
 * @create 2021-12-13 15:08
 **/
@Deprecated
@Component
public class WishingBottleComponent extends BaseActComponent<WishingBottleComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ActInfoService actInfoService;


    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private EnrollmentNewService enrollmentNewService;


    @Autowired
    private Locker locker;

    @Override
    public Long getComponentId() {
        return ComponentId.WISHING_BOTTLE;
    }

    /**
     * string  许愿瓶时效性标记
     * %s_%s:%s sid_ssid:boxId
     */
    private static final String BOX_INFO_KEY = "box_info:%s_%s:%s";

    /**
     * set 送了特定礼物有资格获取稀缺祝福用户
     * %s yyyyMMdd
     */
    private static final String RARE_AWARD_USER_KEY = "box_rare_award_user:%s";

    /**
     * 用户收到的祝福余额
     * %s uid
     * hash field===giftcode value===amount
     */
    private static final String USER_AWARD_BALANCE = "user_award_balance:%s";

    /**
     * 用户集齐总祝福的套数
     */
    private static final String USER_COLLECT_ALL_AMOUNT = "user_collect_all_amount";

    /**
     * hash 每天可瓜分奖池的主播数量
     */
    private static final String CAN_CARVE_ANCHOR_AMOUNT = "can_carve_anchor_amount";

    /**
     * 发送许愿瓶排队队列名
     */
    private static final String SEND_BOX_QUEUE_NAME = "wish_box_queue";


    /**
     * 记录今天送过520礼物的付费用户,添加稀缺礼物（稀缺祝福）中奖资格
     */
    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = true)
    public void sendGiftLottery(SendGiftEvent event, WishingBottleComponentAttr attr) {
        long actId = attr.getActId();
        if (!actInfoService.inActTime(actId)) {
            //不在时间内
            return;
        }

        Long userUid = event.getSendUid();
        String giftId = event.getGiftId();
        long giftNum = event.getGiftNum();

        //非指定业务以及礼物
        BusiId busiId = broadCastHelpService.changeBroTemplate2BusiId(event.getTemplate());
        if (attr.getBusiId() != busiId.getValue() || !attr.getRareAwardUserGift().containsKey(giftId)) {
            return;
        }
        long amount = attr.getRareAwardUserGift().get(event.getGiftId());
        //添加稀缺礼物（稀缺祝福）中奖资格
        if (giftNum >= amount) {
            String key = makeRareAwardUserKey(attr);
            log.info("add rare gift,seq:{},actId:{},uid:{},key:{}", attr.getActId(), event.getSeq(), userUid, key);
            actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId())).opsForSet().add(key, userUid + "");
        }

    }


    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void anchorAccomplishTask(TaskProgressChanged event, WishingBottleComponentAttr attr) {
        log.info("anchorAccomplishTask,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        //一级任务 许愿值
        anchorAccomplishYwzTask(event, attr);
        //二级任务 许愿瓶
        anchorAccomplishXypTask(event, attr);
    }

    /**
     * 愿望值任务，触发许愿瓶
     */
    private void anchorAccomplishYwzTask(TaskProgressChanged event, WishingBottleComponentAttr attr) {
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        String redisGroup = getRedisGroupCode(attr.getActId());
        long startTaskIndex = event.getStartTaskIndex();
        long curTaskIndex = event.getCurrTaskIndex();
        long amount = curTaskIndex - startTaskIndex;
        if (amount <= 0) {
            return;
        }

        long anchorId = Convert.toLong(event.getMember(), 0);
        //主播完成任务时所在频道，这个必须要提前获取，避免后续离开频道获取不到
        UserCurrentChannel channel = commonService.getNoCacheUserCurrentChannel(anchorId, 3);
        if (channel == null) {
            log.error("releaseBox error,anchor not in channel,event:{},attr:{},amount:{}", JSON.toJSONString(event), JSON.toJSONString(attr), amount);
            return;
        }

        //---许愿瓶逻辑  做成延迟、排队
        log.info("begin releaseBox,uid:{},sid:{},ssid:{},amount:{}", event.getMember(), channel.getTopsid(), channel.getSubsid(), amount);
        for (int i = 0; i < amount; i++) {
            String boxId = UUID.randomUUID().toString();


            //更新许愿瓶榜单---防止有排队跨天问题，更新数据放到排队前
            updateBoxRank(event, 1, attr);

            InvokeSendBox box = new InvokeSendBox(attr, event, channel, boxId);
            long queueDelay = attr.getBoxEffectiveSeconds() + attr.getBoxQueueDelaySeconds();
            //按子频道排队
            String subQueueName = channel.getTopsid() + "_" + channel.getSubsid();
            long delayInvokeTime = actRedisDao.addLineUp(redisGroup, makeKey(attr, SEND_BOX_QUEUE_NAME), subQueueName, boxId, JSON.toJSONString(box), queueDelay);
            log.info("releaseBox add lineup,invoke time,uid:{},id:{},time:{}", event.getMember(), boxId, delayInvokeTime);
        }
    }


    /**
     * 许愿瓶任务，上报瓜分榜单
     */
    private void anchorAccomplishXypTask(TaskProgressChanged event, WishingBottleComponentAttr attr) {
        if (event.getRankId() != attr.getTask2RankId() || event.getPhaseId() != attr.getTask2PhaseId()) {
            return;
        }

        //上报主播瓜分奖池资格
        if (event.getCurrTaskIndex() >= attr.getCanCarveTaskIndex()) {
            String redisGroup = getRedisGroupCode(attr.getActId());
            log.info("add carve qualifications,uid:{}", event.getMember());
            Date time = DateUtil.getDate(event.getOccurTime());
            String dateCode = DateUtil.format(time, DateUtil.PATTERN_TYPE2);
            //上报瓜分资格，seq不重复上报
            String seq = makeKey(attr, "seq:carve_qua:" + dateCode + ":" + event.getMember());
            Date timestamp = DateUtil.getDate(event.getOccurTime());
            updateCarveRank(seq, timestamp.getTime(), event.getMember(), 1, attr);
            //增加完成任务人数，给前端展示用
            actRedisDao.hIncrByKeyWithSeq(redisGroup, seq, makeKey(attr, CAN_CARVE_ANCHOR_AMOUNT), dateCode, 1, 0);
        }
    }


    /***
     * 排队发放许愿瓶
     */
    // @Scheduled(cron = "0/3 * * * * ? ")
    public void invokeSettlePreDayAward() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        List<ActivityInfoVo> effectActInfos = hdztRankingThriftClient.queryEffectActInfos();
        if (CollectionUtils.isEmpty(effectActInfos)) {
            return;
        }

        for (ActivityInfoVo actInfo : effectActInfos) {
            long actId = actInfo.getActId();
            if (!actIds.contains(actId)) {
                continue;
            }
            List<WishingBottleComponentAttr> attrs = this.getAllComponentAttrs(actId);
            if (CollectionUtils.isEmpty(attrs)) {
                continue;
            }
            attrs.forEach(attr -> {
                Date now = commonService.getNow(actId);
                //活动结束后，有可能还要结算前一天，所以用活动展示结束时间来判断
                if (!actInfoService.inActShowTime(now, actInfo)) {
                    return;
                }
                String boxInfo = actRedisDao.popLineUp(getRedisGroupCode(actId), makeKey(attr, SEND_BOX_QUEUE_NAME));
                if (StringUtil.isBlank(boxInfo)) {
                    return;
                }
                log.info("popLineUp box,actId:{},index:{},box Info:{}", actId, attr.getCmptUseInx(), JSON.toJSONString(boxInfo));
                InvokeSendBox box = JSON.parseObject(boxInfo, InvokeSendBox.class);
                invokeSendBox(box.getBoxId(), box);
            });
        }

    }


    public void invokeSendBox(String boxId, InvokeSendBox box) {
        log.info("invokeSendBox,boxId:{}", boxId);
        saveBoxInfo(box.getAttr(), box.getChannel().getTopsid(), box.getChannel().getSubsid(), boxId);
        //触发许愿瓶
        broBox(box.getEvent(), box.getAttr(), box.getChannel(), boxId);
    }


    public void saveBoxInfo(WishingBottleComponentAttr attr, long sid, long ssid, String boxId) {
        log.info("saveBoxInfo actId:{},sid:{},ssid:{},boxId:{}", attr.getActId(), sid, ssid, boxId);
        String key = makeBoxInfoKey(attr, sid, ssid, boxId);
        long expireSeconds = attr.getBoxEffectiveSeconds() + attr.getBoxEffectiveDelaySeconds();
        actRedisDao.set(getRedisGroupCode(attr.getActId()), key, DateUtil.getNowYyyyMMddHHmmss(), expireSeconds);
    }


    /**
     * 广播许愿瓶
     */
    public void broBox(TaskProgressChanged event, WishingBottleComponentAttr attr, UserCurrentChannel channel, String boxId) {
        Date now = DateUtil.getDate(event.getOccurTime());
        //取贡献榜最高神豪
        String dateStr = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        Map<String, String> ext = Maps.newHashMap();
        ext.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, event.getMember());
        int top1 = 1;
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getContributeRankId(), attr.getPhaseId(), dateStr, top1, ext);
        String top1PlayerNick = getTop1Nick(ranks);


        JSONObject jsonObject = new JSONObject();
        //唯一抽奖id
        jsonObject.put("boxId", boxId);
        //许愿瓶有效期倒计时
        jsonObject.put("effectiveSeconds", attr.getBoxEffectiveSeconds());

        //出 业务 宝箱横幅
        GameecologyActivity.BannerBroadcast.Builder banner = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(attr.getActId())
                .setBannerId(PBCommonBannerId.WISHING_BOTTLE)
                .setBannerType(0)
                .setUserNick(top1PlayerNick)
                .setJsonData(jsonObject.toJSONString());
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(banner).build();
        svcSDKService.broadcastSub(channel.getTopsid(), channel.getSubsid(), msg);

        log.info("broBox,channelInfo:{},boxId:{},nick:{}", JSON.toJSONString(channel), boxId, top1PlayerNick);
    }


    /**
     * 点击许愿瓶抽奖（抽奖+发奖）
     * <p>
     * 处理通用客户端pb请求
     * 响应抽奖接口
     */
    @Override
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        WishingBottleComponentAttr attr = getComponentAttr(request.getActId(), request.getCmptIndex());
        String lockName = makeKey(attr, "commonOperatePbRequest:" + request.getOpUid());
        Secret secret = locker.lock(lockName, 5, request.getSeq(), 5);
        if (secret == null) {
            return new CommonPBOperateResp(-1, "", "手速太快了！");
        }
        try {
            return invokeCommonOperatePbRequest(request);
        } catch (Exception e) {
            log.error("commonOperatePbRequest error,result:{},e:{}", JSON.toJSONString(request), e.getMessage(), e);
            return new CommonPBOperateResp(-2, "", "网络超时，请重试！");

        } finally {
            locker.unlock(lockName, secret, 5);
        }

    }


    private CommonPBOperateResp invokeCommonOperatePbRequest(CommonPBOperateRequest request) {
        log.info("invokeCommonOperatePbRequest request:{}", JSON.toJSONString(request));
        //非活动中不响应
        final boolean suc = actInfoService.intWorkTime(request.getActId(), 0, 60 * 30);
        if (!suc) {
            log.warn("not in act,uid:{},boxId:{},seq:{}", request.getOpUid(), request.getOpId(), request.getSeq());
            return null;
        }

        String redisGroup = getRedisGroupCode(request.getActId());
        WishingBottleComponentAttr attr = getComponentAttr(request.getActId(), request.getCmptIndex());
        long uid = request.getOpUid();
        String boxId = request.getOpId();
        //许愿瓶id已过期或不存在
        String key = makeBoxInfoKey(attr, request.getOpSid(), request.getOpSsid(), boxId);
        String boxInfo = actRedisDao.get(redisGroup, key);
        if (StringUtil.isBlank(boxInfo)) {
            log.info("box expire or box not exist,uid:{},key:{}", request.getOpUid(), key);
            return null;
        }

        //抽奖，抽祝福
        String awardGiftCode = dealLottery(attr, boxId, uid);
        if (StringUtil.isBlank(awardGiftCode)) {
            //可能重复抽，未抽中
            log.warn("awardGiftCode empty,uid:{},boxId:{}", request.getOpUid(), boxId);
            return null;
        }

        log.info("dealLottery,actId:{},boxId:{},uid:{},awardGift:{}", request.getActId(), request.getOpId(), uid, awardGiftCode);

        //增加抽中的祝福余额
        addUserAwardBalance(attr, redisGroup, uid, awardGiftCode, 1);

        //当日抽中的那祝福达到6种，触发新年成就特效+2天新年进场秀，发送相应横幅（子频道可见）
        dealCollectAllAward(attr, uid, request.getOpSid(), request.getOpSsid());

        //抽奖结果
        CommonPBOperateResp resp = new CommonPBOperateResp();
        resp.setCode(0);
        resp.setContent(awardGiftCode);

        return resp;
    }

    /**
     * 抽奖，抽祝福
     */
    public String dealLottery(WishingBottleComponentAttr attr, String boxId, long uid) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        //看是否有资格抽取特殊祝福
        boolean bingoRareAward = false;
        String rareUserKey = makeRareAwardUserKey(attr);
        if (actRedisDao.sIsMember(redisGroup, rareUserKey, uid + "")) {
            bingoRareAward = LotteryUtils.lottery(attr.getRareAwardBingoRatio(), attr.getRareAwardTotalRatio());
        }
        String awardGiftCode;
        //1个人1个瓶子只能抽1次
        String awardSeq = boxId + "|" + uid;
        //抽中特殊奖励，直接发放
        if (bingoRareAward) {
            awardGiftCode = attr.getRareAwardGiftCode();
            long taskId = attr.getRareAwardConfig().getTaskId();
            long packageId = attr.getRareAwardConfig().getPackageId();
            BatchWelfareResult welfareResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(), uid, taskId, 1, packageId, awardSeq, 3);
            //重复发奖
            final boolean suc = welfareResult != null && welfareResult.getReason().contains("异步发放中，请勿重复请求");
            if (suc) {
                log.warn("welfare dup,uid:{},taskId:{},result:{}", uid, taskId, JSON.toJSONString(welfareResult));
                return null;
            }
        }
        //没抽中特殊奖励，走中台额外抽奖
        else {
            long taskId = attr.getCommonAwardConfig().getTaskId();
            BatchLotteryResult batchLotteryResult =
                    hdztAwardServiceClient.doLottery(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(), uid, taskId, 1, 0, awardSeq);
            //key：packageId，val：recordId
            Map<Long, Long> bingoPackage = batchLotteryResult.getRecordIds();
            //重复抽奖
            if (batchLotteryResult.getCode() == LotteryException.E_LOTTERY_COMPLETED) {
                log.warn("doLottery dup,uid:{},taskId:{},result:{}", uid, taskId, JSON.toJSONString(batchLotteryResult));
                return null;
            }
            if (MapUtils.isEmpty(bingoPackage)) {
                log.error("doLottery error,uid:{},taskId:{},result:{}", uid, taskId, JSON.toJSONString(batchLotteryResult));
                return null;
            }
            long packageId = bingoPackage.keySet().iterator().next();
            awardGiftCode = attr.getPackageIdGiftCodeMap().get(packageId);
        }

        return awardGiftCode;
    }


    /**
     * 处理集齐全部卡的特殊奖励
     * <p>
     * 内部逻辑复杂，有先读后写操作，依赖上层用户级别分布式锁保证并发下数据准确性！！！
     */
    private void dealCollectAllAward(WishingBottleComponentAttr attr, long uid, long sid, long ssid) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        String collectAmountKey = makeKey(attr, USER_COLLECT_ALL_AMOUNT);
        long oldCollectAmount = Convert.toLong(actRedisDao.hget(redisGroup, collectAmountKey, uid + ""), 0);

        Set<String> allGiftCode = getAllGiftCode(attr);
        long newCollectAmount = -1;
        Map<Object, Object> balance = actRedisDao.hGetAll(redisGroup, makeKey(attr, String.format(USER_AWARD_BALANCE, uid)));
        for (String gift : allGiftCode) {
            if (newCollectAmount == -1) {
                newCollectAmount = Convert.toLong(balance.get(gift));
            } else {
                newCollectAmount = Math.min(newCollectAmount, Convert.toLong(balance.get(gift)));
            }
        }

        //每一次抽奖最多只能集齐多1套
        if (newCollectAmount > oldCollectAmount) {
            log.info("dealCollectAllAward,uid:{},newCollectAmount:{},oldCollectAmount:{}", uid, newCollectAmount, oldCollectAmount);
            //更新总集齐数量
            actRedisDao.hset(redisGroup, collectAmountKey, uid + "", newCollectAmount + "");


            //发奖 2天新年进场秀、最多14天
            hdztAwardServiceClient.doBatchWelfare(UUID.randomUUID().toString(), uid, attr.getCollectAllAward(), DateUtil.getNowYyyyMMddHHmmss(), 3, Maps.newHashMap());

            //子频道广播
            GameecologyActivity.BannerBroadcast.Builder banner = GameecologyActivity.BannerBroadcast.newBuilder()
                    .setActId(attr.getActId())
                    .setBannerId(PBCommonBannerId.COLLECT_AWARD)
                    .setBannerType(0)
                    .setUserNick(commonService.getNickName(uid, true))
                    .setJsonData("");
            GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                    .setBannerBroadcast(banner).build();
            svcSDKService.broadcastSub(sid, ssid, msg);


            //底部广播
            if (StringUtil.isNotBlank(attr.getCollectAllTips())) {
                GameecologyActivity.Act202010_MatchResultBroadcast.Builder resultBroadcast = GameecologyActivity.Act202010_MatchResultBroadcast.newBuilder();
                resultBroadcast.setActId(attr.getActId());
                String nick = commonService.getNickName(uid, false);
                if (nick == null) {
                    nick = "";
                }
                String name = nick.replace("<", "&lt;").replace(">", "&gt;");
                resultBroadcast.setContent(String.format(attr.getCollectAllTips(), name));
                resultBroadcast.setMatchType(1);
                resultBroadcast.setModuleType(1);
                resultBroadcast.setAsid(0);
                resultBroadcast.setSkipFlag(0);
                resultBroadcast.setExtjson("");


                GameecologyActivity.GameEcologyMsg noticeMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                        .setUri(GameecologyActivity.PacketType.kAct202010_MatchResultBroadcast_VALUE)
                        .setAct202010MatchResultBroadcast(resultBroadcast.build()).build();
                svcSDKService.broadcastSub(sid, ssid, noticeMsg);
                log.info("dealCollectAllAward,bro,uid:{},newCollectAmount:{},oldCollectAmount:{},content:{}", uid, newCollectAmount, oldCollectAmount, resultBroadcast.getContent());
            }


        }
    }

    /**
     * 读取用户卡余额说
     */
    public Map<Object, Object> queryUserBalance(long actId, long index, long uid) {
        WishingBottleComponentAttr attr = getComponentAttr(actId, index);
        String userAwardBalanceKey = makeKey(attr, String.format(USER_AWARD_BALANCE, uid));
        return actRedisDao.hGetAll(getRedisGroupCode(actId), userAwardBalanceKey);
    }

    /**
     * 本日有多少人获得瓜分奖池资格
     */
    public long queryCarveAmount(long actId, long index, String dateCode) {
        WishingBottleComponentAttr attr = getComponentAttr(actId, index);
        String key = makeKey(attr, CAN_CARVE_ANCHOR_AMOUNT);
        String amount = actRedisDao.hget(getRedisGroupCode(actId), key, dateCode);
        return Convert.toLong(amount, 0);

    }

    /**
     * 增加用户新年祝福余额
     */
    private void addUserAwardBalance(WishingBottleComponentAttr attr, String redisGroup, long uid, String awardGiftCode, long addBalance) {
        String userAwardBalanceKey = makeKey(attr, String.format(USER_AWARD_BALANCE, uid));
        //增加某个祝福余额
        long afterAdd = actRedisDao.hIncrByKey(redisGroup, userAwardBalanceKey, awardGiftCode, addBalance);
        log.info("addUserAwardBalance,uid:{},userAwardBalanceKey:{},awardGiftCode:{},addBalance:{},afterAdd:{}"
                , uid, userAwardBalanceKey, awardGiftCode, addBalance, afterAdd);
    }

    private String getTop1Nick(List<Rank> ranks) {
        if (CollectionUtils.isEmpty(ranks)) {
            return "";
        }
        long uid = Convert.toLong(ranks.get(0).getMember());
        return commonService.getNickName(uid, true);
    }

    /**
     * 上报许愿瓶次数，更新许愿瓶榜单
     */
    private void updateBoxRank(TaskProgressChanged event, long score, WishingBottleComponentAttr attr) {
        String seq = UUID.randomUUID().toString();
        log.info("updateBoxRank,seq:{},memberId:{},score:{}", seq, event.getMember(), score);
        Date timestamp = DateUtil.getDate(event.getOccurTime());
        updateRank(seq, timestamp.getTime(), attr.getUpdateRankingItemId(), event.getMember(), score, attr);
    }

    /**
     * 更细瓜分资格榜单
     */
    private void updateCarveRank(String seq, long timestamp, String memberId, long score, WishingBottleComponentAttr attr) {
        updateRank(seq, timestamp, attr.getCarveUpdateRankingItem(), memberId, score, attr);
    }

    private void updateRank(String seq, long timestamp, String item, String memberId, long score, WishingBottleComponentAttr attr) {
        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), attr.getBusiId(), RoleType.ANCHOR.getValue(), memberId);
        //一定程度上防止报名信息未入库的时序问题
        if (enrollmentInfo == null) {
            SysEvHelper.waiting(1000);
            enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), attr.getBusiId(), RoleType.ANCHOR.getValue(), memberId);
        }
        if (enrollmentInfo == null) {
            log.error("updateRank error,item:{},enrollmentInfo not found,memberId:{},score:{},attr:{}", item, memberId, score, JSON.toJSONString(attr));
            return;
        }

        //中台累榜增加相应榜单积分
        Map<Long, String> actors = Maps.newHashMap();
        actors.put(enrollmentInfo.getDestRoleId(), memberId);
        RankDataEvent rankDataEvent = new RankDataEvent();
        rankDataEvent.setBusiId(attr.getBusiId());
        rankDataEvent.setActId(attr.getActId());
        rankDataEvent.setSeq(seq);
        rankDataEvent.setActors(actors);
        rankDataEvent.setItemId(item);
        rankDataEvent.setCount(1);
        rankDataEvent.setScore(score);
        rankDataEvent.setTimestamp(timestamp);
        kafkaService.updateRanking(rankDataEvent);
    }

    /**
     * 许愿瓶有效标记key
     */
    private String makeBoxInfoKey(WishingBottleComponentAttr attr, long sid, long ssid, String boxId) {
        return makeKey(attr, String.format(BOX_INFO_KEY, sid, ssid, boxId));
    }

    private String makeRareAwardUserKey(WishingBottleComponentAttr attr) {
        Date now = commonService.getNow(attr.getActId());
        String dayCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        return makeKey(attr, String.format(RARE_AWARD_USER_KEY, dayCode));
    }

    /**
     * 集卡祝福礼物编码
     */
    private Set<String> getAllGiftCode(WishingBottleComponentAttr attr) {
        Set<String> giftCode = Sets.newHashSet();
        giftCode.add(attr.getRareAwardGiftCode());
        giftCode.addAll(attr.getPackageIdGiftCodeMap().values());
        return giftCode;
    }
}
