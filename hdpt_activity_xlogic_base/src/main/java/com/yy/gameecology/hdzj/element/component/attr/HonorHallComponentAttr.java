package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.bean.HonorConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021.09.22 10:06
 * 阶段结束,从榜单rankId的第N位startIndex开始获取count个,排名从rankIndex开始,记录在redisKey中
 */
public class HonorHallComponentAttr extends ComponentAttr {

    /**
     * 分组定义 保存到act_result_group表
     **/
    @ComponentAttrField(labelText = "分组定义", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = GroupDefine.class)
    })
    private List<GroupDefine> groupDefines;

    /**
     * 一个荣耀榜涉及的榜单ID和阶段ID,用于过滤事件  {rankId_phaseId:groupId}
     */
    @ComponentAttrField(labelText = "榜单阶段配置", subFields = {
            @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "榜单ID_阶段ID", remark = "rankId_phaseId"),
            @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "分组id")
    })
    private Map<String, String> rankGroupMap;

    /**
     * 具体的配置  {grouId:[{config1},{config2}]
     */
    @ComponentAttrField(labelText = "详细配置", subFields = {
            @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "分组id"),
            @SubField(fieldName = Constant.VALUE, type = List.class, skip = true),
            @SubField(fieldName = Constant.MAP_LIST_VALUE, type = HonorConfig.class)
    })
    private Map<String, List<HonorConfig>> configsMap;

    /**
     * 结果设置 数据保存到act_result表
     **/
    @ComponentAttrField(labelText = "结果定义", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = ActResult.class)
    })
    private List<ActResult> actResults;


    public Map<String, String> getRankGroupMap() {
        return rankGroupMap;
    }

    public void setRankGroupMap(Map<String, String> rankGroupMap) {
        this.rankGroupMap = rankGroupMap;
    }

    public Map<String, List<HonorConfig>> getConfigsMap() {
        return configsMap;
    }

    public void setConfigsMap(Map<String, List<HonorConfig>> configsMap) {
        this.configsMap = configsMap;
    }

    public List<GroupDefine> getGroupDefines() {
        return groupDefines;
    }

    public void setGroupDefines(List<GroupDefine> groupDefines) {
        this.groupDefines = groupDefines;
    }

    public List<ActResult> getActResults() {
        return actResults;
    }

    public void setActResults(List<ActResult> actResults) {
        this.actResults = actResults;
    }

    @Data
    public static class GroupDefine {
        @ComponentAttrField(labelText = "分组id")
        private String group_id;
        @ComponentAttrField(labelText = "分组名称")
        private String group_name;
        @ComponentAttrField(labelText = "业务类型", remark = "1-交友(默认) 2-约战 3-宝贝 4-互动全品类 5-陪玩 6-技能卡 10001-挂件top n定制展示")
        private Integer type;
    }

    public static class ActResult {
        @ComponentAttrField(labelText = "分组id")
        private String group_id;
        @ComponentAttrField(labelText = "排名")
        private Integer rank;
        @ComponentAttrField(labelText = "角色类型", remark = "100用户,200主播，400公会,401厅,700家族")
        private Integer roleType;
        @ComponentAttrField(labelText = "显示顺序")
        private Integer show_order;
        @ComponentAttrField(labelText = "称号")
        private String title;
        @ComponentAttrField(labelText = "业务类型", remark = "1-交友(默认) 2-约战 3-宝贝 4-互动全品类 5-陪玩 6-技能卡 10001-挂件top n定制展示")
        private Integer type;
        @ComponentAttrField(labelText = "扩展数据")
        private String extData;
    }
}
