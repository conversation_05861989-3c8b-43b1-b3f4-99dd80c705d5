package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021/6/28
 */
@Getter
@Setter
public class RankChannelLabelComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "频道角色列表", remark = "多个用逗号分隔,后端去重,后端基于该角色从榜单分值改变事件中获取对应频道的id"
            , subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Set<Long> channelRoleIds = Collections.emptySet();
    /**
     * 监听的榜单
     */
    @ComponentAttrField(labelText = "榜单id", remark = "！！！需要注意！！！由于活动新增了免费票和粉丝票,该榜单只能统计付费活动礼物,而不能统计神豪榜.用于监听该榜单的分值改变事件,该榜单需要增加send_ranking_score_changed_event属性")
    private long rankId;
    /**
     * 监听的阶段
     */
    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;

    /**
     * 数量对应的标签(int 需要和前端约定)
     */
    @ComponentAttrField(labelText = "数量对应的标签",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "数量", remark = "对应的数值只是一个阈值,向下接近该值的数量属于同一个级别"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "标签", remark = "这里使用的是数值枚举,前端根据枚举展示成对应文案")
            })
    private Map<Integer, Integer> labelMap;

    @ComponentAttrField(labelText = "标签key", remark = "透传给前端的数据，需要和前端协商,添加到榜单的ext属性中,该值作为其中的一个key")
    private String labelConfig = "userLabel";

    @ComponentAttrField(labelText = "标签可见的榜单阈值", remark = "榜单超过该值才会展示标签")
    private long labelViewThreshold;

    @ComponentAttrField(labelText = "榜单阈值类型", remark = "false:榜单达到阀值，true：每个频道都要达到阀值,当打开时,需要在频道消费达到榜单阈值才算是有效的频道,否则只需要有消费过就算有效频道")
    private boolean threshold4EachCh;
}
