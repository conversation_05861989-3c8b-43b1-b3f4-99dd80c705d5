package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.TimeKeySource;
import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-23 15:37
 **/
@Data
public class CpInfoComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "cp 榜单id")
    private long rankId;

    @ComponentAttrField(labelText = "cp 阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "保存cp数据时间分榜", dropDownSourceBeanClass = TimeKeySource.class)
    protected long timeKey;

    @ComponentAttrField(labelText = "榜单分数显示分榜", dropDownSourceBeanClass = TimeKeySource.class)
    protected long rankShowScoreTimeKey;
}
