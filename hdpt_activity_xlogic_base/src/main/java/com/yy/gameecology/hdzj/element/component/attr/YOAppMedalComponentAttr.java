package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.bean.YOAppMedalComponentMedaInfo;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class YOAppMedalComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务", dropDownSourceBeanClass = BizSource.class)
    private int busiId;

    @ComponentAttrField(labelText = "礼物ID", remark = "多个使用英文逗号隔开", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class, labelText = "礼物ID")
    })
    private List<String> giftIds;

    @ComponentAttrField(labelText = "高级勋章APP", remark = "多个使用英文逗号隔开", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class, labelText = "APP标识")
    })
    private Set<String> apps;

    @ComponentAttrField(labelText = "活动礼物图标", subFields = {
            @SubField(labelText = "活动礼物id", type = String.class, fieldName = Constant.KEY1, remark = "活动礼物id"),
            @SubField(labelText = "活动礼物图标", type = String.class, fieldName = Constant.VALUE, remark = "活动礼物图标", propType = ComponentAttrCollector.PropType.IMAGE)
    })
    private Map<String, String> giftIcons = Maps.newHashMap();

    @ComponentAttrField(labelText = "勋章信息", subFields = {
            @SubField(labelText = "勋章packageId", type = Long.class, fieldName = Constant.KEY1, remark = "勋章packageId"),
            @SubField(labelText = "勋章信息配置", type = YOAppMedalComponentMedaInfo.class, fieldName = Constant.VALUE)
    })
    private Map<Long, YOAppMedalComponentMedaInfo> medalInfos = Maps.newHashMap();

    @ComponentAttrField(labelText = "勋章奖池id")
    private long medalTaskId;

    @ComponentAttrField(labelText = "一级勋章packageId")
    private long primaryPackageId;

    @ComponentAttrField(labelText = "二级勋章packageId")
    private long seniorPackageId;

    @ComponentAttrField(labelText = "三级勋章packageId")
    private long expertPackageId;

    @ComponentAttrField(labelText = "三级勋章送礼门槛", remark = "点亮三级勋章需要送礼门槛金额，单位厘")
    private long threshold;

    @ComponentAttrField(labelText = "显示app名称")
    private String appName;

}
