package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-11-27 15:07
 **/
@Data
public class ContributeRankPhaseTopConfig {
    @ComponentAttrField(labelText = "贡献榜榜单Id")
    private long rankId;

    @ComponentAttrField(labelText = "贡献榜阶段Id")
    private long phaseId;


    @ComponentAttrField(labelText = "奖励TOP N")
    private int topN;
}
