package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.hdzj.annotation.SkipCheck;
import com.yy.gameecology.hdzj.bean.TaskBroadcastConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvagConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;


@SkipCheck
@Data
public class RankingTaskBannerComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "广播下发端", remark = "1-app 2-pc 3-app和pc")
    private int broPlatform=1;

    @ComponentAttrField(labelText = "榜单id列表", remark = "多个时逗号分隔,后端去重",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Set<Long> rankIds = Sets.newHashSet();

    //@ComponentAttrField(labelText = "榜单id",remark = "后期可废弃")
    private long rankId;

    @ComponentAttrField(labelText = "阶段ID", remark = "限定监听榜单所在阶段，可配置0或不配置，则不限制阶段")
    private long phaseId = 0;


    @ComponentAttrField(labelText = "成员下标", remark = "榜单成员有可能是用 | 分隔的组合值如CP榜，用来指示取哪个组合部分，默认为0（取第一个）")
    private int receiverInx = 0;

    @ComponentAttrField(labelText = "角色类型ID", remark = "累榜参与制角色类型", dropDownSourceBeanClass = RoleTypeSource.class)
    private int roleId;

    @ComponentAttrField(labelText = "子频道角色ID，注意！！！广播范围有子频道或者需要黑名单控制时必填",remark = "多个逗号隔开，用于控制子频道广播时，从累榜actor上下文提取角色",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private List<Long> subChannelRoleId = Lists.newArrayList();

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    private int busiId;

    @ComponentAttrField(labelText = "过滤弹幕游戏频道", remark = "是否过滤弹幕游戏频道，1-是，0-否")
    protected int excludeDanmaku = 0;

    @ComponentAttrField(labelText = "特效类型", dropDownSourceBeanClass = EffectsTypeSrouce.class)
    private int effectsType=1;

    @ComponentAttrField(labelText = "APP等级横幅配置",
            subFields = {@SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "等级"),
                    @SubField(fieldName = Constant.VALUE, type = BannerConfig.class, labelText = "横幅配置")})
    private Map<Long, BannerConfig> levelBanner;

    @ComponentAttrField(labelText = "APP横幅svga配置",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "svgaConfigCode"),
                    @SubField(fieldName = Constant.VALUE, type = BannerSvagConfig.class, labelText = "svga配置")})
    private Map<String, BannerSvagConfig> bannerSvag;

    @ComponentAttrField(labelText = "APPsvgaText文案配置",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "svga文案配置编码"),
                    @SubField(fieldName = Constant.VALUE, type = BannerSvgaTextConfig.class, labelText = "svga文案配置")})
    private Map<String, BannerSvgaTextConfig> svgaText;


    @ComponentAttrField(labelText = "APPtext动态文案", remark = "可用于替换svgaText文案配置-富文本消息 中的占位符",
            subFields = {@SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级"),
                    @SubField(fieldName = Constant.KEY2, type = String.class, labelText = "文案中的占位符"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "占位符对应的值")})
    private Map<Long, Map<String, String>> levelTextDynamicValue;

    @ComponentAttrField(labelText = "APPsvga图片key",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "svgaConfigCode"),
                    @SubField(fieldName = Constant.KEY2, type = String.class, labelText = "imgkey"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "图片地址")})
    private Map<String, Map<String, String>> svgaImgLayers;


    @ComponentAttrField(labelText = "APPsvga动态图片", remark = "可用于替换svga图片key中的占位符",
            subFields = {@SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级"),
                    @SubField(fieldName = Constant.KEY2, type = String.class, labelText = "文案中的占位符"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "占位符对应的图片地址", propType = ComponentAttrCollector.PropType.RESOURCE)})
    private Map<Long, Map<String, String>> levelImgDynamicValue;

    /**
     * 等级广播范围
     **/
    @ComponentAttrField(labelText = "APP等级对应广播范围", remark = "可不填,默认子频道广播, 1-子频道,2-顶频道, 3-全业务 4-全平台", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级"),
            @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "广播类型,可不填,默认子频道广播, 1-子频道,2-顶频道, 3-全业务 4-全平台")})
    private Map<Long, Integer> level2BroType;

    /**
     * 需要推送的业务类型 1 -语音房 2 -交友房 4 -其他 8 -宝贝 位域表示 支持组合：交友and宝贝：2+8
     */
    @ComponentAttrField(labelText = "等级推送的业务",
            subFields = {@SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "等级"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "业务", remark = "1 -语音房 2 -交友房 4 -其他 8 -宝贝 位域表示 支持组合：交友and宝贝：2+8")})
    private Map<Long, Integer> level2business;

    @ComponentAttrField(labelText = "黑名单组件序号", remark = "默认0,不开启黑名单")
    private long blackListCmptUseIndex = 0;

    @ComponentAttrField(labelText = "一次送礼多级任务，pc只广播最高分值阶梯")
    private boolean selectHighestLevel = true;

    @ComponentAttrField(labelText = "PC默认横幅广播配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "等级"),
                    @SubField(fieldName = Constant.VALUE, type = List.class, skip = true),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = TaskBroadcastConfig.class)
            })
    private TreeMap<Long, List<TaskBroadcastConfig>> defaultScoreBannerConfig = Maps.newTreeMap();

    /**
     * 根据角色id配置横幅广播配置 第一次key，角色id,多个的时候用逗号隔开 第二key需要达到的分值 value广播横幅配置
     */
    @ComponentAttrField(labelText = "PC角色id配置横幅广播",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "角色id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "等级"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = TaskBroadcastConfig.class),
                    @SubField(fieldName = Constant.VALUE, type = List.class, skip = true)
            })
    private TreeMap<String, TreeMap<Long, List<TaskBroadcastConfig>>> roleScoreBannerConfig = Maps.newTreeMap();


    /**
     * rankIds优先判断，后续废除rankId
     */
    public boolean isMyDuty(long rankId, long phaseId) {

        return rankIds.contains(rankId) && (this.phaseId == 0 || this.phaseId == phaseId);
    }

}
