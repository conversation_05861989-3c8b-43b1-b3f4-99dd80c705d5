package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.RoleTypeSource;
import com.yy.gameecology.hdzj.element.attrconfig.TimeKeySource;
import lombok.Data;

@Data
public class CommTaskQueryComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private int busiId;

    @ComponentAttrField(labelText = "角色类型", dropDownSourceBeanClass = RoleTypeSource.class)
    private int roleType;

    @ComponentAttrField(labelText = "榜单ID")
    private long rankId;

    @ComponentAttrField(labelText = "阶段ID")
    private long phaseId;

    @ComponentAttrField(labelText = "时间分榜", dropDownSourceBeanClass = TimeKeySource.class)
    private long timeKey;

    @ComponentAttrField(labelText = "起始任务ID", remark = "不配置则默认从1开始（包含）")
    private long startTaskId;

    @ComponentAttrField(labelText = "结束任务ID", remark = "不配置则默认到最高ID（包含）")
    private long endTaskId;
}
