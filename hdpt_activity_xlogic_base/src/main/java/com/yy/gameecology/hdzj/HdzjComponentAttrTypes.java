package com.yy.gameecology.hdzj;

import com.yy.gameecology.hdzj.element.ActComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class HdzjComponentAttrTypes implements BeanPostProcessor {

    private Map<Long, Class<? extends ComponentAttr>> componentAttrTypes = new ConcurrentHashMap<>(512);

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        Class<?> targetClass = AopProxyUtils.ultimateTargetClass(bean);
        if (ActComponent.class.isAssignableFrom(targetClass)) {
            ActComponent<?> actComponent = (ActComponent<?>) bean;
            componentAttrTypes.put(actComponent.getComponentId(), actComponent.getMyAttrClass());
        }

        return bean;
    }

    public Class<? extends ComponentAttr> getComponentAttrType(Long componentId) {
        return componentAttrTypes.get(componentId);
    }
}
