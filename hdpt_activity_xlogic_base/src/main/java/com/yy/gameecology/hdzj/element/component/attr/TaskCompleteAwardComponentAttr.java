package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.activity.bean.acttask.TaskAwardConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.List;



@Data
public class TaskCompleteAwardComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "过任务的榜单")
    private long taskRankId;

    @ComponentAttrField(labelText = "过任务的阶段")
    private long taskPhaseId;

    @ComponentAttrField(labelText = "是否添加到已完成的任务列表")
    private boolean addFinishTaskList=false;

    @ComponentAttrField(labelText = "完成任务是否显示红点")
    private boolean showRedDot=false;

    @ComponentAttrField(labelText = "模块标识",placeholder = "跟前端协商")
    private String moduleName ;

    @ComponentAttrField(labelText = "过任务奖励配置" ,
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = TaskAwardConfig.class)})
    List<TaskAwardConfig> award;


}
