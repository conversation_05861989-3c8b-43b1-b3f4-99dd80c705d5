package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AppPopupConfig;
import lombok.Data;

import java.util.Map;
import java.util.Set;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-07 16:04
 **/
@Data
public class WowTaskComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "参与活动APP")
    private String app;

    @ComponentAttrField(labelText = "海度白名单组件索引")
    private long oriWhiteListCmptIndex;

    @ComponentAttrField(labelText = "登陆白名单组件索引")
    private long loginUserWhiteListCmptIndex;

    @ComponentAttrField(labelText = "奖池限额组件索引id")
    private long poolLimitCmptIndex;



    @ComponentAttrField(labelText = "发奖限额id")
    private long coinLimitId;


    @ComponentAttrField(labelText = "日任务通用组件索引")
    private long dayTaskCmptIndex;

    @ComponentAttrField(labelText = "任务进度显示取数据配置", remark = "依赖外部追玩任务系统",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "任务天数"),
                    @SubField(fieldName = Constant.KEY2, type = String.class, labelText = "任务item"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "追玩任务id"),
            })
    private Map<Integer, Map<String, Long>> taskProcessShow = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "签到使用模拟时间", remark = "加这个开关的目的是测试签到事件真实事件有无用")
    private boolean signTaskUseTestTime = false;

    @ComponentAttrField(labelText = "签到任务跳转url")
    private String signTaskJumpUrl;

    @ComponentAttrField(labelText = "发帖任务跳转url")
    private String postJumpUrl;

    @ComponentAttrField(labelText = "新版app发帖任务跳转url",remark = "zhuiwan://community/publish/topicId/topicName")
    private String newAppPostJumpUrl;

    @ComponentAttrField(labelText = "新版app浏览广场任务跳转url",remark = "zhuiwan://community/topicDetailTask/topicId/taskId/taskType")
    private String newAppBrowseJumpUrl;

    @ComponentAttrField(labelText = "发帖任务话题id")
    private Integer postTopicId = 0;

    @ComponentAttrField(labelText = "进入频道任务补量库id")
    private long directRecommendLibrary;

    @ComponentAttrField(labelText = "进入频道任务默认跳转顶级频道")
    private long directRecommendDefaultJumpSid;

    @ComponentAttrField(labelText = "进入频道任务默认跳转子频道频道")
    private long directRecommendDefaultJumpSsId;

    @ComponentAttrField(labelText = "中间货币", remark = "用于暂存币余额")
    private String taskAwardCId;

    @ComponentAttrField(labelText = "实际金币奖池id")
    private long coinAwardPoolTaskId;

    @ComponentAttrField(labelText = "金币提取配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "金币数量"),
                    @SubField(fieldName = Constant.VALUE, type = AwardShopItemConfig.class, labelText = "商城兑换配置")
            })
    private Map<Long, AwardShopItemConfig> shopItem = Maps.newLinkedHashMap();


    @ComponentAttrField(labelText = "激活时间当天0点的追玩任务id", remark = "多个时逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Set<Long> activeTimeDayFirstZhuiyaTaskId = Sets.newHashSet();

    @ComponentAttrField(labelText = "每次激活不重置的追玩任务id", remark = "多个时逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Set<Long> notResetZhuiyaTaskId = Sets.newHashSet();

    @ComponentAttrField(labelText = "超过N天未打卡气泡提醒", remark = "开区间 >")
    private int signTaskTipsDay;

    @ComponentAttrField(labelText = "最后1个任务天数")
    private long lastTaskDayIndex;

    @ComponentAttrField(labelText = "未打开提醒气泡文案", remark = "$leftDay 还差X天 $award 奖励")
    private String signTaskTips;

    @ComponentAttrField(labelText = "奖励顶部提醒title")
    private String awardPopUpTitle;

    @ComponentAttrField(labelText = "奖励app顶部提醒",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "完成任务天数，0代表老用户登录任务"),
                    @SubField(fieldName = Constant.VALUE, type = AppPopupConfig.class, labelText = "顶部提醒配置")
            })
    private Map<Integer, AppPopupConfig> awardNotice = Maps.newLinkedHashMap();



    @ComponentAttrField(labelText = "风控策略key")
    private String riskStrategyKey = "";


    @Data
    public static class AwardShopItemConfig {
        @ComponentAttrField(labelText = "商城奖项id")
        protected String shopItem;

        @ComponentAttrField(labelText = "奖励奖包Id")
        protected Long packageId;
    }
}
