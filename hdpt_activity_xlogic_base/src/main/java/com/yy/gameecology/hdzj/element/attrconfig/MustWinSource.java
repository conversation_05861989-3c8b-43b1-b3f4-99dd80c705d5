package com.yy.gameecology.hdzj.element.attrconfig;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/25 17:26
 **/
@Component
public class MustWinSource implements DropDownSource {
    @Override
    public List<DropDownVo> listDropDown() {
        // 1= （日）送礼顺序排名，2= （活动）送礼顺序排名，3=榜单排名 ，其他不开启必中
        return Arrays.asList(
                new DropDownVo("0", "不开启必中(0)"),
                new DropDownVo("1", "（日）送礼顺序排名(1)"),
                new DropDownVo("2", "（活动）送礼顺序排名(2)"),
                new DropDownVo("3", "榜单排名(3)")
        );
    }
}
