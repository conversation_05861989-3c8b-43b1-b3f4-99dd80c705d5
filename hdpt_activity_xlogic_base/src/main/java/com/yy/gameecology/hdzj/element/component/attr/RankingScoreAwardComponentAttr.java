package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.HdzjHelper;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * 功能描述:按榜单中的分值做奖励的组件
 *
 * <AUTHOR>
 * @date 2021/4/8 22:27
 */
public class RankingScoreAwardComponentAttr extends ComponentAttr {
    // 每个发放错误尝试的次数，总共最多调用 1 + retry 次， 让发放尽量成功
    @ComponentAttrField(labelText = "重试次数", remark = "每个发放错误尝试的次数,总共最多调用 1 + retry 次,让发放尽量成功,默认值2")
    private int retry = 2;

    //榜单成员有可能是用 | 分隔的组合值，用来指示取哪个组合部分，默认为0（取第一个）
    @ComponentAttrField(labelText = "成员下标", remark = "榜单成员有可能是用 | 分隔的组合值，用来指示取哪个组合部分，默认为0（取第一个）")
    private int receiverInx = 0;

    // 筛选榜单ID，输入榜单ID和此处不同不处理
    @ComponentAttrField(labelText = "榜单ID", remark = "发放奖励的榜单ID，输入榜单ID和此处不同不处理")
    private long rankId = 0;

    // 第一层key：需要达到的分值说明，第一层value：指定分值门槛发放的奖励， 第二层key：taskId，第三层key：奖包ID， 第三层值：奖包ID发放的数量
    @ComponentAttrField(labelText = "奖励配置", remark = "第一层key：需要达到的分值说明，第一层value：指定分值门槛发放的奖励， 第二层key：taskId，第三层key：奖包ID， 第三层值：奖包ID发放的数量",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "分值门槛"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "奖池id"),
                    @SubField(fieldName = Constant.KEY3, type = Long.class, labelText = "奖包id"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "发放的奖包数量")
            })
    TreeMap<Long, Map<Long, Map<Long, Integer>>> scoreAwardConfig = Maps.newTreeMap();

    public int getRetry() {
        return retry;
    }

    public void setRetry(int retry) {
        this.retry = retry;
    }

    public int getReceiverInx() {
        return receiverInx;
    }

    public void setReceiverInx(int receiverInx) {
        this.receiverInx = receiverInx;
    }

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public TreeMap<Long, Map<Long, Map<Long, Integer>>> getScoreAwardConfig() {
        return scoreAwardConfig;
    }

    public void setScoreAwardConfig(TreeMap<Long, Map<Long, Map<Long, Integer>>> scoreAwardConfig) {
        this.scoreAwardConfig = scoreAwardConfig;
    }

    /**
     * 查找 phaseScore 对应的等级分值
     */
    public long findAwardLevelScore(long currScore) {
        List<Long> list = Lists.newArrayList(scoreAwardConfig.keySet());
        return HdzjHelper.findLevelScore(currScore, list);
    }

    /**
     * 提阶梯分值在 (fromScore, toScore] 直接的所有奖励配置
     */
    public Map<Long, Map<Long, Map<Long, Integer>>> getSubScoreAwardConfig(long fromScore, long toScore) {
        Map<Long, Map<Long, Map<Long, Integer>>> map = Maps.newLinkedHashMap();
        for (Long levelScore : scoreAwardConfig.keySet()) {
            if (levelScore > fromScore && levelScore <= toScore) {
                map.put(levelScore, scoreAwardConfig.get(levelScore));
            }
        }
        return map;
    }

    public boolean isMyDuty(long rankId) {
        return rankId < 1 ? false : this.rankId == rankId;
    }
}
