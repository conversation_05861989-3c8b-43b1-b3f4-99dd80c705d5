package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.Map;

@Data
public class PureWhiteListPromoteComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务Id")
    private long busiId;

    @ComponentAttrField(labelText = "角色类型")
    private int roleType;

    @ComponentAttrField(labelText = "默认roleId")
    private long defaultRoleId;

    @ComponentAttrField(labelText = "结算数据来源榜单")
    private long srcRankId;

    @ComponentAttrField(labelText = "结算数据来源阶段")
    private long srcPhaseId;

    @ComponentAttrField(labelText = "用于驱动晋级的礼物Id")
    private String promoteItem;

    @ComponentAttrField(labelText = "晋级白名单"
        , subFields = {
            @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "晋级排名，从1开始"),
            @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "晋级member")
    })
    private Map<Integer, String> whiteList;

    @ComponentAttrField(labelText = "通知目标榜单晋级结算配置"
            , subFields = {
            @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "榜单Id"),
            @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "结算标记tag")})
    private Map<Long, String> promoteNotifySettle = Maps.newHashMap();

    @ComponentAttrField(labelText = "晋级PK分组调整phaseId")
    private long promotePhaseId;

    @ComponentAttrField(labelText = "操作设置pk的人的uid,中台权限控制用")
    private long setPkOpUid;
}
