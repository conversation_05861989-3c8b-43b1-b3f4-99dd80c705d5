package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.common.consts.PBCommonBannerId;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvagConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class CpCityWalkComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "cp榜单id")
    private long cpRankId;

    @ComponentAttrField(labelText = "cp用户对主持贡献榜单id")
    private long cpContributeRankId;

    @ComponentAttrField(labelText = "cp 主持对用户贡献榜单id")
    private long cpAntiContributeRankId;

    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "cp榜单拉取数量")
    private long rankLimit;

    @ComponentAttrField(labelText = "口令")
    private String targetWord;

    @ComponentAttrField(labelText = "城市总数")
    private int totalCity;

    @ComponentAttrField(labelText = "幸运cp城市列表", remark = ",多个逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Integer.class)
    })
    private List<Integer> specialCityNum;

    @ComponentAttrField(labelText = "告白文案")
    private String bannerText;

    @ComponentAttrField(labelText = "外显限额")
    private long priceLimit;

    @ComponentAttrField(labelText = "大奖发放数限额")
    private long jackPointLimit;

    @ComponentAttrField(labelText = "最强cp抽奖包",  subFields = {
            @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "日期(yyyyMMdd)"),
            @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "抽奖taskId")
    })
    private Map<String, Integer> bestCpTaskMap = Maps.newHashMap();

    @ComponentAttrField(labelText = "幸运cp抽奖包",  subFields = {
            @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "日期(yyyyMMdd)"),
            @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "抽奖taskId")
    })
    private Map<String, Integer> luckCpTaskMap = Maps.newHashMap();

    @ComponentAttrField(labelText = "最强cp大奖兜底奖品")
    private long bestCpBackUpPackageId;

    @ComponentAttrField(labelText = "口令抽奖包")
    private int chatTaskId;

    @ComponentAttrField(labelText = "是否需要多昵称")
    private boolean needNickExt;

    @ComponentAttrField(labelText = "cp文本")
    private String bestCpText;

    @ComponentAttrField(labelText = "礼物价格", subFields = {
            @SubField(labelText = "活动礼物id", type = String.class, fieldName = Constant.KEY1, remark = "活动礼物id"),
            @SubField(labelText = "价值", type = Long.class, fieldName = Constant.VALUE, remark = "价值")
    })
    private Map<String, Long> giftValueMap = Maps.newHashMap();

    @ComponentAttrField(labelText = "活动礼物阈值")
    private long threshold;

    @ComponentAttrField(labelText = "礼物元数据",remark = "礼物元数据",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "packageId"),
                    @SubField(fieldName = Constant.VALUE, type = PackageInfo.class, labelText = "礼物信息")
            })
    private Map<Long, PackageInfo> packageMap = Maps.newHashMap();

    @ComponentAttrField(labelText = "城市名表",remark = "城市名表",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "cityIndex"),
                    @SubField(fieldName = Constant.VALUE, type = String.class)
            })
    private Map<Integer, String> cityMap =  Maps.newHashMap();

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    private long busiId = 200;

    @ComponentAttrField(labelText = "完成广播类型", dropDownSourceBeanClass = BroadcastTypeSource.class)
    private int bannerBroType = 2;

    @ComponentAttrField(labelText = "挂件广播类型", dropDownSourceBeanClass = BroadcastTypeSource.class)
    private int layerBroType = 2;

    @ComponentAttrField(labelText = "bannerId")
    private long bannerId = PBCommonBannerId.SUNSHINE_TASK;

    @ComponentAttrField(labelText = "bannerType")
    private long bannerType = 0;


    @ComponentAttrField(labelText = "横幅svga配置",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "svgaConfigCode"),
                    @SubField(fieldName = Constant.VALUE, type = BannerSvagConfig.class, labelText = "svga配置")})
    private Map<String, BannerSvagConfig> bannerSvag;

    @ComponentAttrField(labelText = "svgaText文案配置",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "svga文案配置编码"),
                    @SubField(fieldName = Constant.VALUE, type = BannerSvgaTextConfig.class, labelText = "svga文案配置")})
    private Map<String, BannerSvgaTextConfig> svgaText;

    @ComponentAttrField(labelText = "text动态文案", remark = "可用于替换svgaText文案配置-富文本消息 中的占位符",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "文案中的占位符"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "占位符对应的值")})
    private Map<String, String> textDynamicValue;

    @ComponentAttrField(labelText = "svga图片key",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "imgkey"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "图片地址")})
    private Map<String, String> svgaImgLayers;


    @ComponentAttrField(labelText = "循环次数", remark = "循环播放次数, 0-无限循环(勿填0)")
    private int loops = 1;

    @ComponentAttrField(labelText = "布局", remark = " 横幅类型(contentType)==6使用 可选 动画播放位置（左右充满，垂直对齐类型）0：全屏播放；1：居中对齐播放；2：顶部对齐播放；3：底部对齐播放")
    private int layoutType;

    @ComponentAttrField(labelText = "布局边距", remark = "相对父布局的间距 2个元素的数组，分别对应顶部和底部的间距；对应位置为[top, bottom]。通常top、bottom为0。配置例子：{\"android\":[10,0],\"ios\":[0,0]}")
    private String layoutMargin = "{\"android\":[10,0],\"ios\":[0,0]}";


    @ComponentAttrField(labelText = "宽高比", remark = "必填 宽高比 客户端默认宽为全屏，svga高度根据宽高比计算,填写实例：6:9")
    private String whRatio;

    @ComponentAttrField(labelText = "广播业务ID", remark = "1 -语音房 2 -交友房 4 -其他 8 -宝贝 位域表示 支持组合：交友and宝贝：2+8")
    private int broBusiId;

    @ComponentAttrField(labelText = "mp4广播类型", remark = "1-子频道广播 2-顶级频道下所有子厅广播 3 -全服广播 4 -全平台广播 5 -全家族广播 6 -单播")
    private int bcType;

    @Data
    public static class PackageInfo {
        @ComponentAttrField(labelText = "发奖taskId")
        private int awardTaskId;

        @ComponentAttrField(labelText = "发奖packageId")
        private int awardPackageId;

        @ComponentAttrField(labelText = "奖品名")
        private String awardName;

        @ComponentAttrField(labelText = "奖品图片", propType = ComponentAttrCollector.PropType.IMAGE)
        private String awardPic;

        @ComponentAttrField(labelText = "发放时间")
        private String awardTimeUnit;

        @ComponentAttrField(labelText = "奖品价值")
        private long value;

        @ComponentAttrField(labelText = "是否大奖")
        private int bigAward;
    }

}
