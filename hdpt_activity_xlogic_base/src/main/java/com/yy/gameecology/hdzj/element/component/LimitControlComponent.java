package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.DayTaskComponent2Attr;
import com.yy.gameecology.hdzj.element.component.attr.LimitControlComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardLimitConfig;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-06 11:04
 **/
@RestController
@RequestMapping("1018")
public class LimitControlComponent extends BaseActComponent<LimitControlComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CommonDataDao commonDataDao;

    /**
     * 已发放数量
     * %s-- limitId
     */
    private static final String AWARD_SEND = "award_send:%s";


    private static final String AWARD_SEND_DAY_SEND_STATIC = "award_day_send:static:%s:%s";

    // 登录任务金额
    private static final String AWARD_SEND_DAY_LOGIN_STATIC = "award_day_login:static:%s:%s";

    @Override
    public Long getComponentId() {
        return ComponentId.LIMIT_CONTROL;
    }

    /**
     * 限额配置
     */
    public AwardLimitConfig getLimitConfig(long actId, long cmptIndex, long limitConfigId) {
        var attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return null;
        }
        return attr.getAwardTotalLimit().get(limitConfigId);
    }

    /**
     * 增加指定数量的值，忽略超出限制的情况
     *
     * @param limitConfigId 限制配置ID
     * @param limitSeq      限制序列
     * @param incAmount     要增加的数量
     * @return 增加操作的结果
     */
    public CommonDataDao.ValueIncResult valueIncrIgnoreWithLimit(long actId, long cmptIndex, long limitConfigId, String limitSeq, long incAmount) {
        LimitControlComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            throw new IllegalArgumentException("limit control config not exist");
        }

        AwardLimitConfig limitConfig = attr.getAwardTotalLimit().get(limitConfigId);
        if (limitConfig == null) {
            throw new IllegalArgumentException("limit config not found");
        }

        String sendKey = buildAwardSendKey(limitConfigId);
        long limit = limitConfig.getLimit();
        var valueIncResult = commonDataDao.valueIncrIgnoreWithLimit(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), limitSeq, sendKey, incAmount, limit);

        // 如果超出了限额 并且支持大于0就扣
        if (valueIncResult.isViolateLimit() && limitConfig.isReduceToZero()) {
            long value = commonDataDao.valueGet(actId, attr.getCmptId(), cmptIndex, sendKey);
            long balance = limitConfig.getLimit() - value;
            if (balance > 0) {
                valueIncResult = commonDataDao.valueIncrIgnoreWithLimit(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), limitSeq, sendKey, balance, limit);
            }
        }

        //日消耗统计
        if (!valueIncResult.isViolateLimit()) {
            String dayCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
            String dayKey = buildAwardDaySendStaticKey(dayCode, limitConfigId);
            String daySeq = limitSeq + ":daystatic";
            commonDataDao.valueIncrIgnore(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), daySeq, dayKey, incAmount);
        }

        return valueIncResult;
    }

    /**
     * 登录任务发奖增加金额
     * 增加指定数量的值，忽略超出限制的情况
     *
     * @param limitSeq      限制序列
     * @param incAmount     要增加的数量
     * @return 增加操作的结果
     */
    public void valueIncrIgnoreWithLoginLimit(long actId, long cmptIndex, long limitConfigId, String limitSeq,Date now,long incAmount) {
        LimitControlComponentAttr attr = getComponentAttr(actId, cmptIndex);
        String dayCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String key = buildAwardDayLoginTaskStaticKey(dayCode,limitConfigId);
        commonDataDao.valueIncrIgnore(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), limitSeq, key, incAmount);
    }

    @GetMapping("balance")
    public Response<BalanceResult> queryBalance(@RequestParam(name = "actId") long actId,
                                           @RequestParam(name = "cmptInx") long cmptInx,
                                           @RequestParam(name = "limitId") long limitId) {
        var attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        var limitConfig = attr.getAwardTotalLimit().get(limitId);
        if (limitConfig == null) {
            return Response.fail(400, "limit not exist");
        }

        long balance = queryPoolBalance(actId, cmptInx, limitId);
        return Response.success(new BalanceResult(limitConfig.getLimit(), balance));
    }

    /**
     * 查询当前池的余额
     *
     * @param limitId 限制配置的ID
     * @return 返回池的余额，如果配置不存在或限制小于等于0则返回-1
     */
    public long queryPoolBalance(long actId, long cmptIndex, long limitId) {
        var attr = getComponentAttr(actId, cmptIndex);
        AwardLimitConfig config = attr.getAwardTotalLimit().get(limitId);
        // 检查配置是否存在以及限制是否有效
        if (config == null || config.getLimit() <= 0) {
            return -1;
        }
        String sendKey = buildAwardSendKey(limitId);
        long alreadySend = commonDataDao.valueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), sendKey);
        long left = config.getLimit() - alreadySend;
        if (left <= config.getMinimumAmount()) {
            return 0;
        }
        return left;
    }

    public long getAwardDaySend(DayTaskComponent2Attr attr, Date now, long packageId) {
        String dayCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String dayKey = buildAwardDaySendStaticKey(dayCode, packageId);
        return commonDataDao.valueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), dayKey);
    }

    public long getAwardSendTotal(long actId, long cmptIndex,long limitId) {
        var attr = getComponentAttr(actId, cmptIndex);
        String key = buildAwardSendKey(limitId);
        return commonDataDao.valueGet(actId, attr.getCmptId(), cmptIndex, key);
    }

    public long getAwardDaySendDay(long actId, long cmptIndex, Date now, long limitId) {
        var attr = getComponentAttr(actId, cmptIndex);
        String dayCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String dayKey = buildAwardDaySendStaticKey(dayCode, limitId);
        return commonDataDao.valueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), dayKey);
    }

    public long getAwardDayLoginSend(long actId, long cmptIndex, Date now, long limitId) {
        LimitControlComponentAttr attr = getComponentAttr(actId, cmptIndex);
        String dayCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String key = buildAwardDayLoginTaskStaticKey(dayCode,limitId);
        return commonDataDao.valueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), key);
    }

    private String buildAwardSendKey(long packageId) {
        return String.format(AWARD_SEND, packageId);
    }


    private String buildAwardDaySendStaticKey(String dayCode, long packageId) {
        return String.format(AWARD_SEND_DAY_SEND_STATIC, dayCode, packageId);
    }

    private String buildAwardDayLoginTaskStaticKey(String dayCode, long packageId) {
        return String.format(AWARD_SEND_DAY_LOGIN_STATIC, dayCode, packageId);
    }

    @Getter
    @Setter
    public static class BalanceResult {
        protected long limit;

        protected long balance;

        public BalanceResult() {}

        public BalanceResult(long limit, long balance) {
            this.limit = limit;
            this.balance = balance;
        }
    }
}
