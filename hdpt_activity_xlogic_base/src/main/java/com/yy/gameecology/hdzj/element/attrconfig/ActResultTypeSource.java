package com.yy.gameecology.hdzj.element.attrconfig;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/30 11:39
 **/
@Component
public class ActResultTypeSource implements DropDownSource {
    // [{"code":"1","desc":"交友(1)"},{"code":"2","desc":"约战(2)"},{"code":"3","desc":"宝贝(3)"},{"code":"4","desc":"陪玩(4)"}]
    @Override
    public List<DropDownVo> listDropDown() {
        return Arrays.asList(
                new DropDownVo("1", "交友(1)"),
                new DropDownVo("2", "约战(2)"),
                new DropDownVo("3", "宝贝(3)"),
                new DropDownVo("4", "陪玩(4)")
        );
    }
}
