package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 王者明星赛组件属性
 *
 * <AUTHOR>
 * @since 2023/6/21 15:20
 **/
@Data
public class KingOfStarComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "预约推送的消息发送者", remark = "系统消息:1234567，活动中心:10000，20000:Yo语音小助手")
    private int fromId = 20000;
    @ComponentAttrField(labelText = "预约推送的消息标题")
    private String title;
    @ComponentAttrField(labelText = "预约推送的消息内容")
    private String content;
    @ComponentAttrField(labelText = "预约推送的消息图片")
    private String image;
    @ComponentAttrField(labelText = "预约推送的消息跳转链接")
    private String link;

    @ComponentAttrField(labelText = "赛事列表", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = CompetitionInfo.class, labelText = "赛事列表")
    })
    private List<CompetitionInfo> competitionList;


    public List<CompetitionInfo> listNoStartCompetition() {
        return this.competitionList.stream()
                .filter(competitionInfo -> StringUtil.isNotBlank(competitionInfo.getStartTime()) && DateUtil.getDate(competitionInfo.getStartTime(), DateUtil.DEFAULT_PATTERN).getTime() >= System.currentTimeMillis())
                .collect(Collectors.toList());
    }

    /**
     * 赛事信息
     **/
    @Data
    public static class CompetitionInfo {
        @ComponentAttrField(labelText = "开始时间", remark = "格式：2023-06-21 19:00:00")
        private String startTime;

        @ComponentAttrField(labelText = "结束时间", remark = "格式：2023-06-21 19:00:00")
        private String endTime;

        @ComponentAttrField(labelText = "状态", remark = "2:进行中;1:待开始;0:已结束，如果配置成-1，则用开始和结束时间自动判断状态")
        private int status;

        @ComponentAttrField(labelText = "回放链接", remark = "赛后录入")
        private String playbackLink;

        @ComponentAttrField(labelText = "用户ID")
        private long uid;
        @ComponentAttrField(labelText = "用户昵称")
        private String nickname;
        @ComponentAttrField(labelText = "用户头像")
        private String avatar;
        @ComponentAttrField(labelText = "用户比分")
        private int score;

        @ComponentAttrField(labelText = "对手ID")
        private long targetUid;
        @ComponentAttrField(labelText = "对手昵称")
        private String targetNickname;
        @ComponentAttrField(labelText = "对手头像")
        private String targetAvatar;
        @ComponentAttrField(labelText = "对手比分")
        private int targetScore;

        // 用户的直播将信息
        @ComponentAttrField(labelText = "官频sid")
        private long sid;
        @ComponentAttrField(labelText = "官频ssid")
        private long ssid;
    }
}
