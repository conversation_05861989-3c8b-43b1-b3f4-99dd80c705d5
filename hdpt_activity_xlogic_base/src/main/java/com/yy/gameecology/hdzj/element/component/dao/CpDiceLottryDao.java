package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.CpDiceLotteryFlow;
import com.yy.gameecology.common.db.model.gameecology.CpDiceLotteryInfo;
import com.yy.gameecology.common.db.model.gameecology.CpDiceLotteryRestrictRewardInfo;
import com.yy.gameecology.common.db.model.gameecology.CpDiceLotteryTicket;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2067AsyncWelfare;
import com.yy.gameecology.common.utils.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Repository
public class CpDiceLottryDao {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    GameecologyDao gameecologyDao;

    @Autowired
    AsyncWelfareDao asyncWelfareDao;

    private static final String HASH_INSERT_IGNORE = "insert ignore into act_cp_flow (seq, act_id, cmpt_index, seq_type, uid, anchor_uid, value, create_time) values (?, ?, ?, ?, ?, ?, ?, ?)";

//    private static final String ADD_TICKET_HASH = "insert into cmpt_5152_cp_lottery_ticket (seq, act_id, cmpt_index, value) values (?, ?, ?, ?)";


    private static final String ADD_TICKET_SQL = "INSERT INTO cmpt_5155_cp_lottery_ticket (act_id, cmpt_use_inx,uid,anchor_uid,total_count,balance,create_time) " +
            "VALUES (?, ?, ?, ?, ?, ?, NOW()) " +
            "ON DUPLICATE KEY UPDATE  " +
            "total_count = total_count + VALUES(total_count)," +
            "balance = balance + VALUES(balance) ";

    private static final String GET_CP_TICKET_LIST =
            """
                    SELECT * FROM cmpt_5155_cp_lottery_ticket
                    WHERE act_id = %s
                    AND cmpt_use_inx = %s
                    AND (uid =%s OR anchor_uid = %s);
                    """;

    private static final String ADD_REWARD_STOCK_SQL = "INSERT INTO cmpt_5155_restrict_reward_info (act_id, cmpt_use_inx,reward_package_id, total_count, balance, create_time) " +
            "VALUES (?, ?, ?, ?, ?, NOW()) " +
            "ON DUPLICATE KEY UPDATE  " +
            "total_count = total_count + VALUES(total_count)," +
            "balance = balance + VALUES(balance)";

    private static final String ADD_USER_CLICK_SQL = "INSERT INTO cmpt_5155_lottery_info (act_id, cmpt_use_inx,uid, anchor_uid, step,reward_value, uid_click,anchor_click,create_time) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?,?,NOW()) " +
            "ON DUPLICATE KEY UPDATE  " +
            "uid_click = 1 ";

    private static final String ADD_ANCHOR_CLICK_SQL = "INSERT INTO cmpt_5155_lottery_info (act_id, cmpt_use_inx,uid, anchor_uid, step, reward_value,uid_click,anchor_click,create_time) " +
            "VALUES (?, ?, ?, ?, ?, ?,?, ?,NOW()) " +
            "ON DUPLICATE KEY UPDATE  " +
            "anchor_click = 1 ";

    private static final String GET_CP_LIST =
            """
                    SELECT * FROM cmpt_5155_lottery_info
                    WHERE act_id = %s
                    AND cmpt_use_inx = %s
                    AND (uid = %s OR anchor_uid = %s);
                    """;

    private static final String DECR_BALANCE =
            """
                    UPDATE cmpt_5155_cp_lottery_ticket SET balance = balance - ?
                    WHERE act_id = ?
                    AND cmpt_use_inx = ?
                    AND uid = ? AND  anchor_uid = ? AND balance >= ?
                    """;

    private static final String DECR_REWARD_STOCK_BALANCE =
            """
                    UPDATE cmpt_5155_restrict_reward_info SET balance = balance - ?
                    WHERE act_id = ?
                    AND cmpt_use_inx = ?
                    AND reward_package_id = ? AND  balance >= ?
                    """;

    private static final String UID_INCR_CLICK =
            """
                    UPDATE cmpt_5155_lottery_info SET step = ?,reward_value = ?, uid_click =0,anchor_click=0
                    WHERE act_id = ?
                    AND cmpt_use_inx = ?
                    AND uid = ? AND anchor_uid = ?
                    """;

    private static final String INSERT_FLOW_SQL =
            """
                    insert ignore into cmpt_5155_lottery_flow
                    (seq, act_id, cmpt_use_inx, uid, anchor_uid, dice_point_serial, step_old, step,
                    step_serial,use_draw, ticket_old, ticket,reward_value, create_time)
                    values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """;

    public void addTicket(long actId, long cmptUseInx, long uid, long anchorUid, long totalCount, long balance) {
        gameecologyDao.update(ADD_TICKET_SQL, actId, cmptUseInx, uid, anchorUid, totalCount, balance);
    }

    public void addRewardStock(long actId, long cmptUseInx, long packageId, long totalCount) {
        gameecologyDao.update(ADD_REWARD_STOCK_SQL, actId, cmptUseInx, packageId, totalCount, totalCount);
    }

    String TOTAL_KEY = "total";
    String BALANCE_KEY = "balance";
    String SEQ_ADD_KEY = "update_stock";
    String SEQ_USE_KEY = "user_draw";

    public CpDiceLotteryTicket getTicket(long actId, long cmptIndex, long uid, long anchorUid) {
        CpDiceLotteryTicket where = new CpDiceLotteryTicket();
        where.setActId(actId);
        where.setCmptUseInx((int) cmptIndex);
        where.setUid(uid);
        where.setAnchorUid(anchorUid);
        CpDiceLotteryTicket ticket = gameecologyDao.selectOne(CpDiceLotteryTicket.class, where, "", CpDiceLotteryTicket.TABLE_NAME, false);
        return ticket == null ? new CpDiceLotteryTicket(actId, (int) cmptIndex, uid, anchorUid, 0, 0) : ticket;
    }

    public List<CpDiceLotteryRestrictRewardInfo> getRestrictRewardInfo(long actId, long cmptIndex) {
        CpDiceLotteryRestrictRewardInfo where = new CpDiceLotteryRestrictRewardInfo();
        where.setActId(actId);
        where.setCmptUseInx((int) cmptIndex);
        return gameecologyDao.select(CpDiceLotteryRestrictRewardInfo.class, where, "", false, CpDiceLotteryRestrictRewardInfo.TABLE_NAME);
    }

//    public List<CpDiceLotteryRestrictRewardInfo> getRestrictRewardInfo(long actId, long cmptIndex) {
//        return getRestrictRewardInfo(actId, cmptIndex);
//    }

//    public List<CpDiceLotteryRestrictRewardInfo> getRestrictRewardBalance(long actId, long cmptIndex) {
//        return getRestrictRewardInfo(actId, cmptIndex);
//    }

    public List<CpDiceLotteryTicket> listCpLotteryTicketInfo(long actId, long cmptUseInx, long uid) {
        return gameecologyDao.getJdbcTemplate().query(String.format(GET_CP_TICKET_LIST, actId, cmptUseInx, uid, uid), CpDiceLotteryTicket.ROW_MAPPER);
    }


    @Transactional(rollbackFor = Exception.class)
    public void UpdateUserTicketAndSvStock(long actId, long cmptIndex, long uid, long anchorUid, String seq, CpLotteryTicketInfo writeInfo) {
        boolean addResult = InsertSeq(actId, cmptIndex, uid, anchorUid, seq, SEQ_ADD_KEY, JsonUtil.toJson(writeInfo));
        if (!addResult) {
            log.warn("add ticket already add? actId:{},cmptIndex:{},seq:{},uid:{},anchorUid:{},info{}", actId, cmptIndex, seq, uid,
                    anchorUid, JsonUtil.toJson(writeInfo));
            return;
        }
        addTicket(actId, cmptIndex, uid, anchorUid, writeInfo.getAddTicket(), writeInfo.getAddTicket());
        addTicket(actId, cmptIndex, 0, 0, writeInfo.getAddTicket(), writeInfo.getAddTicket());
        for (Map.Entry<Long, Long> entry : writeInfo.getStockMap().entrySet()) {
            if (entry.getValue() > 0) {
                addRewardStock(actId, cmptIndex, entry.getKey(), entry.getValue());
            }
        }
    }

    private boolean InsertSeq(long actId, long cmptIndex, long uid, long anchorUid, String seq, String seqType, String value) {
        return gameecologyDao.getJdbcTemplate().update(HASH_INSERT_IGNORE, seq, actId, cmptIndex, seqType, uid, anchorUid, value, new Date()) >= 1;
    }

    public CpDiceLotteryInfo getDrawInfo(long actId, long cmptIndex, long uid, long anchorUid) {
        CpDiceLotteryInfo where = new CpDiceLotteryInfo();
        where.setActId(actId);
        where.setCmptUseInx((int) cmptIndex);
        where.setUid(uid);
        where.setAnchorUid(anchorUid);
        CpDiceLotteryInfo info = gameecologyDao.selectOne(CpDiceLotteryInfo.class, where, "", CpDiceLotteryInfo.TABLE_NAME, false);
        return info == null ? new CpDiceLotteryInfo(actId, (int) cmptIndex, uid, anchorUid) : info;
    }

    public void userClick(long actId, long cmptIndex, long uid, long anchorUid) {
        gameecologyDao.update(ADD_USER_CLICK_SQL, actId, cmptIndex, uid, anchorUid, 0, 0, 1, 0);
    }

    public void anchorClick(long actId, long cmptIndex, long uid, long anchorUid) {
        gameecologyDao.update(ADD_ANCHOR_CLICK_SQL, actId, cmptIndex, uid, anchorUid, 0, 0, 0, 1);
    }


    public List<CpDiceLotteryInfo> listCpLotteryInfo(long actId, long cmptUseInx, long uid) {
        return gameecologyDao.getJdbcTemplate().query(String.format(GET_CP_LIST, actId, cmptUseInx, uid, uid), CpDiceLotteryInfo.ROW_MAPPER);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean userDraw(long actId, long cmptIndex, long uid, long anchorUid, String seq, CpLotteryUserTicketInfo writeInfo, List<Cmpt2067AsyncWelfare> l,
                            CpDiceLotteryFlow flows) {
        long startTime = System.nanoTime();
        log.info("userDraw actId:{} cmptIndex:{} writeInfo:{} l:{} flow:{}", actId, cmptIndex, writeInfo, l, flows);
        boolean addPuzzleResult = InsertSeq(actId, cmptIndex, uid, anchorUid, seq, SEQ_USE_KEY, JsonUtil.toJson(writeInfo));
        if (!addPuzzleResult) {
            log.warn("add puzzle already add? actId:{},cmptIndex:{},seq:{},uid:{},anchorUid:{}", actId, cmptIndex, seq, uid,
                    anchorUid);
            return false;
        }
        int update = deductUserBalance(actId, cmptIndex, uid, anchorUid, writeInfo.getUseTicket());
        if (update <= 0) {
            throw new RuntimeException("update user balance error");
        }
        for (Map.Entry<Long, Long> entry : writeInfo.getDeductMap().entrySet()) {
            if (entry.getKey() > 0 && entry.getValue() > 0) {
                update = deductRewardBalance(actId, cmptIndex, entry.getKey(), entry.getValue());
                if (update <= 0) {
                    throw new RuntimeException("update reward balance error " + entry.getKey());
                }
            }
        }
        long duration = System.nanoTime() - startTime;
        log.info("use draw duration： {} {}", duration, duration / 1_000_000.0);
        usreClick(actId, cmptIndex, uid, anchorUid, writeInfo);
        asyncWelfareDao.addWelfareRecords(l);

        duration = System.nanoTime() - startTime;
        addDrawEventFlow(flows);
        log.info("use draw duration： {} {}", duration, duration / 1_000_000.0);
        return true;
    }

    private int deductUserBalance(long actId, long cmptIndex, long uid, long anchorUid, long count) {
        return gameecologyDao.getJdbcTemplate().update(DECR_BALANCE, count, actId, cmptIndex, uid, anchorUid, count);
    }

    private int deductRewardBalance(long actId, long cmptIndex, long packageId, long count) {
        return gameecologyDao.getJdbcTemplate().update(DECR_REWARD_STOCK_BALANCE, count, actId, cmptIndex, packageId, count);
    }

    private int usreClick(long actId, long cmptIndex, long uid, long anchorUid, CpLotteryUserTicketInfo writeInfo) {
        return gameecologyDao.getJdbcTemplate().update(UID_INCR_CLICK, writeInfo.getStep(), writeInfo.getRewardAmount(), actId, cmptIndex, uid, anchorUid);
    }

    public void addDrawEventFlow(CpDiceLotteryFlow records) {
        gameecologyDao.update(INSERT_FLOW_SQL, records.getSeq(), records.getActId(), records.getCmptUseInx(), records.getUid(), records.getAnchorUid(),
                records.getDicePointSerial(), records.getStepOld(), records.getStep(), records.getStepSerial(), records.getUseDraw(), records.getTicketOld(),
                records.getTicket(), records.getRewardValue(), records.getCreateTime());
    }

    @Data
    public static class CpLotteryTicketInfo {
        //        private long uid;
//        private long anchorUid;
        private long addTicket;
        private long beforeTicket;
        private long afterTicket;
        private Map<Long, Long> stockMap;

//        public CpLotteryTicketInfo(long uid, long anchorUid) {
//            this.uid = uid;
//            this.anchorUid = anchorUid;
//        }
    }

    @Data
    public static class CpLotteryUserTicketInfo {
        //        private long uid;
//        private long anchorUid;
        private long useTicket;
        private long step;
        private long rewardAmount;
        private Map<Long, Long> deductMap;
    }

}
