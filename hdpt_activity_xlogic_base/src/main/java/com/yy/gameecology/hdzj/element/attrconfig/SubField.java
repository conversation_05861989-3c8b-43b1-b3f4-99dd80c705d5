package com.yy.gameecology.hdzj.element.attrconfig;

import java.lang.annotation.*;

/**
 * 字典
 *
 * <AUTHOR>
 **/
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SubField {
    /**
     * 子属性的名字
     **/
    String fieldName();

    /**
     * 子属性的类型
     **/
    Class<?> type();

    /**
     * 子属性展示的标签文案
     **/
    String labelText() default "";

    /**
     * 子属性说明
     **/
    String remark() default "";

    /**
     * 是否跳过
     * 对于 Map<K,List<>> 类型,List是不需要收集成子属性的
     **/
    boolean skip() default false;

    /**
     * 强行指定propType
     * @return
     */
    String propType() default "";
}
