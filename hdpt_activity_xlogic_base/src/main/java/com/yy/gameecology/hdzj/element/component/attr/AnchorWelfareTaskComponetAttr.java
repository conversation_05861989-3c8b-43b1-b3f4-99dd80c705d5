package com.yy.gameecology.hdzj.element.component.attr;


import com.yy.gameecology.activity.bean.anchorwelfare.LoginTaskAward;
import com.yy.gameecology.activity.bean.anchorwelfare.TaskGroupConfig;
import com.yy.gameecology.activity.bean.anchorwelfare.TaskInfoConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Data
public class AnchorWelfareTaskComponetAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "白名单组件索引", remark = "用户白名单存储的useIndex")
    private long whitelistCmptInx;

    @ComponentAttrField(labelText = "黑名单组件索引", remark = "用户黑名单存储的useIndex")
    private long blacklistCmptInx;

    @ComponentAttrField(labelText = "同一实名用户黑名单组件索引", remark = "同一实名用户黑名单useIndex")
    private long relateUidblacklistCmptInx;

    @ComponentAttrField(labelText = "同一实名检查忽略uid列表", remark = "测试环境使用")
    private String ignoreRelateUidb;

    @ComponentAttrField(labelText = "App", remark = "yomi、zhuiwan")
    private String app;

    @ComponentAttrField(labelText = "奖池id")
    private long taskId;

    @ComponentAttrField(labelText = "黄水晶奖包id")
    private long crystalPackageId;

    @ComponentAttrField(labelText = "黄钻奖包id")
    private long diamondPackageId;


    @ComponentAttrField(labelText = "登录任务配置",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = LoginTaskAward.class)})
    private List<LoginTaskAward> loginTaskAward;


    @ComponentAttrField(labelText = "签到任务组配置",remark = "任务天数从小到大配置",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = TaskGroupConfig.class)})
    private List<TaskGroupConfig> groupConfig;


    @ComponentAttrField(labelText = "签到任务详细配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "任务天数"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = TaskInfoConfig.class, labelText = "任务")
            })
    Map<Integer, List<TaskInfoConfig>> taskConfig;


    @ComponentAttrField(labelText = "榜单任务源配置", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = HdztTaskSource.class)
    })
    private List<HdztTaskSource> taskSourceList = Collections.emptyList();


    @ComponentAttrField(labelText = "频道停留辅助榜配置", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "任务天数"),
            @SubField(fieldName = Constant.MAP_LIST_VALUE, type = AssistRankConfig.class, labelText = "辅助榜")
    })
    private Map<Integer,List<AssistRankConfig>> assistRankConfig;


    @ComponentAttrField(labelText = "白名单用户开始拉取时间", remark = "yyyyMMdd")
    private String whiteUserStartTime;

    @ComponentAttrField(labelText = "白名单用户有效开播天数")
    private int valideLiveDays;


    @ComponentAttrField(labelText = "风控策略key")
    private String riskStrategyKey = "ANCHOR_WELFARE_ACT";

    @ComponentAttrField(labelText = "活动发放总奖池",remark = "单位厘")
    private long totalPool;

    @ComponentAttrField(labelText = "奖池内最低金额",remark = "单位厘，低于5元归0")
    private long minAwardPool;

    @Data
    public static class HdztTaskSource {

        @ComponentAttrField(labelText = "任务天数")
        private int level;

        @ComponentAttrField(labelText = "任务类型", remark = "1-普通签到 2-频道停留 3-上传相册 4-发布动态 5-上传语音条")
        private int taskType;

        @ComponentAttrField(labelText = "任务榜单")
        private long rankId;

        @ComponentAttrField(labelText = "任务阶段")
        private long phaseId;
    }

    @Data
    public static class AssistRankConfig {

        @ComponentAttrField(labelText = "任务类型", remark = "1-普通签到 2-频道停留 3-上传相册 4-发布动态 5-上传语音条")
        private int taskType;

        @ComponentAttrField(labelText = "辅助榜单id")
        private long rankId;

        @ComponentAttrField(labelText = "辅助榜单阶段id")
        private long phaseId;

        @ComponentAttrField(labelText = "频道停留辅助榜单ITEM")
        private String applyItemId;

        @ComponentAttrField(labelText = "道停留辅助榜单RoleId")
        private long applyRoleId;
    }


    @AllArgsConstructor
    @Getter
    public static enum TaskType {
        NORMAL_SIGN(1, "普通签到"),
        SIGN_CHANNEL_HEART(2, "签约频道停留"),
        UPLOAD_PHOTO(3, "上传相册"),
        ADD_POST(4,"发布动态"),
        UPLOAD_AUDIO(5,"上传语音");

        private int code;
        private String name;
    }

}
