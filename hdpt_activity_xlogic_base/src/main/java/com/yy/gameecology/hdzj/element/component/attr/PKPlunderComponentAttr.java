package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.Map;

@Data
public class PKPlunderComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "初始榜单ID")
    protected long initRankId;

    @ComponentAttrField(labelText = "初始阶段ID")
    protected long initPhaseId;

    @ComponentAttrField(labelText = "初始账户余额")
    protected long initCurrency;

    @ComponentAttrField(labelText = "结算配置", subFields = {
            @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "rankId_phaseId"),
            @SubField(fieldName = Constant.VALUE, type = SettleConfig.class, labelText = "结算配置")
    })
    protected Map<String, SettleConfig> settleConfigs;

    @Data
    public static class SettleConfig {

        @ComponentAttrField(labelText = "掠夺类型，1：按比例，2：固定值")
        protected int plunderType;

        @ComponentAttrField(labelText = "比例：万分之，固定值：掠夺值")
        protected long plunderValue;
    }
}
