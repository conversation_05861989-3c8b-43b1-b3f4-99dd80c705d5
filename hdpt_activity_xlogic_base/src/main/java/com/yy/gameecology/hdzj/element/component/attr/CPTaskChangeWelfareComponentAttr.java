package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;
import java.util.Map;

@Getter
@Setter
public class CPTaskChangeWelfareComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    protected long busiId;

    @ComponentAttrField(labelText = "任务榜单id", remark = "多个使用英文逗号隔开")
    protected long rankId;

    @ComponentAttrField(labelText = "任务阶段id")
    protected long phaseId;

    @ComponentAttrField(labelText = "是否保存记录", remark = "是否需要开启记录发奖记录")
    protected boolean record;

    @ComponentAttrField(labelText = "是否发奖励单播", remark = "是否需要给对应CP发中奖单播")
    protected boolean noticeAward;

    @ComponentAttrField(labelText = "单播延迟", remark = "给对应CP发奖励单播时需要延迟的时间")
    protected Duration noticeDelay;

    @ComponentAttrField(labelText = "发奖配置", subFields = {
            @SubField(labelText = "任务等级", fieldName = Constant.KEY1, type = Long.class, remark = "从1开始"),
            @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class)
    })
    protected Map<Long, AwardAttrConfig> taskAwardAttrMap;
}
