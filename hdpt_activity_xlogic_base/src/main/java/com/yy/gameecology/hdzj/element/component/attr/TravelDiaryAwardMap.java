package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

@Data
public class TravelDiaryAwardMap {
    @ComponentAttrField(labelText = "任务等级")
    private int level;

    @ComponentAttrField(labelText = "发奖奖池ID")
    private long taskId;

    @ComponentAttrField(labelText = "发奖奖包ID")
    private long packageId;

    @ComponentAttrField(labelText = "礼物名称")
    private String giftName;

    @ComponentAttrField(labelText = "礼物图标")
    private String giftIcon;

    @ComponentAttrField(labelText = "礼物金额-单位厘")
    private long giftAmount;

}
