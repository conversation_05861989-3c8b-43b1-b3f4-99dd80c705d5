package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.common.consts.PBCommonBannerId;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvagConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class CpPuzzleComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "强厅角色")
    private long tingActor;

    @ComponentAttrField(labelText = "cp任务榜单id")
    private long cpTaskRankId;

    @ComponentAttrField(labelText = "全程阶段id")
    private long cpTaskPhaseId;

    @ComponentAttrField(labelText = "cp榜单id")
    private long cpRankId;

    @ComponentAttrField(labelText = "cp用户对主持贡献榜单id")
    private long cpContributeRankId;

    @ComponentAttrField(labelText = "cp 主持对用户贡献榜单id")
    private long cpAntiContributeRankId;

    @ComponentAttrField(labelText = "全程阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "cp榜单拉取数量")
    private long rankLimit;

    @ComponentAttrField(labelText = "万能碎片图片")
    private String jackPointPic;

    @ComponentAttrField(labelText = "产生万能碎片时间")
    private long jackPointTimestamp;

    @ComponentAttrField(labelText = "真实产生万能碎片等级")
    private long jackPointTaskIndex;

    @ComponentAttrField(labelText = "对外产生万能碎片等级")
    private long jackPointShowTaskIndex;

    @ComponentAttrField(labelText = "任务抽奖奖池id")
    private long lotteryTaskId;

    @ComponentAttrField(labelText = "大奖奖池task id")
    private long jackPointTaskId;

    @ComponentAttrField(labelText = "大奖奖池package id")
    private long jackPointPackageId;

    @ComponentAttrField(labelText = "大奖奖品名")
    private String jackPointAwardName;

    @ComponentAttrField(labelText = "大奖图片")
    private String jackPointAwardPic;

    @ComponentAttrField(labelText = "任务阈值", remark = ",多个逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
    })
    private List<Long> missions;

    @ComponentAttrField(labelText = "任务奖池",remark = "任务奖池",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "任务等级"),
                    @SubField(fieldName = Constant.VALUE, type = Award.class, labelText = "奖励")
            })
    private Map<Integer, Award> missionAward = Maps.newHashMap();

    @ComponentAttrField(labelText = "拼图位置",remark = "拼图位置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "cid"),
                    @SubField(fieldName = Constant.VALUE, type = RowCol.class)
            })
    private Map<String, RowCol> cidRowColMap =  Maps.newHashMap();

    @ComponentAttrField(labelText = "行集齐拼图列表",remark = "行集齐拼图列表",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "行"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = String.class, labelText = "拼图列表")
            })
    private Map<Integer, List<String>> rowPuzzleCids = Maps.newHashMap();

    @ComponentAttrField(labelText = "列集齐拼图列表",remark = "列集齐拼图列表",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "列"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = String.class, labelText = "拼图列表")
            })
    private Map<Integer, List<String>> colPuzzleCids = Maps.newHashMap();

    @ComponentAttrField(labelText = "行集齐奖励",remark = "行集齐奖励",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "行"),
                    @SubField(fieldName = Constant.VALUE, type = Award.class)
            })
    private Map<Integer, Award> rowAwardMap =  Maps.newHashMap();

    @ComponentAttrField(labelText = "列集齐奖励",remark = "列集齐奖励",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "列"),
                    @SubField(fieldName = Constant.VALUE, type = Award.class)
            })
    private Map<Integer, Award> colAwardMap =  Maps.newHashMap();

    @ComponentAttrField(labelText = "抽奖packageId对应的拼图",remark = "抽奖packageId对应的拼图",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "lottery packageId"),
                    @SubField(fieldName = Constant.VALUE, type = String.class)
            })
    private Map<Long, String> lotteryPuzzleMap =  Maps.newHashMap();

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    private long busiId = 200;

    @ComponentAttrField(labelText = "完成广播类型", dropDownSourceBeanClass = BroadcastTypeSource.class)
    private int bannerBroType = 2;

    @ComponentAttrField(labelText = "bannerId")
    private long bannerId = PBCommonBannerId.SUNSHINE_TASK;

    @ComponentAttrField(labelText = "bannerType")
    private long bannerType = 0;

    @ComponentAttrField(labelText = "横幅svga配置",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "svgaConfigCode"),
                    @SubField(fieldName = Constant.VALUE, type = BannerSvagConfig.class, labelText = "svga配置")})
    private Map<String, BannerSvagConfig> bannerSvag;

    @ComponentAttrField(labelText = "svgaText文案配置",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "svga文案配置编码"),
                    @SubField(fieldName = Constant.VALUE, type = BannerSvgaTextConfig.class, labelText = "svga文案配置")})
    private Map<String, BannerSvgaTextConfig> svgaText;

    @ComponentAttrField(labelText = "text动态文案", remark = "可用于替换svgaText文案配置-富文本消息 中的占位符",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "文案中的占位符"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "占位符对应的值")})
    private Map<String, String> textDynamicValue;

    @ComponentAttrField(labelText = "svga图片key",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "imgkey"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "图片地址")})
    private Map<String, String> svgaImgLayers;


    @ComponentAttrField(labelText = "循环次数", remark = "循环播放次数, 0-无限循环(勿填0)")
    private int loops = 1;

    @ComponentAttrField(labelText = "布局", remark = " 横幅类型(contentType)==6使用 可选 动画播放位置（左右充满，垂直对齐类型）0：全屏播放；1：居中对齐播放；2：顶部对齐播放；3：底部对齐播放")
    private int layoutType;

    @ComponentAttrField(labelText = "布局边距", remark = "相对父布局的间距 2个元素的数组，分别对应顶部和底部的间距；对应位置为[top, bottom]。通常top、bottom为0。配置例子：{\"android\":[10,0],\"ios\":[0,0]}")
    private String layoutMargin = "{\"android\":[10,0],\"ios\":[0,0]}";


    @ComponentAttrField(labelText = "宽高比", remark = "必填 宽高比 客户端默认宽为全屏，svga高度根据宽高比计算,填写实例：6:9")
    private String whRatio;

    @ComponentAttrField(labelText = "广播业务ID", remark = "1 -语音房 2 -交友房 4 -其他 8 -宝贝 位域表示 支持组合：交友and宝贝：2+8")
    private int broBusiId;


    @Data
    public static class RowCol {
        @ComponentAttrField(labelText = "行")
        private int row;
        @ComponentAttrField(labelText = "列")
        private int col;
        @ComponentAttrField(labelText = "图片")
        private String pic;
        @ComponentAttrField(labelText = "发奖taskId")
        private long taskId;
        @ComponentAttrField(labelText = "发奖packageId")
        private long packageId;
    }

    @Data
    public static class Award {
        @ComponentAttrField(labelText = "图片")
        private String pic;
        @ComponentAttrField(labelText = "名称")
        private String name;
        @ComponentAttrField(labelText = "发奖taskId")
        private long taskId;
        @ComponentAttrField(labelText = "发奖packageId")
        private long packageId;
    }
}
