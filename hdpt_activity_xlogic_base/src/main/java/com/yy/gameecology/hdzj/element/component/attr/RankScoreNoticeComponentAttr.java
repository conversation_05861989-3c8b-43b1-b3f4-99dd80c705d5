package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.RoleTypeSource;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption: 榜单分数通知组件属性
 * @Date: 2022/4/15 10:39
 * @Modified:
 */
@Data
public class RankScoreNoticeComponentAttr extends ComponentAttr {

    /**
     * 榜单id
     */
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;
    /**
     * 阶段id
     */
    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;

    /**
     * 查询的榜单id-不配置按rankId
     */
    @ComponentAttrField(labelText = "查询的榜单id", remark = "不配置按榜单id")
    private long extQueryRankId;
    /**
     * 查询的阶段id -不配置按phaseId
     */
    @ComponentAttrField(labelText = "查询的阶段id", remark = "不配置按阶段id")
    private long extQueryPhaseId;
    /**
     * 查询的key
     */
    @ComponentAttrField(labelText = "查询的key")
    private String extQueryKey;


    /**
     * 成员角色
     */
    @ComponentAttrField(labelText = "成员角色", dropDownSourceBeanClass = RoleTypeSource.class)
    private int roleType;


    /**
     * 消息模板
     */
    @ComponentAttrField(labelText = "消息模板")
    private String noticeFormat;

    /**
     * 如流群id
     */
    @ComponentAttrField(labelText = "如流群id")
    private long groupId;

    /**
     * 机器人token
     * http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dd54387406ec799763cd62b4c40219dd8
     */
    @ComponentAttrField(labelText = "机器人token")
    private String robotToken;
    /**
     * @的如流用户
     */
    @ComponentAttrField(labelText = "@的如流用户", remark = "多个时逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class)})
    private List<String> userIds;
    /**
     * 通知的分数map
     */
    @ComponentAttrField(labelText = "通知的分数",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, labelText = "分值", type = Long.class),
                    @SubField(fieldName = Constant.VALUE, labelText = "分值对应的文案", type = String.class)
            })
    private Map<Long, String> scoreMap;

    @ComponentAttrField(labelText = "霸屏最低分值")
    private long bpScoreMinLimit;

    @ComponentAttrField(labelText = "高光霸屏图片生成链接")
    private String ggbpLink;

    @ComponentAttrField(labelText = "白名单组件索引", remark = "用户白名单存储的白名单组件的useIndex")
    private long whitelistCmptInx;

}
