package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;

/**
 * @Author: CXZ
 * @Desciption: 奖品广播属性
 * @Date: 2021/4/15 19:19
 * @Modified:
 */
public class AwardBroadcastComponentAttr extends ComponentAttr {
    /**
     * 发奖事件过来的业务id
     */
    @ComponentAttrField(labelText = "发奖事件业务id", dropDownSourceBeanClass = BizSource.class)
    private Long busiId;

    /**
     * 广播业务类型
     */
    @ComponentAttrField(labelText = "广播业务类型", dropDownSourceBeanClass = BizSource.class)
    private Integer broBusiId;

    @ComponentAttrField(labelText = "奖池id", remark = "多个时用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private long[] taskIds;
    /**
     * 空表示所有taskid下的奖品
     */
    @ComponentAttrField(labelText = "奖包id", remark = "空表示所有奖池下的奖品,多个时用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private long[] packageIds;
    /**
     * 是否合并奖品
     */
    @ComponentAttrField(labelText = "是否合并奖品")
    private boolean isMageAward = true;
    /**
     * isMageAward = true 时的分隔符
     */
    @ComponentAttrField(labelText = "分隔符", remark = "合并奖品 = true 时的分隔符")
    private String separator = "+";
    /**
     * 广播类型  4=单业务广播；5=多业务广播
     */
    @ComponentAttrField(labelText = "广播类型", dropDownSourceBeanClass = BroadcastTypeSource.class)
    private int broadcastType = 4;


    public long getBusiId() {
        return busiId;
    }

    public void setBusiId(long busiId) {
        this.busiId = busiId;
    }

    public long[] getTaskIds() {
        return taskIds;
    }

    public void setTaskIds(long[] taskIds) {
        this.taskIds = taskIds;
    }

    public long[] getPackageIds() {
        return packageIds;
    }

    public void setPackageIds(long[] packageIds) {
        this.packageIds = packageIds;
    }

    public boolean isMageAward() {
        return isMageAward;
    }

    public void setMageAward(boolean mageAward) {
        isMageAward = mageAward;
    }

    public String getSeparator() {
        return separator;
    }

    public void setSeparator(String separator) {
        this.separator = separator;
    }

    public int getBroadcastType() {
        return broadcastType;
    }

    public void setBroadcastType(int broadcastType) {
        this.broadcastType = broadcastType;
    }

    public int getBroBusiId() {
        return broBusiId;
    }

    public void setBroBusiId(int broBusiId) {
        this.broBusiId = broBusiId;
    }
}
