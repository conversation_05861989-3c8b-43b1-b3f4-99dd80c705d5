package com.yy.gameecology.hdzj.element.attrconfig;

/**
 * <AUTHOR>
 * @date 2022.12.23 11:06
 */
public enum NianshouStatus {
    //未开始 or 未打败,等待下一次定时器初始化
    NOT_START("0"),
    //已创建,即就绪状态
    CREATED("11"),
    //打败了,展示十秒失败状态
    DEAD_HOLDING("12");


    private String status;
    NianshouStatus(String status) {
        this.status = status;
    }


    public String getStatus() {
        return status;
    }
}
