package com.yy.gameecology.hdzj.element.component;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.service.SvcSDKService;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AwardUnicastComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 单播协议
 * 1. uri=1019
 * 2. type = 1 可配置,用于区分不同弹窗
 * 3. content：获得的奖品名称
 *
 * @Author: CXZ
 * @Desciption: 奖品单播弹窗
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
@Component
public class AwardUnicastComponent extends BaseActComponent<AwardUnicastComponentAttr> {

    @Autowired
    private SvcSDKService svcSDKService;

    @Autowired
    private CommonDataDao commonDataDao;

    @Override
    public Long getComponentId() {
        return ComponentId.AWARD_UNICAST;
    }

    private static final String KEY_NAME = "broLevel";


    @HdzjEventHandler(value = HdztAwardLotteryMsg.class, canRetry = false)
    public void unicastAward(HdztAwardLotteryMsg event, AwardUnicastComponentAttr attr) {
        long taskId = event.getTaskId();
        long busiId = event.getBusiId();
        if (attr.getBusiId() != busiId || !ArrayUtils.contains(attr.getTaskIds(), taskId)) {
            return;
        }

        long actId = attr.getActId();
        long uid = event.getUid();
        long[] unicastAttrPackageIds = attr.getPackageIds();
        List<HdztAwardLotteryMsg.Award> awardList = event.getData().stream()
                .filter(award -> ArrayUtils.isEmpty(unicastAttrPackageIds) || ArrayUtils.contains(unicastAttrPackageIds, award.getPackageId()))
                .toList();
        if (awardList.isEmpty()) {
            return;
        }
        log.info("event={}", JSON.toJSONString(event));

        int type = attr.getType();

        // 已广播过的等级
        int level = 0;
        if (MapUtils.isNotEmpty(attr.getPackageLevelMap())) {
            List<Integer> levelList = awardList.stream().map(award -> attr.getPackageLevelMap().get(award.getPackageId())).sorted().toList();
            if (!levelList.isEmpty()) {
                level = levelList.getLast();
            }
        }

        if (level > 0) {
            String hashKey = String.valueOf(uid);
            String value = commonDataDao.hashValueGet(actId, attr.getCmptId(), attr.getCmptUseInx(), KEY_NAME, hashKey);
            int oldLevel = Convert.toInt(value, 0);
            if (oldLevel > level) {
                log.info("has already bro old level={},level={}", oldLevel, level);
                return;
            }

            commonDataDao.hashValueSet(actId, attr.getCmptId(), attr.getCmptUseInx(), KEY_NAME, hashKey, String.valueOf(level));
            type = level;
        }

        if (attr.isMageAward()) {
            String content = awardList.stream().map(HdztAwardLotteryMsg.Award::getGiftName).collect(Collectors.joining(attr.getSeparator()));
            unicastTip(actId, uid, content, type);
        } else {
            final int fType = type;
            awardList.forEach(award -> unicastTip(actId, uid, award.getGiftName(), fType));
        }
    }

    private void unicastTip(long actId, long uid, String content, int type) {
        GameecologyActivity.Act202008_LotteryTips.Builder tips = GameecologyActivity.Act202008_LotteryTips.newBuilder()
                .setActId(actId).setType(type).setContent(content);
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202008_LotteryTips_VALUE)
                .setAct202008LotteryTips(tips).build();
        svcSDKService.unicastUid(uid, msg);
        log.info("unicastAward unicastTip  uid:{} type:{} content:{}", uid, type, content);
    }

    public void test(long actId, int index, long uid) {

        AwardUnicastComponentAttr attr = getComponentAttr(actId, index);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + index);
        HdztAwardLotteryMsg event = new HdztAwardLotteryMsg();
        event.setActId(actId);
        event.setBusiId(attr.getBusiId());
        event.setSeq(UUID.randomUUID().toString());
        event.setUid(uid);
        event.setTaskId(attr.getTaskIds()[0]);
        event.setData(Lists.newArrayList());
        for (long packgeId : attr.getPackageIds()) {
            HdztAwardLotteryMsg.Award award = new HdztAwardLotteryMsg.Award();
            award.setPackageId(packgeId);
            award.setGiftName("testGiftName" + packgeId);
            event.getData().add(award);
        }
        unicastAward(event, attr);
    }

}
