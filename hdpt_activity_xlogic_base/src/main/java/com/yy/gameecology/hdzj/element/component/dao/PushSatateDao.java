package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class PushSatateDao {

    @Autowired
    private GameecologyDao gameecologyDao;

    private static final String UPDATE_RECORD_STATE = "update cmpt_5129_push_user_info_%s set `push_time` = ? ,`push_amount` = `push_amount`+1  where uid = ?";

    private static final String SELECT_PUSH_STATE = "select close_push from cmpt_5129_push_user_info_%s where `uid` = ?";

    private static final String SELECT_PUSH_MEMBERS = """
            select uid from cmpt_5129_push_user_info_%s 
            where `uid` > ?
            and `close_push` = 0 
            and `push_amount` < ? 
            and `last_complete_task_time` <  ? 
            and `push_time` < ? 
            order by uid limit ? ,?""";

    public int updateAwardRecordState(long actId, Date pushTime,long uid) {
        String  sql = String.format(UPDATE_RECORD_STATE, actId);
        return gameecologyDao.getJdbcTemplate().update(sql,pushTime,uid);
    }

    public Long  getUserPushState(long actId,long uid) {
        String  sql = String.format(SELECT_PUSH_STATE, actId);
        List<Long> l = gameecologyDao.getJdbcTemplate().queryForList(sql,Long.class,uid);
        return CollectionUtils.isEmpty(l) ? 0 : l.get(0);
    }

    public List<Long> selectPushMembers(long actId,Date pushTime,Date lastCompleteTaskTime,long push_amount,long startUid, long offset,long limit) {
        String  sql = String.format(SELECT_PUSH_MEMBERS, actId);
        List<Long> l = gameecologyDao.getJdbcTemplate().queryForList(sql,Long.class,startUid,push_amount,lastCompleteTaskTime,pushTime,offset,limit);
        return CollectionUtils.isEmpty(l) ? null : l;
    }

    public void updateUserPushState(long actId, long uid,Date timeInfo, long pushState) {
        String sql = "INSERT INTO cmpt_5129_push_user_info_%s (uid, last_complete_task_time,push_time,push_amount,close_push,create_time,update_time) " +
                "VALUES (?, ?, ?, ?, ?, NOW(), NOW()) " +
                "ON DUPLICATE KEY UPDATE  " +
                " close_push  = VALUES(close_push)";
        sql = String.format(sql, actId);

        gameecologyDao.update(sql, uid, null, timeInfo , 0, pushState);
    }

    public void updateUserLastCompleteTime(long actId, long uid, Date lastCompleteTaskTime) {
        String sql = "INSERT INTO cmpt_5129_push_user_info_%s (uid, last_complete_task_time,push_time,push_amount,close_push,create_time,update_time) " +
                "VALUES (?, ?, ?, ?, ?, NOW(), NOW()) " +
                "ON DUPLICATE KEY UPDATE  " +
                " last_complete_task_time  = VALUES(last_complete_task_time)";
        sql = String.format(sql, actId);
        gameecologyDao.update(sql, uid, lastCompleteTaskTime, lastCompleteTaskTime , 0, 0);
    }


}
