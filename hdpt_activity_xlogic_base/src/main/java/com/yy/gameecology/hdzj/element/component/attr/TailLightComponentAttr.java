package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption: 尾灯玩法属性
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
public class TailLightComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "礼物id", remark = "多个时用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class)})
    private String[] giftIds;

    @ComponentAttrField(labelText = "奖池id")
    private long taskId;

    @ComponentAttrField(labelText = "尾灯等级对应的奖包",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "尾灯等级"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖包id")
            })
    private Map<Integer, Long> levelPackageMap;

    @ComponentAttrField(labelText = "尾灯等级对应的资源",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "尾灯等级"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "资源url")
            })
    Map<Integer, String> materialMap;

    @ComponentAttrField(labelText = "尾灯等级对应的提示次数",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "尾灯等级"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "提示次数")
            })
    Map<Integer, Integer> levelTipCountMap;


    public String[] getGiftIds() {
        return giftIds;
    }

    public void setGiftIds(String[] giftIds) {
        this.giftIds = giftIds;
    }

    public long getTaskId() {
        return taskId;
    }

    public void setTaskId(long taskId) {
        this.taskId = taskId;
    }

    public Map<Integer, Long> getLevelPackageMap() {
        return levelPackageMap;
    }

    public void setLevelPackageMap(Map<Integer, Long> levelPackageMap) {
        this.levelPackageMap = levelPackageMap;
    }

    public Map<Integer, String> getMaterialMap() {
        return materialMap;
    }

    public void setMaterialMap(Map<Integer, String> materialMap) {
        this.materialMap = materialMap;
    }

    public Map<Integer, Integer> getLevelTipCountMap() {
        return levelTipCountMap;
    }

    public void setLevelTipCountMap(Map<Integer, Integer> levelTipCountMap) {
        this.levelTipCountMap = levelTipCountMap;
    }
}
