package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;


/**
 * @Author: CXZ
 * @Desciption: 用户进入模板tips组件属性
 * @Date: 2021/7/16 15:39
 * @Modified:
 */
public class UserEnterTipsComponentAttr extends ComponentAttr {
    /**
     * 处理的业务
     */
    @ComponentAttrField(labelText = "业务id列表", remark = "多个时逗号分隔，200:游戏生态,400:游戏宝贝,500:交友,600:约战,900:陪玩",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Long[] busiIds;
    /**
     * 是否需要是首次进入
     */
    @ComponentAttrField(labelText = "是否需要是首次进入")
    private boolean needFirst = true;
    /**
     * 消息的类型，需要跟pc端同步
     */
    @ComponentAttrField(labelText = "通知类型", remark = "通知类型,需要和前端协定好")
    private String noticeType;
    /**
     * 消息实体，透传给前端，可以是json
     */
    @ComponentAttrField(labelText = "消息实体", remark = "消息实体，透传给前端，可以是json")
    private String noticeValue = "";
    /**
     * 消息扩展，透传给前端，可以是json
     */
    @ComponentAttrField(labelText = "消息扩展", remark = "消息扩展，透传给前端，可以是json")
    private String noticeExt = "";

    public Long[] getBusiIds() {
        return busiIds;
    }

    public void setBusiIds(Long[] busiIds) {
        this.busiIds = busiIds;
    }

    public boolean isNeedFirst() {
        return needFirst;
    }

    public void setNeedFirst(boolean needFirst) {
        this.needFirst = needFirst;
    }

    public String getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }

    public String getNoticeValue() {
        return noticeValue;
    }

    public void setNoticeValue(String noticeValue) {
        this.noticeValue = noticeValue;
    }

    public String getNoticeExt() {
        return noticeExt;
    }

    public void setNoticeExt(String noticeExt) {
        this.noticeExt = noticeExt;
    }
}
