package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class SparkPromiseComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    protected long busiId;

    @ComponentAttrField(labelText = "过滤弹幕游戏频道")
    protected boolean excludeDanmaku = true;

    @ComponentAttrField(labelText = "任务榜单ID")
    protected long taskRankId;

    @ComponentAttrField(labelText = "任务阶段ID")
    protected long taskPhaseId;

    @ComponentAttrField(labelText = "时间分榜", dropDownSourceBeanClass = TimeKeySource.class)
    private long timeKey;

    @ComponentAttrField(labelText = "强厅角色", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class), remark = "上报角色为：sid_ssid的角色，主要为了获取当前子频道")
    protected List<Long> hallRoleIds;

    @ComponentAttrField(labelText = "花火时长")
    protected Duration sparkDuration;

    @ComponentAttrField(labelText = "公屏文案")
    private String screenText;

    @ComponentAttrField(labelText = "是否拍照")
    protected boolean taskPhoto;

    @ComponentAttrField(labelText = "完成任务抽奖ID")
    protected long sparkLotteryTaskId;

    @ComponentAttrField(labelText = "花火配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Spark.class), remark = "所有主题的花火的配置")
    protected List<Spark> sparks;

    @ComponentAttrField(labelText = "挂件广播范围", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级", remark = "从1开始的任务等级"),
            @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "广播范围", remark = "2-子频道，3-顶级频道，4-当前业务的所有在线频道，5-所有频道，6-家族")
    }, remark = "完成对应任务触发的挂件花火特效的广播范围")
    protected Map<Long, Integer> layerBroTypes;

    @ComponentAttrField(labelText = "烟花奖池ID")
    protected long fireworkTaskId;

    @ComponentAttrField(labelText = "烟花奖包ID")
    protected long fireworkPackageId;

    @ComponentAttrField(labelText = "烟花静默时长", remark = "同一uid兑换烟花触发特效后静默的时长，静默期内再次兑换不会触发特效")
    protected Duration fireworkSilent;

    @ComponentAttrField(labelText = "烟花App特效", remark = "【万事如意】烟花App特效的url")
    protected String fireworkMp4Url;

    @ComponentAttrField(labelText = "烟花App特效等级", remark = "【万事如意】烟花App特效的广播优先级")
    protected int fireworkBroLevel;

    @ComponentAttrField(labelText = "未送礼命中概率", remark = "未送过活动礼物的神豪命中卡片可以抽奖的概率，分母是：10000")
    protected int unrewardedCent;

    @ComponentAttrField(labelText = "已送礼命中概率", remark = "已经送过活动礼物的神豪命中卡片可以抽奖的概率，分母是：10000")
    protected int rewardedCent;

    @ComponentAttrField(labelText = "用户抽奖奖池", subFields = {
            @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "日期", remark = "yyyyMMdd"),
            @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖池ID", remark = "对应日期用户卡片一致时抽奖的奖池ID")
    })
    protected Map<String, Long> userLotteryTaskIds;

    @ComponentAttrField(labelText = "花火抽奖次数上限", remark = "单次触发火花后至多可以让多少神豪翻卡一致")
    protected int lotteryLimit;

    public Spark fetchSparkByPackageId(long packageId) {
        return sparks.stream().filter(spark -> spark.getPackageId() == packageId).findFirst().orElse(null);
    }

    @Getter
    @Setter
    public static class Spark {
        @ComponentAttrField(labelText = "花火奖包ID", remark = "花火秀对应抽奖奖池配置的奖包ID")
        protected long packageId;

        @ComponentAttrField(labelText = "花火秀名称")
        protected String name;

        @ComponentAttrField(labelText = "花火秀logo", propType = ComponentAttrCollector.PropType.IMAGE)
        protected String logo;

        @ComponentAttrField(labelText = "App特效", remark = "播放app广播特效的mp4素材，为空则不播放app特效")
        protected String mp4Url;

        @ComponentAttrField(labelText = "App特效等级", remark = "播放app广播特效时的广播优先级")
        protected int broLevel;
    }

}
