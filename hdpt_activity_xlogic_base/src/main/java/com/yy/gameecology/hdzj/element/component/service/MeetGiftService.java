package com.yy.gameecology.hdzj.element.component.service;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.TurnoverServiceClient;
import com.yy.gameecology.activity.client.thrift.vo.AddPropsUsedReq;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2067AsyncWelfare;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5146Record;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.component.attr.MeetGiftComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.AsyncWelfareDao;
import com.yy.gameecology.hdzj.element.component.dao.MeetGiftDao;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.thrift.turnover.TServiceException;
import com.yy.thrift.turnover.TYyInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MeetGiftService {

    public static final String CONSUMED_KEY = "meetGiftConsumed";

    @Autowired
    private MeetGiftDao meetGiftDao;

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private AsyncWelfareDao asyncWelfareDao;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    protected HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    protected TurnoverServiceClient turnoverServiceClient;

    @Autowired
    protected CommonService commonService;

    public long queryRemaining(MeetGiftComponentAttr attr, Date now) {
        String hashKey = DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2);
        String value = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), CONSUMED_KEY, hashKey);
        long consumed = 0;
        if (StringUtils.isNumeric(value)) {
            consumed = Long.parseLong(value);
        }

        return Math.max(attr.getDailyLimitCount() - consumed, 0);
    }

    public boolean queryUserReceived(MeetGiftComponentAttr attr, long uid, String hdid) {
        List<Cmpt5146Record> records = meetGiftDao.selectByUidOrHdid(attr.getActId(), uid, hdid);
        return CollectionUtils.isNotEmpty(records);
    }

    /**
     * 领取|直接送出礼物
     * @param directSendGift 是否直接送出礼物
     * @param recvUid 收礼uid，directSendGift = true 的情况下必填
     * @return
     */
    public Integer trySendMeetGift(MeetGiftComponentAttr attr,
                                   long uid,
                                   String hdid,
                                   long sid,
                                   long ssid,
                                   Date date,
                                   boolean directSendGift,
                                   long recvUid,
                                   int usedChannel,
                                   String clientExpand) {
        List<Cmpt5146Record> records = meetGiftDao.selectByUidOrHdid(attr.getActId(), uid, hdid);
        if (CollectionUtils.isNotEmpty(records)) {
            return 1;
        }

        long remaining = queryRemaining(attr, date);
        if (remaining <= 0) {
            return 2;
        }

        final String seq = String.format("mg:%d:%s:%d", attr.getActId(), hdid, uid);
        final String hashKey = DateFormatUtils.format(date, DateUtil.PATTERN_TYPE2);

        final AddPropsUsedReq addPropsUsedReq = directSendGift
                ? buildAddPropsUsedReq(attr, seq, uid, recvUid, sid, ssid, usedChannel, clientExpand)
                : null;

        return transactionTemplate.execute(status -> {
            CommonDataDao.ValueIncResult result = commonDataDao.hashValueIncreaseIgnoreWithLimit(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), seq, CONSUMED_KEY, hashKey, 1, attr.getDailyLimitCount());
            if (result.isViolateLimit()) {
                return 2;
            }

            Cmpt5146Record record = new Cmpt5146Record();
            record.setActId(attr.getActId());
            record.setUid(uid);
            record.setHdid(hdid);
            record.setSid(sid);
            record.setSsid(ssid);
            record.setCreateTime(date);

            int rs = meetGiftDao.insertIgnore(record);
            if (rs <= 0) {
                status.setRollbackOnly();
                return 1;
            }

            if (addPropsUsedReq != null) {
                // 写入本地消息表
                Cmpt2067AsyncWelfare welfare = new Cmpt2067AsyncWelfare();
                welfare.setActId(attr.getActId());
                welfare.setCmptUseInx(attr.getCmptUseInx());
                welfare.setSeq(seq);
                welfare.setBusiId((long) attr.getBuisId());
                welfare.setUid(uid);
                welfare.setTaskId(attr.getTAwardTskId());
                welfare.setTaskPackageIds(JSON.toJSONString(Map.of(attr.getTAwardTskId(), Map.of(attr.getTAwardPkgId(), 1))));
                welfare.setExtData("{}");
                welfare.setState(0);
                Date now = new Date();
                welfare.setCreateTime(now);
                welfare.setUpdateTime(now);
                int ret = asyncWelfareDao.addWelfareRecord(welfare);
                if (ret <= 0) {
                    status.setRollbackOnly();
                    return 1;
                }

                // 调用营收接口送礼
                try {
                    // 0 失败 1成功 2超出限额
                    int r = turnoverServiceClient.addPropsUsed(addPropsUsedReq);
                    if (r == 1) {
                        return 0;
                    }
                    status.setRollbackOnly();
                    log.warn("addPropsUsed customBizFailed r:{}", r);
                    return -1;
                } catch (TServiceException e) {
                    // -1: 参数错误 -22: 账户不存在 -405: 订单序号已存在 -500: 服务端出错 -600: 该活动不存在
                    if (e.getCode() == -405) {
                        log.warn("addPropsUsed duplicateOrder code:{} msg:{}", e.getCode(), e.getMessage(), e);
                        return 0;
                    }
                    log.error("addPropsUsed bizFailed code:{} msg:{}", e.getCode(), e.getMessage(), e);
                    status.setRollbackOnly();
                    return -1;
                } catch (TException e) {
                    log.error("addPropsUsed fail:", e);
                    status.setRollbackOnly();
                    return -1;
                }
            } else {
                // 领取模式
                try {
                    hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(),
                            attr.getBuisId(), uid, attr.getTAwardTskId(), 1, attr.getTAwardPkgId(), seq, Collections.emptyMap());
                } catch (Exception e) {
                    log.error("trySendMeetGift doWelfareV2 fail:", e);
                    status.setRollbackOnly();
                    return -1;
                }
            }
            return 0;
        });
    }

    private AddPropsUsedReq buildAddPropsUsedReq(MeetGiftComponentAttr attr,
                                                 String seqId,
                                                 long sendUid,
                                                 long recvUid,
                                                 long sid, long ssid,
                                                 int usedChannel,
                                                 String clientExpand) {
        Map<Long, WebdbUserInfo> userInfoMap = commonService.batchYyUserInfo(List.of(sendUid, recvUid));
        AddPropsUsedReq req = new AddPropsUsedReq();
        req.setSeqId(seqId);
        req.setPropId(attr.getPropsId());
        req.setActivityId(attr.getPropsActivityId());
        req.setSid(sid);
        req.setSsid(ssid);
        req.setBroadcastPropsUsedMessage(true);
        req.setAddType(2);
        req.setCount(1);

        TYyInfo user = new TYyInfo();
        user.setUid(sendUid);
        user.setUsedChannel(usedChannel);
        var u = userInfoMap.get(user.getUid());
        if (u != null) {
            user.setYyId(StringUtils.isNumeric(u.getYyno()) ? Long.parseLong(u.getYyno()) : 0L);
            user.setYyName(u.getNick());
        }
        TYyInfo recv = new TYyInfo();
        recv.setUid(recvUid);
        recv.setUsedChannel(usedChannel);
        u = userInfoMap.get(user.getUid());
        if (u != null) {
            recv.setYyId(StringUtils.isNumeric(u.getYyno()) ? Long.parseLong(u.getYyno()) : 0L);
            recv.setYyName(u.getNick());
        }

        req.setUserInfo(user);
        req.setReceiveInfo(recv);
        req.setClientExpand(clientExpand);

        return req;
    }
}
