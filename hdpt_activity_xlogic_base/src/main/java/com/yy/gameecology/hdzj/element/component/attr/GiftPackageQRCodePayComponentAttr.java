package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.ImmutableMap;
import com.yy.gameecology.hdzj.bean.TaskPackageInfo;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class GiftPackageQRCodePayComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private Long busiId;

    /**
     * 是否需要限制用户的购买资格
     */
    @ComponentAttrField(labelText = "限制购买资格")
    private boolean limitQualification = true;

    /**
     * 限制允许购买或者不允许购买
     */
    @ComponentAttrField(labelText = "允许购买")
    private boolean allow = true;

    /**
     * 存放限制用户名单的redis set key ,需要直接制定key的全称，不会再加前缀了
     */
    @ComponentAttrField(labelText = "限制用户名单缓存key", remark = "存放限制用户名单的redis set key ,需要直接制定key的全称，不会再加前缀了")
    private String limitUidRedisKey = "";
    /**
     * 被限制的提醒
     */
    @ComponentAttrField(labelText = "被限制的提醒")
    private String limitQualificationTip = "";

    /**
     * 限制用户购买数量，小于等于0是不限制
     */
    @ComponentAttrField(labelText = "限制购买数量", remark = "小于等于0是不限制")
    private int limitBuyCount = 1;
    /**
     * 购买数量限制提示
     */
    @ComponentAttrField(labelText = "购买数量限制提示")
    private String limitBuyCountTip = "";

    /**
     * 同个ip限制购买的秒数
     */
    @ComponentAttrField(labelText = "ip限制购买秒数")
    private long ipLimitSec = 60L;


    /**
     * 礼包扣费价格（以紫宝石单位，1:1000）
     */
    @ComponentAttrField(labelText = "礼包价格", remark = "礼包扣费价格（以紫宝石单位，1:1000）")
    private long price = 100;
    /**
     * 礼包详情，传给营收
     */
    @ComponentAttrField(labelText = "礼包详情", remark = "礼包详情，传给营收")
    private String giftDes = "gift desc";

    @ComponentAttrField(labelText = "总限制数量")
    private boolean globalLimitCount;

    /**
     * 支付网关 支付方式
     * ZfbQrCode2(41)
     * ZfbWap(7)
     * WeiXinQrCode(12)
     */
    @ComponentAttrField(labelText = "支付方式", remark = "ZfbQrCode2(41),ZfbWap(7),WeiXinQrCode(12)",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "支付方式"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "方式描述")
            })
    private Map<String, String> payWay = ImmutableMap.of("41", "支付宝", "12", "微信支付");

    /**
     * 2.交友 14.约战 36.宝贝
     */
    @ComponentAttrField(labelText = "营收AppId", remark = "2.交友 14.约战 36.宝贝", dropDownSourceBeanClass = TurnOverAppIdSource.class)
    private int turnOverAppId = 0;
    /**
     * 每次活动不一样，找吴熙要
     */
    @ComponentAttrField(labelText = "营收actionId", remark = "每次活动不一样，找吴熙要")
    private int turnOverActionId = 57;

    @ComponentAttrField(labelText = "营收渠道类型")
    private int turnOverChannelType = 1;

    /**
     * 4==充值
     */
    @ComponentAttrField(labelText = "营收支付类型", remark = "4==充值")
    private int turnOverPayKeyType = 4;

    /**
     * 中台发奖奖品
     **/
    @ComponentAttrField(labelText = "奖品配置",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = TaskPackageInfo.class)})
    private List<TaskPackageInfo> awards = Collections.emptyList();

    /**
     * 中台抽奖奖品
     **/
    @ComponentAttrField(labelText = "抽奖奖池id")
    private Long lotteryTaskId;

    /**
     * 中台 抽奖抽中的礼包id
     */
    @ComponentAttrField(labelText = "抽奖奖包id")
    private long bingoPackageId = 253;

    /**
     * 抽中礼物个数
     */
    @ComponentAttrField(labelText = "抽中礼物个数")
    private int bingoItemCount = 2;


}
