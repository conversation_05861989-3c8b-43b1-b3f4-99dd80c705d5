package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.mapper.gameecology.cmpt.Cmpt2066MemContribMapper;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2066MemContribTrans;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-26 15:36
 **/
@Repository
public class Cmpt2066MemDao {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private GameecologyDao gameecologyDao;

    @Autowired
    private Cmpt2066MemContribMapper cmpt2066MemContribMapper;


    @Transactional(rollbackFor = Exception.class)
    public int addScore(String seq, long actId, long cmptIndex, String member, String contribute, long score) {

        Cmpt2066MemContribTrans trans = new Cmpt2066MemContribTrans();
        trans.setActId(actId);
        trans.setCmptUseInx(cmptIndex);
        trans.setSeq(seq);
        long exist = gameecologyDao.count(Cmpt2066MemContribTrans.class, trans, "", Cmpt2066MemContribTrans.getTableName(actId));
        if (exist > 0) {
            log.warn("trans exist,actId:{},member:{},contribute:{},seq:{},score:{}", actId, member, contribute, seq, score);
            return 0;
        }

        trans.setScore(score);
        trans.setMember(member);
        trans.setContribte(contribute);
        trans.setCreateTime(new Date());
        int tranRes = gameecologyDao.insert(Cmpt2066MemContribTrans.class, trans, Cmpt2066MemContribTrans.getTableName(actId));

        int res = cmpt2066MemContribMapper.insertOrUpdateCmpt2066MemContrib(actId, cmptIndex, member, contribute, score);
        log.info("trans add done,actId:{},member:{},contribute:{},seq:{},score:{},tranRes:{}", actId, member, contribute, seq, score, tranRes);
        return res;
    }


}
