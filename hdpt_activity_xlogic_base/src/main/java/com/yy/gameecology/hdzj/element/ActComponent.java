package com.yy.gameecology.hdzj.element;

import com.yy.gameecology.common.db.model.gameecology.HdzjComponent;
import com.yy.gameecology.hdzj.ComponentRequest;
import com.yy.gameecology.hdzj.bean.CommonPBOperateRequest;
import com.yy.gameecology.hdzj.bean.CommonPBOperateResp;
import com.yy.gameecology.hdzj.bean.ElementBaseInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 组件接口
 * <AUTHOR>
 * @date 2021/2/4 19:30
 */
public interface ActComponent<T extends ComponentAttr> {
    /**
     * 是否唯一的 1 使用索引，若ture则系统只认唯一的 cmptUseInx=1 属性配置，非1的被忽略
     */
    boolean isUniq1UseIndex();

    /**
     * 获取组件ID
     **/
    Long getComponentId();

    /**
     * 获取组件的活动使用情况（包括无效状态、已结束的活动等）
     **/
    Set<Long> getActivityIds();

    /**
     * 功能描述:获取组件的属性对象类
     */
    Class<T> getMyAttrClass();

    /**
     * 获取组件属性对象
     */
    T getComponentAttr(long actId, long cmptUseInx);

    /**
     * 当 actId 下有且仅有一个 cmptUseInx，则返回该 cmptUseInx 的属性对象，否则返回null
     */
    T getUniqueComponentAttr(long actId);

    T tryGetUniqueComponentAttr(long actId);

    /**
     * 返回 actId 下组件的所有 cmptUseInx 关联对象列表
     */
    List<T> getAllComponentAttrs(long actId);

    /**
     * 获取组件自己的配置
     **/
    HdzjComponent getHdzjComponent(long actId, long cmptUseInx);

    /**
     * 制作组件key（如用于redis key）生成的key含 actId + cmptId + cmptUseInx
     **/
    String makeKey(T componentAttr, String name);

    /**
     * 制作组件key（如用于redis key）生成的key含 actId + cmptId + cmptUseInx
     **/
    String makeKey(ComponentRequest<T> request, String name);

    /**
     * 制作组件key（如用于redis key）生成的key含 actId + cmptId + cmptUseInx
     **/
    String makeKey(long actId, long cmptUseInx, String name);

    /**
     * 获取UI相关配置 - ？包括其下面所有的任务、福利 UI数据打包返回
     **/
    Map<String, Object> getUiConfig(ComponentRequest<T> request);

    /**
     * 获取组件在 actId 下的基础信息
     **/
    ElementBaseInfo getElementBaseInfo(ComponentRequest<T> request);

    /**
     * 获取组件业务数据（供前端渲染） - ？包括其下面所有的任务、福利 业务数据打包返回
     **/
    Map<String, Object> getBusinessData(ComponentRequest<T> request);

    /**
     * 操作组件业务数据（用于和用户交互，也可内部系统调用。实现上要注意安全！）
     **/
    Map<String, Object> operate(ComponentRequest<T> request);

    /**
     * 处理通用客户端pb请求
     */
    default CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request){
        return null;
    }

    String getBeanName();
}
