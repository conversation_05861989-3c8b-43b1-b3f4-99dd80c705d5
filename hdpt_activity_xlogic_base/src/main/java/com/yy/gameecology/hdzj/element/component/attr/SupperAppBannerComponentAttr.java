package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022.10.20 10:21
 */
@Data
public class SupperAppBannerComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;

    @ComponentAttrField(labelText = "角色类型ID", dropDownSourceBeanClass = RoleTypeSource.class)
    private int roleId;

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    private int busiId;
    /**
     * 等级对应的分值描述,如一级为 7千万(前端需要中文表述) level对应中台的任务等级
     **/
    @ComponentAttrField(labelText = "等级对应的分值描述", remark = "如一级为 7千万(前端需要中文表述) level对应中台的任务等级", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级"),
            @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "分值描述")
    })
    private Map<Long, String> level2ScoreDesc;

    /**
     * 等级对应广播范围
     **/
    @ComponentAttrField(labelText = "等级对应广播范围", remark = "可不填,默认子频道广播, 1-子频道,2-顶频道, 3-全业务 4-全平台", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级"),
            @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "广播类型,可不填,默认子频道广播, 1-子频道,2-顶频道, 3-全业务 4-全平台")})
    private Map<Long, Integer> level2BroType;


    @ComponentAttrField(labelText = "等级对应svga配置", useDialog = 1,
            subFields = {@SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "等级"),
                    @SubField(fieldName = Constant.VALUE, type = AppBannerSvgaConfig.class, labelText = "配置")})
    private Map<Long, AppBannerSvgaConfig> level2svgaConfig;

    @Deprecated
    @ComponentAttrField(labelText = "横幅文案", remark = "已弃用,后续使用AppBannerSvgaConfig.contentLayers")
    private String context;

    /**
     * 需要推送的业务类型 1 -语音房 2 -交友房 4 -其他 8 -宝贝 位域表示 支持组合：交友and宝贝：2+8
     */
    @ComponentAttrField(labelText = "需要推送的业务",
            subFields = {@SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "等级"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "业务", remark = "1 -语音房 2 -交友房 4 -其他 8 -宝贝 位域表示 支持组合：交友and宝贝：2+8")})
    private Map<Long, Integer> level2business;

    @ComponentAttrField(labelText = "黑名单组件序号", remark = "默认0,不开启黑名单")
    private long blackListCmptUseIndex = 0;

    @ComponentAttrField(labelText = "昵称最长长度", remark = "默认5个")
    private int nameCountLimit = 5;


}
