package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.SignedService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.*;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.BroadcastType;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.db.model.gameecology.*;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2067AsyncWelfare;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpDiceLotteryComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.dao.CpDiceLottryDao;
import com.yy.gameecology.hdzj.element.component.dao.CpDiceLottryMockDao;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.java.webdb.WebdbUserInfo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@RequestMapping("/5155")
@RestController
@Component
public class CpDiceLotteryComponent extends BaseActComponent<CpDiceLotteryComponentAttr> {

    @Override
    public Long getComponentId() {
        return ComponentId.CP_DICE_LOTTERY;
    }

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    CpDiceLottryDao cpDiceLottryDao;

    @Autowired
    CpDiceLottryMockDao cpDiceLottryMockDao;

    @Autowired
    WebdbThriftClient webdbThriftClient;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private SignedService signedService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    private String LockName(long actId, long cnotIndex) {
        return String.format("CpDiceLotteryLock:%d:%d", actId, cnotIndex);
    }

    private String drawLockName(long actId, long cnotIndex, long uid, long anchorUid) {
        return String.format("CpdLock:%d:%d:%d:%d", actId, cnotIndex, uid, anchorUid);
    }

    public static final String DRAW_PREFIX = "d5155_%d_%d";
    public static final String FIRST_TICKET_NOTICE = "_get_first_pop";

    public static final String REWARD_STATICS_REWARD_KEY = "cp_reward";
    public static final String DICE_KEY = "dice_count";
    public static final String REWARD_STATICS_JOIN_KEY = "cp_join_count";
    public static final String TICKET_HASH_KEY = "dice_first_notice";

    public static final String HASH_TIP_USE = "use";

    private static final Random RANDOM = new Random();

    public static final int LOCK_TTL = 12;

    private static final long CP_DICE_LOTTERY_BRO_BANNER_ID = 5155001;

    public static final String DRAW_NOTICE_TYPE = "cp_lottery_5155";

    private static final DecimalFormat FORMATTER = new DecimalFormat("00.00%");

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, CpDiceLotteryComponentAttr attr) {
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }
        long startTime = System.nanoTime();
        CpUid cpUid = Const.splitCpMember(event.getMember());
        long uid = cpUid.getUserUid();
        long anchorUid = cpUid.getAnchorUid();
        long diceCount = Convert.toInt(event.getItemScore());
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        Secret secret = null;
        String lockKey = LockName(attr.getActId(), attr.getCmptUseInx());

        if (uid == 0 || anchorUid == 0 || diceCount == 0) {
            log.error("get zero value {} {} {} {}",event,uid,anchorUid,diceCount);
            return;
        }
        try {
            secret = locker.lock(lockKey, 7, "", 10, 100);
            if (secret == null) {
                throw new SuperException("网络超时，请重试", SuperException.E_DATA_ERROR);
            }
            updateUserLotteryStateRetry(attr, uid, anchorUid, diceCount, seq);
        } catch (Exception e) {
            log.error("onRankingScoreChanged error:{}", e.getMessage(), e);
            throw e;
        } finally {
            if (secret != null) {
                locker.unlock(lockKey, secret);
            }
            log.info("ms: {}" ,(System.nanoTime() - startTime) / 1_000_000.0);
        }
    }

    public void updateUserLotteryStateRetry(CpDiceLotteryComponentAttr attr, long uid, long anchorUid, long count, String seq) {
//        int retry = 2;
//        for (int i = 0; i < retry ; i++) {
//            try {
//                UpdateUserLotteryState(attr,uid,anchorUid,count,seq);
//                break;
//            } catch (Exception e) {
//                log.error("updateUserLotteryStateRetry error:{}", e.getMessage(), e);
//            }
//        }
        updateUserLotteryState(attr, uid, anchorUid, count, seq);
    }

    public long updateUserLotteryState(CpDiceLotteryComponentAttr attr, long uid, long anchorUid, long count, String seq) {
        CpDiceLottryDao.CpLotteryTicketInfo addInfo = new CpDiceLottryDao.CpLotteryTicketInfo();
        CpDiceLotteryTicket userTicket = cpDiceLottryDao.getTicket(attr.getActId(), attr.getCmptUseInx(), uid, anchorUid);
        log.info("myTicket uid:{} anchorUid:{} userTicket:{}", uid, anchorUid, userTicket);
        if (userTicket.getTotalCount() >= attr.getMaxTicketCount()) {
            log.info("reach max ticket {} {}", uid, userTicket.getTotalCount());
            return 0;
        }
        addInfo.setBeforeTicket(userTicket.getTotalCount());
        addInfo.setAddTicket(Math.min(count, attr.getMaxTicketCount() - userTicket.getTotalCount()));
        addInfo.setAfterTicket(userTicket.getTotalCount() + addInfo.getAddTicket());
        CpDiceLotteryTicket svTicketInfo = cpDiceLottryDao.getTicket(attr.getActId(), attr.getCmptUseInx(), 0, 0);
        log.info("svTicket userTicket:{}", svTicketInfo);
        long oldsSvTicket = svTicketInfo.getTotalCount();
        long newSvTicket = svTicketInfo.getTotalCount() + addInfo.getAddTicket();
        Map<Long, Long> restrictNowMap = getRestrictNowCount(attr);
        Map<Long, Long> restrictAddMap = new HashMap<>();
        for (Map.Entry<Long, CpDiceLotteryComponentAttr.RestrictGiftInfo> entry : attr.getStockReward().entrySet()) {
            Long packageId = entry.getKey();
            if (restrictNowMap.get(packageId) >= entry.getValue().getUpLimit()) {
                log.info("over limit packageId:{} nowCount:{} configCount:{}", packageId, restrictNowMap.get(packageId), entry.getValue().getUpLimit());
                continue;
            }
            CpDiceLotteryComponentAttr.RestrictGiftInfo value = entry.getValue();
            long addValue = newSvTicket / value.getNeedTicket() - oldsSvTicket / value.getNeedTicket();
            addValue = Math.min(addValue, value.getUpLimit() - restrictNowMap.get(packageId));
            if (addValue > 0) {
                restrictAddMap.put(packageId, addValue);
            }
        }
        addInfo.setStockMap(restrictAddMap);
        log.info("add ticket uid:{} anchorUid:{} addInfo:{}", uid, anchorUid, addInfo);
        cpDiceLottryDao.UpdateUserTicketAndSvStock(attr.getActId(), attr.getCmptUseInx(), uid, anchorUid, seq, addInfo);
        if (userTicket.getTotalCount() == 0) {
            log.info("first get ticket notice {}", userTicket);
            JSONObject noticeExt = new JSONObject();
            noticeExt.put("uid", uid);
            noticeExt.put("anchorUid", anchorUid);
            Const.EXECUTOR_DELAY_GENERAL.schedule(
                    () -> {
                        if (isNotice(attr, uid)) {
                            commonBroadCastService.commonNoticeUnicast(attr.getActId(), getComponentId() + FIRST_TICKET_NOTICE, JSON.toJSONString(noticeExt), StringUtils.EMPTY
                                    , uid);
                        }
                        if (isNotice(attr, anchorUid)) {
                            commonBroadCastService.commonNoticeUnicast(attr.getActId(), getComponentId() + FIRST_TICKET_NOTICE, JSON.toJSONString(noticeExt), StringUtils.EMPTY
                                    , anchorUid);
                        }
                    }, 2, TimeUnit.SECONDS);

        }
        return addInfo.getAddTicket();
    }

    private boolean isNotice(CpDiceLotteryComponentAttr attr, long uid) {
        Date now = new Date();
        return commonDataDao.hashValueSetNX(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), TICKET_HASH_KEY, Convert.toString(uid), DateUtil.format(now, DateUtil.PATTERN_TYPE1));
    }


    private Map<Long, Long> getRestrictNowCount(CpDiceLotteryComponentAttr attr) {
        Map<Long, Long> restrictAddMap = new HashMap<>();
        for (Map.Entry<Long, CpDiceLotteryComponentAttr.RestrictGiftInfo> entry : attr.getStockReward().entrySet()) {
            restrictAddMap.put(entry.getKey(), 0L);
        }
        List<CpDiceLotteryRestrictRewardInfo> restrictRewwardList = cpDiceLottryDao.getRestrictRewardInfo(attr.getActId(), attr.getCmptUseInx());
        for (int i = 0; i < restrictRewwardList.size(); i++) {
            if (restrictRewwardList.get(i) != null) {
                restrictAddMap.put(restrictRewwardList.get(i).getRewardPackageId(), restrictRewwardList.get(i).getTotalCount());
            }
        }
        log.info("restrictAddMap:{}", restrictAddMap);
        return restrictAddMap;
    }

    public Map<Long, Long> getRestrictBalanceCount(CpDiceLotteryComponentAttr attr) {
        Map<Long, Long> restrictAddMap = new HashMap<>();
        for (Map.Entry<Long, CpDiceLotteryComponentAttr.RestrictGiftInfo> entry : attr.getStockReward().entrySet()) {
            restrictAddMap.put(entry.getKey(), 0L);
        }
        List<CpDiceLotteryRestrictRewardInfo> restrictRewwardList = cpDiceLottryDao.getRestrictRewardInfo(attr.getActId(), attr.getCmptUseInx());
        for (int i = 0; i < restrictRewwardList.size(); i++) {
            if (restrictRewwardList.get(i) != null) {
                restrictAddMap.put(restrictRewwardList.get(i).getRewardPackageId(), restrictRewwardList.get(i).getBalance());
            }
        }
        return restrictAddMap;
    }

     @RequestMapping("/addDiceTool")
    public Response mockData(HttpServletRequest request, HttpServletResponse response,long actId, long cmptInx, long uid,
                         long anchorUid, long diceCount, String seq) {
         long loginYYUid = getLoginYYUid(request, response);
         if (!isUIDWorkAccount(loginYYUid)) {
             log.warn("not staff uid {}", loginYYUid);
             return Response.fail(400,"not staff uid");
         }
         long startTime = System.currentTimeMillis();
         Secret secret = null;
         var attr = getUniqueComponentAttr(actId);
         if (attr == null) {
             return Response.fail(400,"attr nil");
         }
         String lockKey = LockName(attr.getActId(), attr.getCmptUseInx());

         if (uid == 0 || anchorUid == 0 || diceCount == 0) {
             log.error("get zero value {} {} {}",uid,anchorUid,diceCount);
             return Response.fail(400,"uid = 0");
         }
         try {
             secret = locker.lock(lockKey, 7, "", 10, 100);
             if (secret == null) {
                 throw new SuperException("网络超时，请重试", SuperException.E_DATA_ERROR);
             }
             updateUserDice(attr, uid, anchorUid, diceCount, seq);
         } catch (Exception e) {
             log.error("onRankingScoreChanged error:{}", e.getMessage(), e);
             throw e;
         } finally {
             if (secret != null) {
                 locker.unlock(lockKey, secret);
             }
         }
        log.info("mockData: start {}", actId);
         return Response.ok();
    }

    //    @RequestMapping("/mockData")
//    public void mockData(long actId, long cmptInx) {
//        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> processMock(actId));
//        log.info("mockData: start {}", actId);
//    }

    public long updateUserDice(CpDiceLotteryComponentAttr attr, long uid, long anchorUid, long count, String seq) {
        CpDiceLottryDao.CpLotteryTicketInfo addInfo = new CpDiceLottryDao.CpLotteryTicketInfo();
        CpDiceLotteryTicket userTicket = cpDiceLottryDao.getTicket(attr.getActId(), attr.getCmptUseInx(), uid, anchorUid);
        log.info("myTicket uid:{} anchorUid:{} userTicket:{}", uid, anchorUid, userTicket);
        if (userTicket.getTotalCount() >= attr.getMaxTicketCount()) {
            log.info("reach max ticket {} {}", uid, userTicket.getTotalCount());
            return 0;
        }
        addInfo.setBeforeTicket(userTicket.getTotalCount());
        addInfo.setAddTicket(Math.min(count, attr.getMaxTicketCount() - userTicket.getTotalCount()));
        addInfo.setAfterTicket(userTicket.getTotalCount() + addInfo.getAddTicket());
        CpDiceLotteryTicket svTicketInfo = cpDiceLottryDao.getTicket(attr.getActId(), attr.getCmptUseInx(), 0, 0);
        log.info("svTicket userTicket:{}", svTicketInfo);
        long oldsSvTicket = svTicketInfo.getTotalCount();
        long newSvTicket = svTicketInfo.getTotalCount() + addInfo.getAddTicket();
        Map<Long, Long> restrictNowMap = getRestrictNowCount(attr);
        Map<Long, Long> restrictAddMap = new HashMap<>();
        for (Map.Entry<Long, CpDiceLotteryComponentAttr.RestrictGiftInfo> entry : attr.getStockReward().entrySet()) {
            Long packageId = entry.getKey();
            if (restrictNowMap.get(packageId) >= entry.getValue().getUpLimit()) {
                log.info("over limit packageId:{} nowCount:{} configCount:{}", packageId, restrictNowMap.get(packageId), entry.getValue().getUpLimit());
                continue;
            }
            CpDiceLotteryComponentAttr.RestrictGiftInfo value = entry.getValue();
            long addValue = newSvTicket / value.getNeedTicket() - oldsSvTicket / value.getNeedTicket();
            addValue = Math.min(addValue, value.getUpLimit() - restrictNowMap.get(packageId));
            if (addValue > 0) {
                restrictAddMap.put(packageId, addValue);
            }
        }
        addInfo.setStockMap(restrictAddMap);
        log.info("add ticket uid:{} anchorUid:{} addInfo:{}", uid, anchorUid, addInfo);
        cpDiceLottryDao.UpdateUserTicketAndSvStock(attr.getActId(), attr.getCmptUseInx(), uid, anchorUid, seq, addInfo);
        return addInfo.getAddTicket();
    }

    public void processMock(long actId) {
        long now = System.nanoTime();
        var attr = getUniqueComponentAttr(actId);
        long startId = 0;
        long pageSize = 500;
        long times = 0;
        List<Cmpt5155MockData> cmpt5155MockDataList = cpDiceLottryMockDao.selectNockDatas(startId, pageSize);
        while (CollectionUtils.isNotEmpty(cmpt5155MockDataList)) {
            startId = cmpt5155MockDataList.getLast().getIdx();
            log.info("startId:{}", startId);
            for (int i = 0; i < cmpt5155MockDataList.size(); i++) {
                var csvInfo = cmpt5155MockDataList.get(i);
                long count = csvInfo.getAmount() / 131400;
                if (count == 0) {
                    continue;
                }
                String seq = String.format("m%d_%d", csvInfo.getUid(), csvInfo.getIdx());
                long ticket = updateUserLotteryState(attr, csvInfo.getUid(), csvInfo.getAnchorId(), count, seq);
                Cmpt5155MockTicketResult result = new Cmpt5155MockTicketResult();
                result.setSeq(seq);
                result.setUid(csvInfo.getUid());
                result.setAnchorUid(csvInfo.getAnchorId());
                result.setAmount(csvInfo.getAmount());
                result.setTicketOut(Convert.toInt(ticket));
                result.setDatestr(csvInfo.getDatestr());
                cpDiceLottryMockDao.addTicketRecord(result);
                while (true) {
                    CpDiceLotteryTicket userTicket = cpDiceLottryDao.getTicket(attr.getActId(), attr.getCmptUseInx(), csvInfo.getUid(), csvInfo.getAnchorId());
                    if (userTicket.getBalance() == 0) {
                        break;
                    }
                    CpDiceLotteryInfo drawInfo = cpDiceLottryDao.getDrawInfo(attr.getActId(), attr.getCmptUseInx(), csvInfo.getUid(), csvInfo.getAnchorId());
                    if (drawInfo.getStep() == 0) {
                        cpDiceLottryDao.userClick(attr.getActId(), 810, csvInfo.getUid(), csvInfo.getAnchorId());
                    }
                    long draw = Math.min(userTicket.getBalance(), attr.getMaxConsume());
                    var ret = continuousDraw(attr, drawInfo, draw, userTicket.getBalance());
                    log.info("idx:{} uid:{} anchorUid:{] retList:{}", startId, csvInfo.getUid(), csvInfo.getAnchorId(), ret.getRewardList());
                    List<Cmpt5155MockResult> l = new ArrayList<>(ret.getRewardList().size());
                    var retList = ret.getRewardList();
                    for (int j = 0; j < retList.size(); j++) {
                        Cmpt5155MockResult r = new Cmpt5155MockResult();
                        r.setUid(csvInfo.getUid());
                        r.setAnchorUid(csvInfo.getAnchorId());
                        r.setGroupKey(seq);
                        r.setStep(Convert.toInt(retList.get(j).getStep()));
                        r.setRewardId(Convert.toInt(retList.get(j).getRewardInfo().getTAwardPkgId()));
                        r.setRewardName(retList.get(j).getRewardInfo().getAwardName());
                        r.setDatestr(csvInfo.getDatestr());
                        r.setRewardAmount(retList.get(j).getRewardInfo().getAwardAmount() * 2);
                        l.add(r);
                    }
                    cpDiceLottryMockDao.addLotteryRecord(l);
                }
            }
//            if (times >=  5 ) {
//                break;
//            }
//            times = times + 1;
            cmpt5155MockDataList = cpDiceLottryMockDao.selectNockDatas(startId, pageSize);
        }
        long duration = System.nanoTime() - now;
        log.info("finish {} {}", duration, duration / 1_000_000.0);
    }

    public boolean inActTime(CpDiceLotteryComponentAttr attr,Long actId) {
        Date now = commonService.getNow(actId);
        return now.getTime() >= attr.getBeginTime().getTime() && now.getTime() < attr.getEndTime().getTime();
    }

    public boolean befireActTime(CpDiceLotteryComponentAttr attr,Long actId) {
        Date now = commonService.getNow(actId);
        return now.getTime() < attr.getBeginTime().getTime() ;
    }

    public boolean afterctTime(CpDiceLotteryComponentAttr attr,Long actId) {
        Date now = commonService.getNow(actId);
        return now.getTime() >= attr.getEndTime().getTime() ;
    }

    @RequestMapping("/draw")
    public Response<DrawRsp> draw(HttpServletRequest request, HttpServletResponse response,
                                  long actId, long comIndex, long uid, long anchorUid) {
        CpDiceLotteryComponentAttr attr = getComponentAttr(actId, comIndex);
        if (attr == null) {
            return Response.fail(404, "活动未配置");
        }
        if (befireActTime(attr, actId)) {
            return Response.fail(400, "活动未开始");
        }

        if (afterctTime(attr, actId)) {
            return Response.fail(400, "活动已结束");
        }
        long loginUid = getLoginYYUid(request, response);
        if (loginUid <= 0L) {
            return Response.fail(400, "未登陆");
        }
//        if (SysEvHelper.isHistory()) {
//            return Response.fail(400, "活动已结束!");
//        }


        if (loginUid != uid && loginUid != anchorUid) {
            log.info("uid:{}, loginUid:{}, anchorUid:{}", uid, loginUid, anchorUid);
            return Response.fail(400, "参数错误");
        }
        if (uid == 0 || anchorUid == 0) {
            log.info("uid:{}, loginUid:{}, anchorUid:{}", uid, loginUid, anchorUid);
            return Response.fail(400, "参数错误");
        }

//        if (!inActTime(attr,actId)) {
//            return Response.fail(400, "活动已结束");
//        }


        DrawRsp rsp = new DrawRsp();
        //lock
        String lockKey = drawLockName(actId, comIndex, uid, anchorUid);
        Secret secret = locker.lock(lockKey, LOCK_TTL);
        if (secret == null) {
            return Response.fail(400, "您的操作太快了, 请稍后重试");
        }

        try {
            CpDiceLotteryTicket userTicket = cpDiceLottryDao.getTicket(attr.getActId(), attr.getCmptUseInx(), uid, anchorUid);
            if (userTicket.getTotalCount() == 0) {
                return Response.fail(400, "当前骰子不足，一次性赠送131.4元活动礼物可获骰子哦~");
            }
            if (userTicket.getBalance() == 0) {
                if (userTicket.getTotalCount() == attr.getMaxTicketCount()) {
                    return Response.fail(400, "骰子已用完限额");
                }
                return Response.fail(400, "当前骰子不足，一次性赠送131.4元活动礼物可获骰子哦~");
            }
            CpDiceLotteryInfo drawInfo = cpDiceLottryDao.getDrawInfo(actId, comIndex, uid, anchorUid);
            long click = updateUserClick(attr, drawInfo, loginUid);
            if (click <= 1) {
                rsp.setDraw(false);
                rsp.setTotalCount(userTicket.getTotalCount());
                rsp.setBalance(userTicket.getBalance());
                return Response.success(rsp);
            }
//            if(userTicket.getBalance().longValue() <= 0) {
//                return Response.fail(400, "骰子不足，一次性赠送131.4元礼物可获骰子哦~");
//            }
            if (click >= 2) {
                //抽奖 发广播
                long draw = Math.min(userTicket.getBalance(), attr.getMaxConsume());
                try {
                    log.info("ContinuousDraw uid:{] lotteryInfo:{} draw:{}", loginUid, userTicket, draw);
                    DrawRsp rspRet = continuousDraw(attr, drawInfo, draw, userTicket.getBalance());
                    if (rspRet == null || rspRet.getRewardList() == null || rspRet.getRewardList().size() == 0) {
                        return Response.fail(400, "服务器繁忙, 请稍后重试");
                    } else {
                        rsp.setDraw(true);
                        rsp.setTotalCount(userTicket.getTotalCount());
                        rsp.setBalance(userTicket.getBalance() - draw);
                        rsp.setRewardList(rspRet.getRewardList());
                        rsp.setStartStep(rspRet.getStartStep());
//                        ret = Response.success(rsp);
                        settleAwardBro(attr, uid, anchorUid, loginUid, rsp);
                        updateStaticsData(attr, rsp.getRewardList(), draw);
                        return Response.success(rsp);
                    }
                } catch (Exception e) {
                    log.error("award draw uid:{} anchorUid:{} e:{}", uid, anchorUid, e.getMessage(), e);
                    return Response.fail(400, "服务器繁忙, 请稍后重试");
                }
            }
        } catch (Exception e) {
            log.error("cpLotteryComponent draw uid:{} anchorUid:{} e:{}", uid, anchorUid, e.getMessage(), e);
        } finally {
            locker.unlock(lockKey, secret);
        }
        return Response.success(rsp);
    }

    public void settleAwardBro(CpDiceLotteryComponentAttr attr, long bossUid, long anchorUid, long loginUid, DrawRsp rsp) {
        log.info("settleHourRankAwardBro bossUid:{} anchorUid:{} ", bossUid, anchorUid);
        Map<String, Object> msg = ImmutableMap.of(
                "awardList", rsp.getRewardList(),
                "uid", bossUid,
                "anchorUid", anchorUid,
                "balance",rsp.getBalance(),
                "startStep",rsp.getStartStep());
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), DRAW_NOTICE_TYPE, JsonUtil.toJson(msg), StringUtils.EMPTY, bossUid == loginUid ? anchorUid : bossUid);


        Map<Long, Long> bigRewardMap = new HashMap<>();
        for (String part : attr.getBigRewardStr().split(",")) {
            long num = Integer.parseInt(part);
            bigRewardMap.put(num, num);
        }

        List<AwardAttrConfig> bigRewarList = new ArrayList<>();
        for (int i = 0; i < rsp.getRewardList().size(); i++) {
            AwardAttrConfig rewardInfo = rsp.getRewardList().get(i).getRewardInfo();
            if (bigRewardMap.containsKey(rewardInfo.getTAwardPkgId())) {
                bigRewarList.add(rewardInfo);
            }
        }
        if (bigRewarList.size() == 0) {
            return;
        }

        //---pc 广播横幅[全频道广播]
        Set<Long> uids = Sets.newHashSet(bossUid, anchorUid);
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), multiNickUsers, true, attr.getTemplateType());

        Map<String, Object> ext = Maps.newHashMap();
        UserInfoVo userInfoVo = userInfos.get(bossUid);
        UserInfoVo babyInfoVo = userInfos.get(anchorUid);
        ext.put("userUid", bossUid);
        ext.put("babyUid", anchorUid);
        if (userInfoVo != null) {
            ext.put("userLogo", userInfoVo.getAvatarUrl());
            ext.put("userNick", userInfoVo.getNick());
        }
        if (babyInfoVo != null) {
            ext.put("babyLogo", babyInfoVo.getAvatarUrl());
            ext.put("babyNick", babyInfoVo.getNick());
        }

        ext.put("nickExtUsers", JsonUtil.toJson(multiNickUsers));

        ChannelInfoVo anchorUidOnlineInfo = getUserOnlineInfo(anchorUid);
        if (anchorUidOnlineInfo.getSid() > 0) {
            ext.put("sid", anchorUidOnlineInfo.getSid());
            ext.put("ssid", anchorUidOnlineInfo.getSsid());
        }

        for (int i = 0; i < bigRewarList.size(); i++) {
            ext.put("giftName", bigRewarList.get(i).getAwardName());
            ext.put("giftIcon", bigRewarList.get(i).getAwardIcon());
            ext.put("giftUnit", bigRewarList.get(i).getUnit());
            if (StringUtil.isNotBlank(bigRewarList.get(i).getExtJson())) {
                JSONObject jsonObject = JSON.parseObject(bigRewarList.get(i).getExtJson());
                ext.put("giftCount", jsonObject.getIntValue("count"));
            }
//           log.info("settleHourRankAwardBro {}",ext);
            commonBroadCastService.commonBannerBroadcast(0, 0, 0, com.yy.thrift.broadcast.Template.findByValue(attr.getBroTemplate()), BroadcastType.ALL_TEMPLATE
                    , attr.getActId(), 0L, 0L, CP_DICE_LOTTERY_BRO_BANNER_ID, 0L, ext);
        }

    }

    private ChannelInfoVo getUserOnlineInfo(long uid) {
        ChannelInfoVo channelInfoVo = new ChannelInfoVo();
        UserCurrentChannel bossUidChannel = commonService.getNoCacheUserCurrentChannel(uid, 1);
        log.info("getUserOnlineInfo uid:{},bossUidChannel:{}", uid, bossUidChannel);
        if (bossUidChannel != null && bossUidChannel.getSubsid() > 0 && bossUidChannel.getTopsid() > 0) {
            log.info("getUserOnlineInfo uid:{},bossUidChannel:{}", uid, bossUidChannel.getTopsid());
            channelInfoVo.setSid(bossUidChannel.getTopsid());
            channelInfoVo.setSsid(bossUidChannel.getSubsid());
            return channelInfoVo;
        }
        channelInfoVo.setSid(0);
        channelInfoVo.setSsid(0);
        return channelInfoVo;
    }


    private long updateUserClick(CpDiceLotteryComponentAttr attr, CpDiceLotteryInfo drawInfo, long loginUid) {
        boolean idUser = (loginUid == drawInfo.getUid().longValue());
        long click = drawInfo.getUidClick() + drawInfo.getAnchorClick();
        if (idUser) {
            if (Objects.equals(drawInfo.getUidClick(), 0L)) {
                click++;
            }
        } else {
            if (Objects.equals(drawInfo.getAnchorClick(), 0L)) {
                click++;
            }
        }
        if (click == 1) {
            if (idUser && Objects.equals(drawInfo.getUidClick(), 0L)) {
                cpDiceLottryDao.userClick(attr.getActId(), attr.getCmptUseInx(), drawInfo.getUid(), drawInfo.getAnchorUid());
            }
            if (!idUser && Objects.equals(drawInfo.getAnchorClick(), 0L)) {
                cpDiceLottryDao.anchorClick(attr.getActId(), attr.getCmptUseInx(), drawInfo.getUid(), drawInfo.getAnchorUid());
            }
        }
        return click;
    }

    public DrawRsp continuousDraw(CpDiceLotteryComponentAttr attr, CpDiceLotteryInfo lotteryInfo, long draw, long balance) {
//        long startTime = System.nanoTime();
        DrawRsp drawRsp = new DrawRsp();
        int retry = 3;
        Date d = new Date();
        // boolean testUpLimit = false;
        String seq = String.format(DRAW_PREFIX, lotteryInfo.getUid(), d.getTime());
        for (int i = 0; i < retry; i++) {
            List<DiceRet> retList = new ArrayList<>((int) draw);
            Map<Long, Long> restrictInfo = getRestrictBalanceCount(attr);
            Map<Long, Long> deductMap = new HashMap<>();
            long start = lotteryInfo.getStep() == 0 ? 1 : lotteryInfo.getStep();
            long rewardAmount = lotteryInfo.getRewardValue();
            long oldRewardAmount = rewardAmount;
            long nextStep = start;
            try {
                for (int j = 0; j < draw; j++) {
                  DiceRet dice = getLotteryAward(attr, nextStep, restrictInfo, rewardAmount);
                    AwardAttrConfig attrConfig = dice.getRewardInfo();
                    retList.add(dice);
                    rewardAmount = rewardAmount + attrConfig.getAwardAmount() * 2;
                    if (attrConfig.getAwardAmount() > 0) {
                        restrictInfo.computeIfPresent(attrConfig.getTAwardPkgId(), (k, v) -> v - attrConfig.getNum());
                        deductMap.compute(attrConfig.getTAwardPkgId(), (k, v) -> v == null ? attrConfig.getNum() : v + attrConfig.getNum());
                    }
                    nextStep = dice.getStep();
                    log.info("j:{} result:{} nextStep:{} deductMap:{} restrictInfo:{} rewardAmount:{}", j, dice, nextStep, deductMap, restrictInfo, rewardAmount);
                }
                CpDiceLottryDao.CpLotteryUserTicketInfo writeInfo = new CpDiceLottryDao.CpLotteryUserTicketInfo();
//                writeInfo.setUid(lotteryInfo.getUid());
//                writeInfo.setAnchorUid(lotteryInfo.getAnchorUid());
                writeInfo.setStep(nextStep);
                writeInfo.setRewardAmount(rewardAmount);
                writeInfo.setDeductMap(deductMap);
                writeInfo.setUseTicket(draw);
                List<Cmpt2067AsyncWelfare> l = conRewardRecordList(attr, retList, lotteryInfo.getUid(), lotteryInfo.getAnchorUid(), seq);
                CpDiceLotteryFlow flows = conDrawRecordList(attr, start, draw, retList, lotteryInfo.getUid(), lotteryInfo.getAnchorUid(), rewardAmount - oldRewardAmount, balance, seq);
                log.info("seq:{} writeInfo:{}", seq, writeInfo);
                cpDiceLottryDao.userDraw(attr.getActId(), attr.getCmptUseInx(), lotteryInfo.getUid(), lotteryInfo.getAnchorUid(), seq, writeInfo, l, flows);
                drawRsp.setRewardList(retList);
                drawRsp.setStartStep(start);
                return drawRsp;
            } catch (Exception e) {
                log.error("cpLotteryComponent draw uid:{} anchorUid:{} e:{}", lotteryInfo.getUid(), lotteryInfo.getAnchorUid(), e.getMessage(), e);
            }
        }
        return null;
    }

    private List<Cmpt2067AsyncWelfare> conRewardRecordList(CpDiceLotteryComponentAttr attr, List<DiceRet> retList, long uid, long anchorUid, String seq) {
        long startTime = System.nanoTime();
        List<Cmpt2067AsyncWelfare> recordList = new ArrayList<>();
        String cpMember = String.format("%d|%d",uid,anchorUid);
        for (int i = 0; i < retList.size(); i++) {
            Cmpt2067AsyncWelfare w1 = getWelfareRecord(attr, retList.get(i), uid, anchorUid, String.format("%s_u%d", seq, i), String.format("%s_%d", seq, i),cpMember);
            Cmpt2067AsyncWelfare w2 = getWelfareRecord(attr, retList.get(i), anchorUid, uid, String.format("%s_a%d", seq, i), String.format("%s_%d", seq, i),cpMember);
            recordList.add(w1);
            recordList.add(w2);
        }
        long duration = System.nanoTime() - startTime;
        log.info("getLotteryAward use draw duration： {} {}", duration, duration / 1_000_000.0);
        return recordList;
    }

    private CpDiceLotteryFlow conDrawRecordList(CpDiceLotteryComponentAttr attr, long start, long draw, List<DiceRet> retList, long uid, long anchorUid,
                                                long rewardValue, long balance, String seq) {
        CpDiceLotteryFlow flow = new CpDiceLotteryFlow();
        long startStep = start;
        flow.setSeq(seq);
        flow.setActId(attr.getActId());
        flow.setCmptUseInx(attr.getCmptUseInx());
        flow.setUid(uid);
        flow.setAnchorUid(anchorUid);
        flow.setStepOld(startStep);
        flow.setStep(retList.getLast().getStep());
        flow.setTicketOld(balance);
        flow.setTicket(balance - draw);
        flow.setUseDraw(draw);
        flow.setRewardValue(rewardValue);
        flow.setCreateTime(new Date());

        StringBuilder diceSerial = new StringBuilder();
        StringBuilder stepSerial = new StringBuilder().append(start).append(",");

        for (int i = 0; i < retList.size(); i++) {
            stepSerial.append(retList.get(i).getStep());
            diceSerial.append(retList.get(i).getDicePoint());
            if (i < retList.size() - 1) {
                stepSerial.append(",");
                diceSerial.append(",");
            }
        }
        flow.setDicePointSerial(diceSerial.toString());
        flow.setStepSerial(stepSerial.toString());
        return flow;
    }

    private Cmpt2067AsyncWelfare getWelfareRecord(CpDiceLotteryComponentAttr attr, DiceRet r, long uid, long otherUid, String seq, String exSeq, String cpMember) {
        Cmpt2067AsyncWelfare welfare = new Cmpt2067AsyncWelfare();
        AwardAttrConfig a = r.getRewardInfo();
        welfare.setActId(attr.getActId());
        welfare.setCmptUseInx((long) attr.getAsyncRewardInx());
        welfare.setBusiId((long) attr.getBusiId());
        welfare.setUid(uid);
        welfare.setTaskId(a.getTAwardTskId());
        Map<Long, Integer> packageIdAmount = ImmutableMap.of(a.getTAwardPkgId(), a.getNum());

        welfare.setTaskPackageIds(JSON.toJSONString(ImmutableMap.of(a.getTAwardTskId(), packageIdAmount)));
        welfare.setExtLong(0L);
//        String anchorAwardSeqMd5 = MD5SHAUtil.getMD5(String.format("%s_%d_%d",seq,uid,otherUid));
//        String exSeqMd5 = MD5SHAUtil.getMD5(String.format("%s_%d_%d",seq,uid,otherUid));

        Map<String, String> anchorExtData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(otherUid),
                HdztAwardServiceClient.DUP_SHOW_SEQ, exSeq,HdztAwardServiceClient.CP_MEMBER,cpMember);
        welfare.setExtData(JSON.toJSONString(anchorExtData));
        Date now = new Date();
        welfare.setCreateTime(now);
        welfare.setUpdateTime(now);
        welfare.setSeq(seq);
        return welfare;
    }

    private DiceRet getLotteryAward(CpDiceLotteryComponentAttr attr, long start, Map<Long, Long> restrictInfo, long rewardAmount) {
        log.info("getLotteryAward start:{} restrictInfo:{} rewardAmount；{}", start, restrictInfo, rewardAmount);
        List<DiceRet> awardList = new ArrayList<>();
        long startStep = start;
        long roundMax = attr.getCpTaskPackageReward().size();
        Map<Long, Long> restrictTmp = restrictInfo;
        long rewardAmountTmp = rewardAmount;
        long maxStep = attr.getLotteryMaxValue();
        for (int i = 0; i < maxStep; i++) {
            startStep = startStep % roundMax + 1;
            AwardAttrConfig award = attr.getCpTaskPackageReward().get(startStep);
            DiceRet ret = new DiceRet();
            ret.setRewardInfo(award);
            ret.setStep(startStep);
            ret.setDicePoint(i + 1);
            ret.setHit(award.getTAwardPkgId() == attr.getInvalidPackageId() ? false : true);
            if (award.getAwardAmount() == 0) {
                log.info("free award cubeNo:{} award:{} restrictInfo:{} rewardAmount:{} ", startStep, award, restrictInfo, rewardAmount);
                awardList.add(ret);
                continue;
            }
            long balance = restrictTmp.get(award.getTAwardPkgId());
            if ((balance - award.getNum()) >= 0 && rewardAmountTmp + award.getAwardAmount() * 2 <= attr.getAwardSum()) {
                log.info("nofree award cubeNo:{} award:{} restrictInfo:{} rewardAmount:{} ", startStep, award, restrictInfo, rewardAmount);
                awardList.add(ret);
            }
        }
//        Random random = new Random();
        int randomIndex = RANDOM.nextInt(awardList.size());
        return awardList.get(randomIndex);
    }

    private void updateStaticsData(CpDiceLotteryComponentAttr attr, List<DiceRet> diceList, long draw) {
        try {
            Date now = commonService.getNow(attr.getActId());
            Map<Long, Integer> resultMap = diceList.stream()
                    .map(DiceRet::getRewardInfo) // 提取AwardAttrConfig对象
                    .filter(info -> info != null) // 过滤可能的空值
                    .collect(Collectors.toMap(
                            AwardAttrConfig::getTAwardPkgId, // key是奖包ID
                            AwardAttrConfig::getNum,        // value是奖励数量
                            Integer::sum,                   // 合并策略：累加相同奖包ID的数量
                            HashMap::new                    // 指定Map实现
                    ));
            for (Map.Entry<Long, Integer> entry : resultMap.entrySet()) {
                log.info("updateStaticsData {} {}", entry.getKey(), entry.getValue());
                commonDataDao.hashValueSetOrIncr(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), REWARD_STATICS_REWARD_KEY,
                        Convert.toString(entry.getKey()), Convert.toLong(entry.getValue()));
            }
            commonDataDao.hashValueSetOrIncr(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), DICE_KEY,
                    HASH_TIP_USE, draw);
            commonDataDao.hashValueSetOrIncr(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), REWARD_STATICS_JOIN_KEY,
                    DateUtil.format(now, DateUtil.PATTERN_TYPE2), 1);
        } catch (Exception e) {
            log.error("updateStaticsData error:{}", e);
        }
    }


    @RequestMapping("/getMapInfo")
    public Response getMapInfo(long actId, long comIndex) {
        List<DiceRet> awardList = new ArrayList<>();
        CpDiceLotteryComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(404, "活动未配置");
        }
        for (Map.Entry<Long, AwardAttrConfig> entry : attr.getCpTaskPackageReward().entrySet()) {
            DiceRet e = new DiceRet();
            e.setRewardInfo(entry.getValue());
            e.setStep(entry.getKey());
            awardList.add(e);
        }
        return Response.success(awardList);
    }

    @RequestMapping("/info")
    public Response info(HttpServletRequest request, HttpServletResponse response,
                         long actId, long comIndex, long  uid, long anchorUid) {
        long loginUid = getLoginYYUid(request, response);
        InfoRsp rsp = new InfoRsp();
//        uid = Convert.toLong(uid);
//        anchorUid = Convert.toLong(anchorUid);
        log.info("info uid:{}, anchorUid:{}", uid, anchorUid);
        if (loginUid <= 0L) {
            return Response.fail(400, "未登陆");
        }
//        if (SysEvHelper.isHistory()) {
//            return Response.fail(400, "活动已结束!");
//        }
        if (loginUid != uid  && loginUid != anchorUid) {
            return Response.fail(400, "参数错误");
        }
        CpDiceLotteryInfo info =
                cpDiceLottryDao.getDrawInfo(actId, comIndex, uid, anchorUid);
        CpDiceLotteryTicket userTicket = cpDiceLottryDao.getTicket(actId, comIndex, uid, anchorUid);
        rsp.setBalance(userTicket.getBalance());
        rsp.setTotalCount(userTicket.getTotalCount());
        rsp.setUserButton(info.getUidClick() != 0);
        rsp.setAnchorButton(info.getAnchorClick() != 0);
        rsp.setStep(info.getStep() == 0 ? 1 : info.getStep());
        return Response.success(rsp);
    }


    @RequestMapping("/getCpList")
    public Response getCpList(HttpServletRequest request, HttpServletResponse response, long actId, long comIndex) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(400, "未登陆");
        }
        CpDiceLotteryComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(404, "活动未配置");
        }
//        if (SysEvHelper.isHistory()) {
//            return Response.fail(400, "活动已结束!");
//        }
//        List<CpDiceLotteryInfo> cpLists = cpDiceLottryDao.listCpLotteryInfo(actId, cmptInx, uid);
        List<CpDiceLotteryTicket> ticketList = cpDiceLottryDao.listCpLotteryTicketInfo(actId, comIndex, uid);
        Set<Long> uids = new HashSet<>();
        uids.add(uid);
        for (CpDiceLotteryTicket l : ticketList) {
            uids.add(l.getUid());
            uids.add(l.getAnchorUid());
        }
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(new ArrayList<>(uids));
        if (batched == null || MapUtils.isEmpty(batched.getUserInfoMap())) {
            log.error("can not get user info with nick ext");
            return Response.fail(400, "活动已结束!");
        }
        Map<String, User> userInfoMap = batched.getUserInfoMap().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                i -> toUserInfo(i.getValue())));
        Map<String, Long> balanceMap = ticketList.stream()
                .collect(Collectors.toMap(
                        record -> record.getUid() + "_" + record.getAnchorUid(),
                        CpDiceLotteryTicket::getBalance
                ));

        ListCpRsp rsp = new ListCpRsp();
        List<Cp> cps = new ArrayList<>();
        for (CpDiceLotteryTicket elem : ticketList) {
            Cp cp = new Cp();
            cp.setCpMember(elem.getUid() + "_" + elem.getAnchorUid());
            if (userInfoMap.containsKey(elem.getUid() + "")) {
                cp.setUser(userInfoMap.get(elem.getUid() + ""));
            }
            if (userInfoMap.containsKey(elem.getAnchorUid() + "")) {
                cp.setAnchor(userInfoMap.get(elem.getAnchorUid() + ""));
            }
            cp.setBalance(balanceMap.get(elem.getUid() + "_" + elem.getAnchorUid()));
            cps.add(cp);
        }

        Map<String, Map<String, MultiNickItem>> multiNickUsers = null;
        if (org.apache.commons.lang3.StringUtils.startsWith(batched.getNickExt(), StringUtil.OPEN_BRACE)) {
            NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
            multiNickUsers = nickExt.getUsers();
        }
        Collections.sort(cps, new Comparator<Cp>() {
            @Override
            public int compare(Cp user1, Cp user2) {
                return Long.compare(user2.getBalance(), user1.getBalance());
            }
        });
        long signedSid = signedService.getSignedSidByBusiId(Convert.toLong(uid), attr.getBusiId());
        String userAvatar = userInfoMap.get(uid + "").avatar;
        rsp.setCps(cps);
        rsp.setUserAvatar(userAvatar);
        rsp.setNickExtUsers(multiNickUsers);
        rsp.setSignedSid(signedSid);
        return Response.success(rsp);
    }

    private User toUserInfo(WebdbUserInfo userBaseInfo) {
        User user = new User();
        user.setUid(Long.parseLong(userBaseInfo.getUid()));
        user.setName(userBaseInfo.getNick());
        user.setAvatar(WebdbUtils.getLogo(userBaseInfo));
        return user;
    }

    @NeedRecycle(author = "gaofei", notRecycle = true)
    @Scheduled(cron = "0 5 * * * *")
    public void syncComponentInfo() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {
            ActivityInfoVo activityInfo = actInfoService.queryActivityInfo(actId);
            if (activityInfo == null) {
                continue;
            }

            Date now = commonService.getNow(actId);

            CpDiceLotteryComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }

            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }


            String timeCode = DateUtil.format(DateUtil.addMinutes(now, -60), DateUtil.PATTERN_TYPE5);


            timerSupport.work("doCpDiceStaticReport", 300, () -> doSyncComponentInfo(attr, timeCode));
        }

    }

    public void doSyncComponentInfo(CpDiceLotteryComponentAttr attr, String timeCode) {
        log.info("in {}", timeCode);
        StringBuilder content = new StringBuilder();
        content.append("时段：").append(timeCode).append("\n");
        content.append("【奖品发放信息】\n");

        // 今日总发放
        Map<String, String> allRewardInfo = commonDataDao.hashGetAll(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), REWARD_STATICS_REWARD_KEY);
//        boolean isEmpty = allRewardInfo.isEmpty(); // false

        // 安全求和：先检查是否为空
        int totalCount = 0;
        Map<Long, CpDiceLotteryComponentAttr.StaticsInfo> sortedMap = attr.getStaticsConfig().entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.comparing(CpDiceLotteryComponentAttr.StaticsInfo::getAwardName)))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
        if (!allRewardInfo.isEmpty()) {
            totalCount = allRewardInfo.values().stream()
                    .filter(str -> str.matches("\\d+")) // 过滤非数字字符串
                    .mapToInt(Integer::parseInt)
                    .sum();
        }
        content.append("模拟概率 | 实时概率 | 发放/总上限 | 奖品名称\n");
        long amountTotal = 0;
        for (Map.Entry<Long, CpDiceLotteryComponentAttr.StaticsInfo> entry : sortedMap.entrySet()) {
            long c = allRewardInfo.isEmpty() ? 0 : Convert.toLong(allRewardInfo.get(Convert.toString(entry.getKey())));
            content.append(" <font color=\"blue\">" + entry.getValue().getRatio() + "</font>" + " | ");
            content.append(" <font color=\"green\">" + getRealRatio(totalCount, entry.getKey(), allRewardInfo) + "</font>" + " | ");
            String upLimit = entry.getValue().getUpLimit() == -1 ? "无" : Convert.toString(entry.getValue().getUpLimit());
            content.append(" <font color=\"green\">" + c + "</font>").append("/").append("<font color=\"blue\">" + upLimit + "</font>" + " |  ");
            content.append(entry.getValue().getAwardName()).append("\n");
            amountTotal += c * entry.getValue().getAmount();
        }

        content.append("截止当前，累计发放礼物金额：").append("<font color=\"red\">" + amountTotal + "</font>").append("\n");
        CpDiceLotteryTicket svTicketInfo = cpDiceLottryDao.getTicket(attr.getActId(), attr.getCmptUseInx(), 0, 0);
        String useCount = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), DICE_KEY, HASH_TIP_USE);
        Map<String, String> cpDrawCountMap = commonDataDao.hashGetAll(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), REWARD_STATICS_JOIN_KEY);
        Date now = commonService.getNow(attr.getActId());
        String dayTip = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        int totalDrawCount = 0;
        if (!cpDrawCountMap.isEmpty()) {
            totalDrawCount = cpDrawCountMap.values().stream()
                    .filter(str -> str.matches("\\d+")) // 过滤非数字字符串
                    .mapToInt(Integer::parseInt)
                    .sum();
        }
        long dayDrawCount = cpDrawCountMap.isEmpty() ? 0 : Convert.toLong(cpDrawCountMap.get(dayTip));
        content.append("【骰子统计】\n");
        content.append("累计产生数量：").append("<font color=\"green\">" + svTicketInfo.getTotalCount() + "</font>").append("\n");
        content.append("当前已使用数量：").append("<font color=\"green\">" + Convert.toLong(useCount) + "</font>").append("\n");
        content.append("今日参与抽奖CP次数：").append("<font color=\"green\">" + dayDrawCount + "</font>").append("\n");
        content.append("累计参与抽奖CP次数：").append("<font color=\"green\">" + totalDrawCount + "</font>").append("\n");


        String msg = content.toString();
        msg = commonService.buildActRuliuMsg(attr.getActId(), false, "夏日探险玩法", msg);
        log.info(msg);

        baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_ACT_TURNOVER, msg, Lists.newArrayList());

    }


    private String getRealRatio(long totalCount, long k, Map<String, String> m) {
        if (totalCount == 0 || m.isEmpty()) {
            return "00.00%";
        }
        long count = Convert.toLong(m.get(Convert.toString(k)));
        double result = (double) count / totalCount;
        return FORMATTER.format(result);
    }

    @Data
    public static class Gift {
        private String giftName;
        private String giftIcon;
        private String giftUnit;
        private long giftCount;
    }

    @Data
    public static class Cp {
        private String cpMember;
        private User anchor;
        private User user;
        private long balance;
    }

    @Data
    public static class ListCpRsp {
        private List<Cp> cps;

        private Map<String, Map<String, MultiNickItem>> nickExtUsers;

        private String userAvatar;

        private long signedSid;
    }


    @Data
    public static class User {
        private String avatar;

        private String name;

        private Long uid;
    }

    @Data
    public static class InfoRsp {
        private long totalCount;
        private long balance;
        private boolean userButton;
        private boolean anchorButton;
        private long step; // 格子
    }

    @Data
    public static class DrawRsp {
        private boolean draw;
        private long totalCount;
        private long balance;
        private long startStep;
        private List<DiceRet> rewardList;
    }

    @Data
    public static class DiceRet {
        private AwardAttrConfig rewardInfo;
        private long dicePoint; // 骰子点数
        private long step; // 格子
        private boolean hit;
    }
}




