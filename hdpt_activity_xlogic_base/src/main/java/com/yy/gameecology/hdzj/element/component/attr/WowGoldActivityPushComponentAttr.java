package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

import java.time.LocalTime;
import java.util.Date;

@Data
public class WowGoldActivityPushComponentAttr  extends ComponentAttr {

    @ComponentAttrField(labelText = "登录控制组件",remark = "参与活动UID白名单组件索引")
    protected int wowControlCmptIndex;

    @ComponentAttrField(labelText = "任务组件",remark = "任务组件index")
    protected int wowTaskCmptIndex;

    @ComponentAttrField(labelText = "推送开始时间", remark = "触发推送的开始时间 活动开始三天后")
    protected Date pushStartTime;

    @ComponentAttrField(labelText = "推送结束时间", remark = "触发推送的结束时间")
    protected Date pushEndTime;

    @ComponentAttrField(labelText = "最大push次数", remark = "触发推送的结束时间")
    protected int maxPushTime;

    @ComponentAttrField(labelText = "中台推送标题")
    protected String pushTitle;

    @ComponentAttrField(labelText = "中台推送Appid", remark = "推送中台消息的appid")
    protected String pushAppid = "Yomi";

    @ComponentAttrField(labelText = "中台推送跳转链接")
    protected String pushLink;

    @ComponentAttrField(labelText = "中台推送icon")
    protected String pushIcon;
}
