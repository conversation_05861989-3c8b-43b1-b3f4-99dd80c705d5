package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.common.consts.PBCommonBannerId;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.UserTaskBrocastComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021.09.18 18:30
 */
@Deprecated
@Component
public class UserTaskBrocastComponent extends BaseActComponent<UserTaskBrocastComponentAttr> {

    @Override
    public Long getComponentId() {
        return ComponentId.JIN_NANG_BRO;
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void jinnangBro(TaskProgressChanged event, UserTaskBrocastComponentAttr attr) {
        if (!attr.getRankIds().contains(event.getRankId()) || event.getActId() != attr.getActId()) {
            return;
        }

        long uid = Convert.toLong(event.getMember());
        String nickName = commonService.getNickName(uid, false);

        //一次过多个等级处理
        long start = event.getStartTaskIndex() + 1;
        Map<Long, Long> map = attr.getLevel2score().get(event.getRankId());
        for (long i = start; i < event.getCurrTaskIndex(); i++) {
            bro(attr.getActId(), map.get(i), nickName, attr.getSvgaUrl());
        }
        //播放最高一条
        bro(attr.getActId(), event.getItemCurrNumMap().get(attr.getItemId()), nickName, attr.getSvgaUrl());

    }

    private void bro(long actId, long score, String nickName, String svgaUrl) {
        Map<String, String> extMap = ImmutableMap.of("nickName", nickName, "value", score + "", "svgaUrl", svgaUrl);
        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setBannerId(PBCommonBannerId.USER_TASK).setUserNick(nickName)
                .setUserScore(score).setJsonData(JSON.toJSONString(extMap)).build();

        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        for (Template template : Template.values()) {
            svcSDKService.broadcastTemplate(template, bannerBroMsg);
        }
    }
}
