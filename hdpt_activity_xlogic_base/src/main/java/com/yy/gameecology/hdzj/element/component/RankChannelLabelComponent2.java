package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSortedMap;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.rank.RankValueItemBase;
import com.yy.gameecology.activity.processor.ranking.ComponentRankingExtHandle;
import com.yy.gameecology.activity.service.LoginService;
import com.yy.gameecology.common.bean.Page;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.mapper.gameecology.cmpt.Cmpt2066MemContribMapper;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2066MemContrib;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.RankChannelLabelComponent2Attr;
import com.yy.gameecology.hdzj.element.component.dao.Cmpt2066MemDao;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 具体业务需求
 * 1. 活动期间,统计每个神豪在频道内的付费消费
 * 2. 在神豪榜单展示标签,对应规则如下：
 * A. 用户在1-3个顶级频道分别消费活动礼物满288800荣耀值是【专一】标签
 * B. 在4-8个顶级频道分别消费活动礼物满288800荣耀值才会变成【浪漫】标签
 * C. 在9个以上顶级频道分别消费活动礼物满288800荣耀值才会变成【潇洒】标签
 * <p>
 * 在中台的榜单属性添加发送 榜单改变事件 send_ranking_score_changed_event
 */
@Slf4j
@RestController
@RequestMapping("/2066/")
public class RankChannelLabelComponent2 extends
        BaseActComponent<RankChannelLabelComponent2Attr> implements
        ComponentRankingExtHandle<RankChannelLabelComponent2Attr> {

    @Autowired
    private LoginService loginService;


    @Autowired
    private Cmpt2066MemDao cmpt2066MemDao;

    @Autowired
    private Cmpt2066MemContribMapper cmpt2066MemContribMapper;


    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Override
    public Long getComponentId() {
        return ComponentId.CHANNEL_COUNT_LABEL2;
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChanged(RankingScoreChanged event, RankChannelLabelComponent2Attr attr) {

        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }
        if (event.getItemScore() <= 0) {
            return;
        }
        log.info("RankChannelLabelComponent RankingScoreChangedEvent:{}",
                JSON.toJSONString(event));
        Pair<String, String> userIdAndChannelId = prepare(event, attr);
        if (StringUtils.isEmpty(userIdAndChannelId.getLeft()) || StringUtils
                .isEmpty(userIdAndChannelId.getRight())) {
            return;
        }
        action(attr, userIdAndChannelId.getLeft(), userIdAndChannelId.getRight(), event.getItemScore(), event.getSeq());
    }

    private void action(RankChannelLabelComponent2Attr attr, String userId, String channelId, long itemScore, String seq) {
        long res = cmpt2066MemDao.addScore(seq, attr.getActId(), attr.getCmptUseInx(), userId, channelId, itemScore);
        log.info("onRankingScoreChanged done,actId:{},rankId:{},userId:{},channelId:{}, channelScore:{},res:{}",
                attr.getActId(), attr.getRankId(), userId, channelId, itemScore, res);

    }


    private Pair<String, String> prepare(RankingScoreChanged event,
                                         RankChannelLabelComponent2Attr attr) {
        String channel = getChannel(event, attr.getChannelRoleIds()).orElse(StringUtils.EMPTY);
        String userId = StringUtils.trimToEmpty(event.getMember());
        return Pair.of(userId, channel);
    }

    private Optional<String> getChannel(RankingScoreChanged event, Set<Long> channelRoleIds) {
        if (CollectionUtils.isEmpty(channelRoleIds)) {
            return Optional.empty();
        }
        for (Long roleId : channelRoleIds) {
            if (event.getActors().containsKey(roleId)) {
                return Optional.ofNullable(event.getActors().get(roleId));
            }
        }
        return Optional.empty();
    }

    @RequestMapping("/record")
    public Page<LabelRecord> labelRecord(HttpServletRequest request, HttpServletResponse response,
                                         @RequestParam(name = "actId") long actId,
                                         @RequestParam(name = "cmptUseIndex") long cmptUseIndex,
                                         @RequestParam(name = "page", defaultValue = "1") int page,
                                         @RequestParam(name = "pageSize", defaultValue = "10") int pageSize) {
        long uid = getLoginYYUid(request, response);
        RankChannelLabelComponent2Attr attr = getComponentAttr(actId, cmptUseIndex);
        Page<LabelRecord> result = new Page<>();

        List<Cmpt2066MemContrib> contribs = cmpt2066MemContribMapper.selectCmpt2066MemContribList(actId, attr.getCmptUseInx(), Convert.toString(uid), Math.min(pageSize, Const.THIRTY));

        if (CollectionUtils.isNotEmpty(contribs)) {
            List<Long> sids = contribs.stream().map(p -> Convert.toLong(p.getContribute())).toList();
            Map<Long, WebdbChannelInfo> channelInfoMap = webdbThriftClient.batchGetChannelInfo(sids);
            List<LabelRecord> records = new ArrayList<>(sids.size());
            for (Cmpt2066MemContrib item : contribs) {
                long sid = Convert.toLong(item.getContribute());
                long score = item.getScore();
                LabelRecord record = new LabelRecord();
                record.setSid(sid);
                record.setScore(score);
                WebdbChannelInfo channelInfo = channelInfoMap.get(sid);
                if (channelInfo != null) {
                    String asidStr = channelInfo.getAsid();
                    String name = channelInfo.getName();
                    long asid = Optional.ofNullable(asidStr).filter(StringUtils::isNumeric).map(Long::parseLong).orElse(sid);
                    record.setAsid(asid);
                    record.setName(name);
                }

                records.add(record);
            }

            result.setList(records);
        }

        return result;
    }

    @Override
    public List<Object> handleExt(RankChannelLabelComponent2Attr attr, GetRankReq rankReq,
                                  RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        if (attr.getRankId() != rankReq.getRankId() || attr.getPhaseId() != rankReq.getPhaseId()) {
            return objectList;
        }
        if (CollectionUtils.isEmpty(ranks)) {
            return objectList;
        }

        List<String> member = ranks.stream().map(Rank::getMember).toList();
        var contributeList = cmpt2066MemContribMapper
                .selectByActIdAndCmptUseInxAndMembers(attr.getActId(), attr.getCmptUseInx(), attr.getLabelViewThreshold(), member);
        Map<String, Long> contributeCount = contributeList.stream().collect(Collectors.groupingBy(m -> m, Collectors.counting()));


        objectList.stream().map(rank -> ((RankValueItemBase) rank))
                .forEach(rank -> {
                            if (rank.getValue() == null || rank.getValue() < attr.getLabelViewThreshold()) {
                                return;
                            }

                            int channelCount = Convert.toInt(contributeCount.getOrDefault(rank.getKey(), 0L));
                            if (channelCount <= 0) {
                                return;
                            }

                            Map.Entry<Integer, Integer> entry = ImmutableSortedMap.copyOf(attr.getLabelMap())
                                    .floorEntry(channelCount);
                            rank.getViewExt()
                                    .put(attr.getLabelConfig(), entry != null ? String.valueOf(entry.getValue()) : "0");
                        }
                );
        return objectList;
    }

    @Data
    public static class LabelRecord {
        protected long sid;

        protected long asid;

        protected String name;

        protected long score;
    }

    public long getLoginYYUid(HttpServletRequest req, HttpServletResponse resp) {
        if (req == null || resp == null) {
            return 0;
        }
        return loginService.getLoginYYUid(req, resp);
    }
}
