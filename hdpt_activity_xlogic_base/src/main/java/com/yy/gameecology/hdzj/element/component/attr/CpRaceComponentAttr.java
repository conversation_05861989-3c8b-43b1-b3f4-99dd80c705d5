package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-07-11 11:56
 **/
@Data
public class CpRaceComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "结算榜单ID")
    private long rankId;

    @ComponentAttrField(labelText = "强厅角色")
    private long tingActor;

    @ComponentAttrField(labelText = "结算阶段ID")
    private long phaseId;

    @ComponentAttrField(labelText = "业务ID")
    private long busiId;

    @ComponentAttrField(labelText = "发奖配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "最小分值"),
                    @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "奖励")
            })
    private Map<Long, AwardAttrConfig> top1Award = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "未上榜的默认礼物图")
    private String defaultAwardIcon;

    @ComponentAttrField(labelText = "触发特效最小分值")
    private long bannerMinScore;

    @ComponentAttrField(labelText = "svga特效url")
    private String top1Svga;

    @ComponentAttrField(labelText = "svga字体大小")
    private String svgaFontSize;

    @ComponentAttrField(labelText = "svga文案key")
    private String svgaTextKey;

    @ComponentAttrField(labelText = "svga文案")
    private String svgaText;


    @ComponentAttrField(labelText = "cp榜头像图片key",
            subFields = {@SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "用户标识",remark = "1-主播 2-神豪"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "imgkey")})
    private Map<Integer, String> svgaImgLayers;


    @ComponentAttrField(labelText = "app广播范围", remark = "1 -语音房 2 -交友房 4 -其他 8 -宝贝 位域表示 支持组合：交友and宝贝：2+8")
    private int appBannerBusiId;

    @ComponentAttrField(labelText = "广播模板", remark = "2==宝贝 3==交友 5==语音房(技能卡)")
    private int broTemplate;

    @ComponentAttrField(labelText = "缘分值对应格子")
    private long scoreExchangeStep;

    @ComponentAttrField(labelText = "鹊桥竞速格子topn")
    private int roadMapTopN=500;

    @ComponentAttrField(labelText = "cp用户对主持贡献榜单id")
    private long cpContributeRankId;

    @ComponentAttrField(labelText = "cp主持对用户贡献榜单id")
    private long cpAntiContributeRankId;

    @ComponentAttrField(labelText = "cp榜单拉取数量")
    private long rankLimit;

    @ComponentAttrField(labelText = "口令抽奖奖池Id")
    private long lotteryTaskId;


    @ComponentAttrField(labelText = "主持上座礼物图")
    private String onSeatGiftUrl;

    @ComponentAttrField(labelText = "全服婚礼url")
    private String weddingUrl;

    @ComponentAttrField(labelText = "app横幅素材")
    private String appBannerSvga;

    @ComponentAttrField(labelText = "主持首次上座svga图片key",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "imgkey"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "图片地址")})
    private Map<String, String> anchorSvgaImgLayers;

    @ComponentAttrField(labelText = "口令抽奖过期时间",remark = "单位秒")
    private long lotterySxpireSecond=300;

    public AwardAttrConfig findAward(long score) {
        AwardAttrConfig config = null;
        List<Long> configScoreLevel = top1Award.keySet().stream().sorted().toList();
        for (long scoreConfig : configScoreLevel) {
            if (score >= scoreConfig) {
                config = top1Award.get(scoreConfig);
            }
        }

        return config;
    }

    @Data
    public static class Award {
        private String name;

        private String img;

        private int num;
    }


}
