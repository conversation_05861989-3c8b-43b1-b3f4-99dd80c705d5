package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;

import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption: 业务宝箱抽奖属性
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
public class BoxLotteryComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private long bussId;


    /**
     * 礼物的价格转换map，按比列就行，不一定是准确的价格
     */
    @ComponentAttrField(labelText = "礼物价格", remark = "礼物的价格转换map，按比列就行，不一定是准确的价格",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "礼物id"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "礼物价格")
            })
    private Map<String, Integer> giftValueMap;
    /**
     * 价格对应的宝箱类型
     */
    @ComponentAttrField(labelText = "宝箱类型", remark = "价格对应的宝箱类型",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "价格"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "宝箱类型")
            })
    private Map<Long, Integer> valueBoxTypeMap;

    /**
     * 宝箱抽奖的taskId；
     */
    @ComponentAttrField(labelText = "宝箱抽奖配置", remark = "宝箱抽奖的taskId",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "宝箱类型"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "奖池id")
            })
    private Map<Integer, Integer> boxTypeTaskIdMap;


    /**
     * 连抽间隔时间,毫秒,上线后不建议从连抽设置为不连抽，可能会有bug
     */
    @ComponentAttrField(labelText = "连抽间隔时间", remark = "连抽间隔时间,毫秒,上线后不建议从连抽设置为不连抽，可能会有bug")
    private long continuousLotteryTime = 500;

    //---------------------------必中配置----------------------------------

    /**
     * 必中依据1= （日）送礼顺序排名，2= （活动）送礼顺序排名，3=榜单排名 ，其他不开启必中
     */
    @ComponentAttrField(labelText = "必中依据", dropDownSourceBeanClass = MustWinSource.class, remark = "必中依据1= （日）送礼顺序排名，2= （活动）送礼顺序排名，3=榜单排名 ，其他不开启必中")
    private int mustWin;

    /**
     * 用户必须在指定榜单中排名大于等于限制，才执行必中逻辑，0是不必检查榜单限制
     */
    @ComponentAttrField(labelText = "榜单限制", remark = "用户必须在指定榜单中排名大于等于限制，才执行必中逻辑，0是不必检查榜单限制")
    private int limitRank;
    /**
     * 榜单id ，limitRank >0 或者 mustWin =3 必须配置
     */
    @ComponentAttrField(labelText = "榜单id", remark = "榜单id ，榜单限制 >0 或者 必中依据 =3 必须配置")
    private long mustWinRankId;
    /**
     * 阶段id ，limitRank >0 或者 mustWin =3 必须配置
     */
    @ComponentAttrField(labelText = "阶段id", remark = "榜单限制 >0 或者 必中依据 =3 必须配置")
    private long mustWinPhaseId;

    /**
     * 有必中逻辑的宝箱类型 1金 2银
     */
    @ComponentAttrField(labelText = "必中宝箱类型", remark = "有必中逻辑的宝箱类型 1金 2银", defaultValue = "1",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Integer.class)})
    private Integer[] mustWinBoxType = {1};

    /**
     * 排名必中奖包
     */
    @ComponentAttrField(labelText = "排名必中奖包",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "榜单排名"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖包id")
            })
    private Map<Integer, Long> rankMustWinMap;

    public long getBussId() {
        return bussId;
    }

    public void setBussId(long bussId) {
        this.bussId = bussId;
    }

    public Map<String, Integer> getGiftValueMap() {
        return giftValueMap;
    }

    public void setGiftValueMap(Map<String, Integer> giftValueMap) {
        this.giftValueMap = giftValueMap;
    }

    public Map<Long, Integer> getValueBoxTypeMap() {
        return valueBoxTypeMap;
    }

    public void setValueBoxTypeMap(Map<Long, Integer> valueBoxTypeMap) {
        this.valueBoxTypeMap = valueBoxTypeMap;
    }

    public Map<Integer, Integer> getBoxTypeTaskIdMap() {
        return boxTypeTaskIdMap;
    }

    public void setBoxTypeTaskIdMap(Map<Integer, Integer> boxTypeTaskIdMap) {
        this.boxTypeTaskIdMap = boxTypeTaskIdMap;
    }

    public long getContinuousLotteryTime() {
        return continuousLotteryTime;
    }

    public void setContinuousLotteryTime(long continuousLotteryTime) {
        this.continuousLotteryTime = continuousLotteryTime;
    }

    public int getMustWin() {
        return mustWin;
    }

    public void setMustWin(int mustWin) {
        this.mustWin = mustWin;
    }

    public int getLimitRank() {
        return limitRank;
    }

    public void setLimitRank(int limitRank) {
        this.limitRank = limitRank;
    }

    public long getMustWinRankId() {
        return mustWinRankId;
    }

    public void setMustWinRankId(long mustWinRankId) {
        this.mustWinRankId = mustWinRankId;
    }

    public long getMustWinPhaseId() {
        return mustWinPhaseId;
    }

    public void setMustWinPhaseId(long mustWinPhaseId) {
        this.mustWinPhaseId = mustWinPhaseId;
    }

    public Map<Integer, Long> getRankMustWinMap() {
        return rankMustWinMap;
    }

    public void setRankMustWinMap(Map<Integer, Long> rankMustWinMap) {
        this.rankMustWinMap = rankMustWinMap;
    }

    public Integer[] getMustWinBoxType() {
        return mustWinBoxType;
    }

    public void setMustWinBoxType(Integer[] mustWinBoxType) {
        this.mustWinBoxType = mustWinBoxType;
    }
}
