package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022.10.18 16:57
 */
@Data
public class TopNDoNotPKComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;
    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;

    /**
     * 源头topN {rankId:{phaseId:count}}
     */
    @ComponentAttrField(labelText = "源头topN", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单id"),
            @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "阶段id"),
            @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "数量")
    })
    private Map<Long, Map<Long, Integer>> srcTopN;
    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    public int busiId;
    @ComponentAttrField(labelText = "角色类型", dropDownSourceBeanClass = RoleTypeSource.class)
    private int roleType;

    /**
     * 操作人需要在中台活动属性配置权限
     **/
    @ComponentAttrField(labelText = "操作人", remark = "操作人需要在中台活动属性配置权限")
    private long opUid = 50044432;

    @ComponentAttrField(labelText = "主播不pk白名单", remark = "主播不pk白名单,多个逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
    })
    private List<Long> anchorWhiteList;

    @ComponentAttrField(labelText = "同ow公会列表", remark = "不配置则对应策略不生效", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "owuid", remark = "暂无用到,可以先填任意数值"),
            @SubField(fieldName = Constant.MAP_LIST_VALUE, type = Long.class, labelText = "公会列表", remark = "多个时逗号分隔")
    })
    private Map<Long, List<Long>> newWhiteList;

    @ComponentAttrField(labelText = "topN策略得分", remark = "得分越高,优先级越高，越不容易匹配到")
    private long topNScore = 1;

    @ComponentAttrField(labelText = "同公会策略得分", remark = "得分越高,优先级越高，越不容易匹配到")
    private long sameSignScore = 7;

    @ComponentAttrField(labelText = "同ow策略得分", remark = "得分越高,优先级越高，越不容易匹配到")
    private long sameOwScore = 3;

    @ComponentAttrField(labelText = "白名单主播策略得分", remark = "得分越高,优先级越高，越不容易匹配到")
    private long anchorWhiteListScore = 9;

    @ComponentAttrField(labelText = "语音房同家族策略得分(仅语音房业务有效)", remark = "得分越高,优先级越高，越不容易匹配到")
    private long sameFamilyScore = 3;
}
