package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrCollector;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

@Data
public class AwardProp {

    @ComponentAttrField(labelText = "奖励名称")
    protected String awardName;

    @ComponentAttrField(labelText = "奖励ICON", propType = ComponentAttrCollector.PropType.IMAGE)
    protected String awardIcon;
}
