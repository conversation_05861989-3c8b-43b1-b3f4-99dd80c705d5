package com.yy.gameecology.hdzj.element;

import com.google.common.collect.Sets;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.hdzj.ElementManager;
import com.yy.gameecology.hdzj.annotation.SkipCheck;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.doc.ComponentDocGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.Duration;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/17 17:58
 **/
@Component
public class ComponentAttrChecker {
    private static final Logger logger = LoggerFactory.getLogger(ComponentAttrChecker.class);

    private static final int TWO = 2;

    @Autowired
    private ActComponent[] components;
    @Autowired
    private ComponentDocGenerator componentDocGenerator;
    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    private static Set<Class<?>> basicTypeSet = Sets.newHashSet(Byte.class, Short.class, Integer.class, Long.class
            , Float.class, Double.class, Character.class, Boolean.class, String.class, Date.class, LocalTime.class, Duration.class);

    /**
     * 检查组件的属性类型的合法性
     **/
    @PostConstruct
    @NeedRecycle(author = "wangdonghong", notRecycle = true)
    public void checkComponentAttr() {
        // Act2021103002Component
        // ComponentTestHelper
        // ExampleComponent
        Clock clock = new Clock();
        findNoAttrDefineComponents();
        // findNoDocComponents();
        logger.info("begin check component attr");
        for (ActComponent component : components) {
            try {
                // 过滤掉打了 Deprecated 注解的组件
                if (component.getClass().getAnnotation(Deprecated.class) != null) {
                    logger.info("{} is Deprecated", component.getClass().getName());
                    continue;
                }

                if (component.getComponentId() > 100000) {
                    logger.info("{} is customize", component.getClass().getName());
                    continue;
                }

                Class<?> argClass = component.getMyAttrClass();
                if (argClass.getAnnotation(SkipCheck.class) != null) {
                    logger.info("{} is skip check", component.getClass().getName());
                    continue;
                }

                checkArgClass(component, argClass, 1);
            } catch (Exception ex) {
                logger.error(component.getComponentId() + "", ex);
            }
        }
        logger.info("checkComponentAttr finish,Clock={}", clock);
    }

    public void findNoAttrDefineComponents() {
        if (!SysEvHelper.isLocal()) {
            return;
        }

        List<ActComponent> noAttrDefineComponents = new ArrayList<>();
        for (ActComponent component : components) {
            Class<?> attrClass = component.getMyAttrClass();
            // 没有自己的配置属性
            if (attrClass == ComponentAttr.class) {
                continue;
            }
            // 活动定制组件忽略
            if (component.getComponentId() > 10000) {
                continue;
            }
            if (attrClass.getAnnotation(SkipCheck.class) != null) {
                continue;
            }
            Field[] fields = attrClass.getDeclaredFields();
            if (fields.length == 0) {
                continue;
            }
            boolean noAttrDefine = true;
            for (Field field : fields) {
                if (field.getAnnotation(ComponentAttrField.class) != null) {
                    noAttrDefine = false;
                    break;
                }
            }

            if (noAttrDefine) {
                noAttrDefineComponents.add(component);
            }
        }
        if (!noAttrDefineComponents.isEmpty() && !SysEvHelper.isDeploy()) {
            for (ActComponent noAttrDefineComponent : noAttrDefineComponents) {
                logger.error("组件 {} 没有同步属性到数据库,不能进行后台配置,请操作ConfigCollector.synComponentAttrDefineTest 进行同步", noAttrDefineComponent.getComponentId());
            }
            String msg = noAttrDefineComponents.stream().map(c -> ElementManager.getRealClass(c).getName()).collect(Collectors.joining("\r\n"));
            msg = "*<font color=\"red\">━━ 组件属性同步 ━━</font>*\n[" + msg + "]\n 没有同步属性到数据库,请同步";
            baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Collections.emptyList());
        }
    }

    public void findNoDocComponents() {
        List<ActComponent> noDocComponents = new ArrayList<>();
        for (ActComponent component : components) {
            long componentId = component.getComponentId();
            if (componentDocGenerator.getComponentDocBean(componentId) == null) {
                noDocComponents.add(component);
            }
        }

        if (CollectionUtils.isNotEmpty(noDocComponents)) {
            for (ActComponent noDocComponent : noDocComponents) {
                logger.warn("{} {} 没有进行文档生成,可使用ComponentDocGeneratorTest.synAllComponentDocToDBTest进行生成",
                        noDocComponent.getClass().getName(), noDocComponent.getComponentId());
            }
        }
    }


    private void checkArgClass(ActComponent component, Class<?> argClass, int level) {
        Field[] fields = argClass.getDeclaredFields();
        int highLevel = level + 1;
        for (Field field : fields) {
            try {
                Class<?> fieldClass = field.getType();
                if (isBasicType(fieldClass)) {
                    continue;
                } else if (fieldClass.isArray()) {
                    if (isBasicType(fieldClass.getComponentType())) {
                        continue;
                    }
                    checkInnerClass(component, field, fieldClass.getComponentType(), level);
                } else if (isCollectionType(fieldClass)) {
                    // 集合类型 List/Set
                    // field.getGenericType()
                    ParameterizedType parameterizedType = (ParameterizedType) field.getGenericType();
                    if (parameterizedType.getActualTypeArguments()[0] instanceof ParameterizedType) {
                        ParameterizedType innerParamType = (ParameterizedType) parameterizedType.getActualTypeArguments()[0];
                        checkListInnerParameterizedType(component, field, innerParamType, highLevel);
                    } else {
                        // List<BT> & List<OT> check
                        Class<?> argType = (Class<?>) parameterizedType.getActualTypeArguments()[0];
                        if (isBasicType(argType)) {
                            continue;
                        }
                        checkInnerClass(component, field, argType, level);
                    }
                } else if (Map.class.isAssignableFrom(fieldClass)) {
                    ParameterizedType parameterizedType = (ParameterizedType) field.getGenericType();
                    Class<?> keyType = (Class<?>) parameterizedType.getActualTypeArguments()[0];
                    if (!isBasicType(keyType)) {
                        throw new RuntimeException("key type is not basic type,componentId is:" + component.getComponentId() + ";field is :" + field.getName());
                    }

                    if (parameterizedType.getActualTypeArguments()[1] instanceof ParameterizedType) {
                        checkListInnerParameterizedType(component, field, (ParameterizedType) parameterizedType.getActualTypeArguments()[1], highLevel);
                    } else {
                        Class<?> valueType = (Class<?>) parameterizedType.getActualTypeArguments()[1];
                        if (isBasicType(valueType)) {
                            continue;
                        }
                        checkArgClass(component, valueType, highLevel);
                    }
                } else {
                    throw new RuntimeException("field type is illegal ,componentId is:" + component.getComponentId() + ";field is :" + field.getName());
                }
            } catch (Exception ex) {
                if (!SysEvHelper.isDeploy()) {
                    logger.error("{},{},{}", argClass.getName(), field.getName(), ex.getMessage(), ex);
                    logger.error("{}{}属性检测未通过,如果不想检测,可以再属性类上使用SkipCheck注解跳过检测", argClass.getName(), field.getName(), ex);
                    // System.exit(1);
                }
            }
        }
    }

    /**
     * 内部类拥有的属性只能是简单属性
     * 层级只能到第二层
     **/
    private void checkInnerClass(ActComponent component, Field pField, Class<?> argType, int level) {
        if (level > TWO) {
            throw new RuntimeException("inner class level is more than 2,componentId is :" + component.getComponentId()
                    + ",field is :" + pField.getName());
        }

        Field[] fields = argType.getDeclaredFields();
        for (Field field : fields) {
            if ("this$0".equals(field.getName())) {
                continue;
            }

            if (field.getAnnotation(ComponentAttrField.class) == null) {
                continue;
            }

            Class<?> fieldClass = field.getType();

            if (!isBasicType(fieldClass)) {
                throw new RuntimeException("field type is illegal ,componentId is:" + component.getComponentId()
                        + ",pField is :" + pField.getName()
                        + ",field is :" + field.getName());
            }
        }
    }

    /**
     * 检查嵌套泛型
     **/
    private void checkListInnerParameterizedType(ActComponent component, Field pField, ParameterizedType innerParamType, int level) {
        final int three = 3;
        if (level > three) {
            throw new RuntimeException("param level is more than 3,componentId is :" + component.getComponentId()
                    + ",field is :" + pField.getName());
        }
        int highLevel = level + 1;
        Type[] argTypes = innerParamType.getActualTypeArguments();
        if (argTypes.length == 1) {
            // List<List<List<T>>> is illegal
            if (argTypes[0] instanceof ParameterizedType) {
                throw new RuntimeException("List<List<List<T>>> is illegal,componentId is :" + component.getComponentId()
                        + ",field is :" + pField.getName());
            } else {
                // List<List<BT>> & List<List<OT>> check
                Class<?> argType = (Class<?>) argTypes[0];
                if (isBasicType(argType)) {
                    return;
                }
                checkInnerClass(component, pField, argType, level);
            }
        } else if (argTypes.length == TWO) {
            Class<?> keyType = (Class<?>) argTypes[0];
            if (!isBasicType(keyType)) {
                throw new RuntimeException("key type is not basic type,componentId is:" + component.getComponentId() + ";field is :" + pField.getName());
            }

            if (argTypes[1] instanceof ParameterizedType) {
                // List<Map<BT,List<BT>>> & List<Map<BT,Set<BT>>> & List<Map<BT,Map<BT,BT>>>
                checkListInnerParameterizedType(component, pField, (ParameterizedType) argTypes[1], highLevel);
            } else {
                // List<Map<BT,BT>> & List<Map<BT,OT>>
                Class<?> valueType = (Class<?>) argTypes[1];
                if (isBasicType(valueType)) {
                    return;
                }
                checkInnerClass(component, pField, valueType, level);
            }
        }
    }

    public static boolean isBasicType(Class<?> clz) {
        return clz.isPrimitive() || basicTypeSet.contains(clz);
    }

    private boolean isCollectionType(Class<?> clz) {
        return List.class.isAssignableFrom(clz) || Set.class.isAssignableFrom(clz);
    }

    /**
     * 查找自定义定时器
     **/
    public void findCustomizeTimer() {
        for (ActComponent component : components) {
            String componentName = component.getClass().getName();
            Method[] methods = component.getClass().getMethods();
            for (Method method : methods) {
                boolean isTimer = method.getAnnotation(Scheduled.class) != null;
                if (isTimer) {
                    logger.info("{} {} find timer method {}", component.getComponentId(), componentName, method.getName());
                }

                boolean isPostConstruct = method.getAnnotation(PostConstruct.class) != null;
                if (isPostConstruct) {
                    logger.info("{} {} find PostConstruct method {}", component.getComponentId(), componentName, method.getName());
                }
            }
        }
    }


}
