package com.yy.gameecology.hdzj.element.component.attr;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption: 商城属性
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
public class ShopComponentAttr extends ComponentAttr {

    // 支付方式 - 自己维护的虚拟货币
    public static final int PAY_WAY_SELF = 0;

    // 支付方式 - 资产系统维护的虚拟货币
    public static final int PAY_WAY_SETTLE_SYSTEM = 1;

    /**
     * 支付方式
     */
    @ComponentAttrField(labelText = "支付方式", remark = "1-内部货币，2-虚拟资产系统货币")
    private int payWay = PAY_WAY_SELF;

    /**
     * 商城名称，展示使用
     */
    @ComponentAttrField(labelText = "商店名称")
    private String shopName = "";
    /**
     * 商城所属的业务
     */
    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private long busiId;
    /**
     * 商品走中台发放的taskId
     */
    @ComponentAttrField(labelText = "奖池id")
    private long taskId;

    /**
     * 商城开始展示时间，空的是不限制
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ComponentAttrField(labelText = "商城开始展示时间")
    private Date startShowDate;

    /**
     * 商城开始允许兑换的开始时间，不设置以活动时间为准，不允许比活动开始时间早
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ComponentAttrField(labelText = "允许兑换的开始时间")
    private Date startDate;
    /**
     * 商城开始允许兑换的结束时间，不设置以活动时间为准，可以比活动开始时间晚
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ComponentAttrField(labelText = "允许兑换的结束时间")
    private Date endDate;

    /**
     * 余量展示的key
     */
    @ComponentAttrField(labelText = "余量展示的key")
    private String remainShowKey = "";

    /**
     * 货币定义：<货币，展示名称>
     */
    @ComponentAttrField(labelText = "货币定义",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "货币"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "展示名称")
            })
    private Map<String, String> currencyMap;
    /**
     * 商品定义：<Id,商品>map
     */
    @ComponentAttrField(labelText = "商品定义",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "商品id"),
                    @SubField(fieldName = Constant.VALUE, type = Commodity.class)
            })
    private LinkedHashMap<String, Commodity> commodityMap;

    /**
     * 兑现提示信息
     */
    @ComponentAttrField(labelText = "兑现提示信息", remark = "exchange-1:手慢了，该奖励数量不足~;exchange-2:您的兑换券不足，快去参与活动获得年度积分吧！",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "错误码"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "展示信息")
            })
    private Map<String, String> tipMsgMap;

    /**
     * 兑换提醒弹窗:是否开启提示
     **/
    @ComponentAttrField(labelText = "是否开启兑换提醒")
    private boolean openNotify = true;
    /**
     * 兑换提醒弹窗：弹出频率，1=活动期间只弹一次，2=每日只弹一次,其余不限制
     */
    @ComponentAttrField(labelText = "兑换提醒频率", dropDownSourceBeanClass = FrequencySource.class)
    private int notifyFrequency = 0;

    /**
     * 兑换提醒弹窗：消息的类型，需要跟pc端同步
     */
    @ComponentAttrField(labelText = "消息的类型")
    private String notifyType = "jifensai_notify_exchange";
    /**
     * 兑换提醒弹窗：消息实体，透传给前端，可以是json
     */
    @ComponentAttrField(labelText = "消息实体")
    private String notifyValue = "";

    /**
     * 兑换提醒弹窗：需要弹窗的日期，格式yyyymmdd，空表示不限制
     */
    @ComponentAttrField(labelText = "需要弹窗的日期", remark = "兑换提醒弹窗：需要弹窗的日期，格式yyyymmdd，空表示不限制,多个日期用逗号分隔",
            subFields = {
                    @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class)
            })
    private List<String> notifyDays = Lists.newArrayList();

    public int getPayWay() {
        return payWay;
    }

    public void setPayWay(int payWay) {
        this.payWay = payWay;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public long getBusiId() {
        return busiId;
    }

    public void setBusiId(long busiId) {
        this.busiId = busiId;
    }

    public long getTaskId() {
        return taskId;
    }

    public void setTaskId(long taskId) {
        this.taskId = taskId;
    }

    public Date getStartShowDate() {
        return startShowDate;
    }

    public void setStartShowDate(Date startShowDate) {
        this.startShowDate = startShowDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getRemainShowKey() {
        return remainShowKey;
    }

    public void setRemainShowKey(String remainShowKey) {
        this.remainShowKey = remainShowKey;
    }

    public Map<String, String> getCurrencyMap() {
        return currencyMap;
    }

    public void setCurrencyMap(Map<String, String> currencyMap) {
        this.currencyMap = currencyMap;
    }

    public LinkedHashMap<String, Commodity> getCommodityMap() {
        return commodityMap;
    }

    public void setCommodityMap(LinkedHashMap<String, Commodity> commodityMap) {
        this.commodityMap = commodityMap;
    }

    public Map<String, String> getTipMsgMap() {
        return tipMsgMap;
    }

    public void setTipMsgMap(Map<String, String> tipMsgMap) {
        this.tipMsgMap = tipMsgMap;
    }

    public int getNotifyFrequency() {
        return notifyFrequency;
    }

    public void setNotifyFrequency(int notifyFrequency) {
        this.notifyFrequency = notifyFrequency;
    }

    public String getNotifyType() {
        return notifyType;
    }

    public void setNotifyType(String notifyType) {
        this.notifyType = notifyType;
    }

    public String getNotifyValue() {
        return notifyValue;
    }

    public void setNotifyValue(String notifyValue) {
        this.notifyValue = notifyValue;
    }

    public List<String> getNotifyDays() {
        return notifyDays;
    }

    public void setNotifyDays(List<String> notifyDays) {
        this.notifyDays = notifyDays;
    }

    public boolean isOpenNotify() {
        return openNotify;
    }

    public void setOpenNotify(boolean openNotify) {
        this.openNotify = openNotify;
    }

    public static class Commodity {
        @ComponentAttrField(labelText = "商品id")
        private String id;
        /**
         * 发放的包名，所有的走中台的礼物都要在同一个taskId下，特殊id通过制定  releaseType 自定义发放
         */
        @ComponentAttrField(labelText = "奖包id")
        private Long packageId;
        /**
         * 发放的数量，默认是1
         */
        @ComponentAttrField(labelText = "发放数量")
        private int count;
        /**
         * 商品名称-展示
         */
        @ComponentAttrField(labelText = "商品名称")
        private String name;
        @ComponentAttrField(labelText = "商品图标")
        private String icon;

        /**
         * 商品状态，1是正常状态，0不展示，2展示但是不允许购买
         */
        @ComponentAttrField(labelText = "商品状态", remark = "1是正常状态，0不展示，2展示但是不允许购买")
        private int status = 1;
        /**
         * 购买日限制 ,0是不限制
         */
        @ComponentAttrField(labelText = "日限制", remark = "0是不限制")
        private Integer dayLimit = 0;
        /**
         * 购买总限制 ,0是不限制
         */
        @ComponentAttrField(labelText = "总限制", remark = "0是不限制")
        private Integer totalLimit = 0;
        /**
         * 用户购买限制 ,0是不限制
         */
        @ComponentAttrField(labelText = "用户总限制", remark = "0是不限制")
        private Integer userLimit = 0;
        /**
         * 用户日限制
         */
        @ComponentAttrField(labelText = "用户日限制")
        private Integer userDayLimit = 0;
        /**
         * 发放类型，默认是""走中台，其他要自己实现
         */
        @ComponentAttrField(labelText = "发放类型", remark = "默认是\"\"走中台，其他要自己实现")
        private String releaseType = "";

        /**
         * 主要用于标示实物 1:直接发放 2:实物，走人工发放
         */
        @ComponentAttrField(labelText = "发放类型", remark = "1:直接发放 2:实物，走人工发放")
        private int type = 1;
        /**
         * 价格,key是货币中的key
         */
        @ComponentAttrField(labelText = "价格")
        private Map<String, Long> price;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Long getPackageId() {
            return packageId;
        }

        public void setPackageId(Long packageId) {
            this.packageId = packageId;
        }

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public Integer getDayLimit() {
            return dayLimit;
        }

        public void setDayLimit(Integer dayLimit) {
            this.dayLimit = dayLimit;
        }

        public Integer getTotalLimit() {
            return totalLimit;
        }

        public void setTotalLimit(Integer totalLimit) {
            this.totalLimit = totalLimit;
        }

        public Integer getUserLimit() {
            return userLimit;
        }

        public void setUserLimit(Integer userLimit) {
            this.userLimit = userLimit;
        }

        public Integer getUserDayLimit() {
            return userDayLimit;
        }

        public void setUserDayLimit(Integer userDayLimit) {
            this.userDayLimit = userDayLimit;
        }

        public String getReleaseType() {
            return releaseType;
        }

        public void setReleaseType(String releaseType) {
            this.releaseType = releaseType;
        }

        public Map<String, Long> getPrice() {
            return price;
        }

        public void setPrice(Map<String, Long> price) {
            this.price = price;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }
}
