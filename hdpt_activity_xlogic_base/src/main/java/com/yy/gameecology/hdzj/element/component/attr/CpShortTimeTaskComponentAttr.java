package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import lombok.Data;

import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-10-18 16:03
 **/
@Data
public class CpShortTimeTaskComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    protected int busiId;

    @ComponentAttrField(labelText = "榜单变化事件-榜单")
    private long rankId;

    @ComponentAttrField(labelText = "榜单变化事件-阶段")
    private long phaseId;

    @ComponentAttrField(labelText = "榜单变化事件-厅角色id")
    private long actorSubChannelId;

    @ComponentAttrField(labelText = "获取webdb信息的templateType", remark = "交友 1 聊天室 810")
    private int templateType = 810;

    @ComponentAttrField(labelText = "起飞金额(榜单变化分数)", remark = "送出1笔礼物金额大于等于这个金额的时候就会开启飞行")
    private long insertCpMinScore;

    @ComponentAttrField(labelText = "起飞持续时间(秒)")
    private long cpDurSeconds;

    @ComponentAttrField(labelText = "飞行开始结束时间使用模拟时间")
    private boolean timeRangeUserVirTime;

    @ComponentAttrField(labelText = "挂件飞行记录王显示时间（秒）")
    private long cpTop1LayerShowSeconds;

    @ComponentAttrField(labelText = "飞行记录王结算延迟")
    private long hourSettleDelayMill = 2000;


    @ComponentAttrField(labelText = "小时榜发奖", remark = "",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "达成分值", remark = "-1代表奖池不足时发放的奖品"),
                    @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "奖励配置")
            })
    private Map<Long, AwardAttrConfig> hourRankAward = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "总奖池", remark = "单位厘")
    private long totalPool;

    @ComponentAttrField(labelText = "广播模板", remark = "2==宝贝 3==交友 5==语音房(技能卡)")
    private int broTemplate;


    @ComponentAttrField(labelText = "mp4特效url")
    private String mp4Url;

    @ComponentAttrField(labelText = "优先级", remark = "特效排队显示优先级，值越小，优先级越高；目前默认全屏礼物特效的优先级为999，如果优先级低于全屏礼物特效优先级，需要该值大于999")
    private int mp4Level;

    @ComponentAttrField(labelText = "mp4 key配置", remark = "",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "key"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "value")
            })
    private Map<String, String> mp4LayerExtKeyValues = Maps.newLinkedHashMap();


    @ComponentAttrField(labelText = "祝福抽奖组件索引id")
    private long chatLotteryIndex;
}
