package com.yy.gameecology.hdzj;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.hdzt.RankingTimeEnd;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.thrift.hdztranking.Rank;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 活动组件助手类，放一些公用静态方法
 */
public class HdzjHelper {

    /**
     * 查找当前到达的分值区段 - by guoliping
     * @param currScore - 当前分值
     * @param levelScores - 所有等级分值list
     */
    public static long findLevelScore(long currScore, List<Long> levelScores) {
        if(CollectionUtils.isEmpty(levelScores)) {
            return -1;
        }

        // 新建一个list存放原来的集合做排序（防止影响原来的集合元素顺序）
        List<Long> list = Lists.newArrayList(levelScores);
        Collections.sort(list);
        int size = list.size();

        for (int i = size - 1; i >= 0; i--) {
            long levelScore = list.get(i);
            if (currScore >= levelScore) {
                return levelScore;
            }
        }

        return -2;
    }

    /**
     * 提取榜单变化事件的标识性子Key，一般用于组合一个完整的key - by guoliping
     */
    public static String getRankingScoreChangedSubKey(RankingScoreChanged event, boolean bForPhase) {
        String endTime = event.getOccurTime();
        Date date = DateUtil.getDate(endTime, DateUtil.DEFAULT_PATTERN);
        String dateStr = TimeKeyHelper.getTimeCode(event.getTimeKey(), date);
        String phaseId = bForPhase ? String.valueOf(event.getPhaseId()) : "";
        return String.format("rscs_%s_%s_%s_%s", event.getUri(), event.getRankId(), phaseId, dateStr);
    }

    /**
     * 解析位置奖励配置，同一个位置不能出现多次奖励，否则报错 - by guoliping
     */
    public static Map<Integer, Map<Long, Map<Long, Integer>>> parseRankAwardConfig(Map<String, Map<Long, Map<Long, Integer>>> rankAwardConfig) {
        Map<Integer, Map<Long, Map<Long, Integer>>> map = Maps.newTreeMap();
        for(String key : rankAwardConfig.keySet()) {
            // key内容示例：1,3,8,9-20,31 ，每个值用 , 分开，连续值用 - 指示
            Map<Long, Map<Long, Integer>> awardConfig = rankAwardConfig.get(key);
            String[] strs = key.split(",");
            for(String str : strs) {
                if(str.contains("-")){
                    String[] ranks = str.split("-");
                    if(ranks.length != 2) {
                        throw new SuperException("奖励配置无效", SuperException.E_CONF_ILLEGAL);
                    }
                    int fromRank = Integer.parseInt(ranks[0].trim());
                    int toRank = Integer.parseInt(ranks[1].trim());
                    if(fromRank > toRank) {
                        throw new SuperException("奖励配置无效", SuperException.E_CONF_ILLEGAL);
                    }
                    for(int i=fromRank; i<= toRank; i++) {
                        if(map.containsKey(i)) {
                            throw new SuperException("奖励配置出现重复", SuperException.E_CONF_ILLEGAL);
                        } else {
                            map.put(i, awardConfig);
                        }
                    }
                } else {
                    int rank = Integer.parseInt(str.trim());
                    if(map.containsKey(rank)) {
                        throw new SuperException("奖励配置出现重复", SuperException.E_CONF_ILLEGAL);
                    } else {
                        map.put(rank, awardConfig);
                    }
                }
            }
        }
        return map;
    }

    /**
     * 解析位置mysql配置，同一个位置不能出现多次mysql配置，否则报错 - by guoliping
     */
    public static Map<String, Set<Integer>> parseRankMysqlConfig(Map<String, String> rankMysqlConfig) {
        Map<String, Set<Integer>> result = Maps.newHashMap();
        for(String mysql : rankMysqlConfig.keySet()) {
            // 安全起见，只允许 insert 操作！！！
            if(!mysql.toUpperCase().startsWith("INSERT ")) {
                continue;
            }

            Set<Integer> map = Sets.newTreeSet();
            // value 内容示例：1,3,8,9-20,31 ，每个值用 , 分开，连续值用 - 指示
            String key = rankMysqlConfig.get(mysql);
            String[] strs = key.split(",");
            for(String str : strs) {
                if(str.contains("-")){
                    String[] ranks = str.split("-");
                    if(ranks.length != 2) {
                        throw new SuperException("奖励配置无效", SuperException.E_CONF_ILLEGAL);
                    }
                    int fromRank = Integer.parseInt(ranks[0].trim());
                    int toRank = Integer.parseInt(ranks[1].trim());
                    if(fromRank > toRank) {
                        throw new SuperException("奖励配置无效", SuperException.E_CONF_ILLEGAL);
                    }
                    for(int i=fromRank; i<= toRank; i++) {
                        if(map.contains(i)) {
                            throw new SuperException("奖励配置出现重复", SuperException.E_CONF_ILLEGAL);
                        } else {
                            map.add(i);
                        }
                    }
                } else {
                    int rank = Integer.parseInt(str.trim());
                    if(map.contains(rank)) {
                        throw new SuperException("奖励配置出现重复", SuperException.E_CONF_ILLEGAL);
                    } else {
                        map.add(rank);
                    }
                }
            }
            result.put(mysql, map);
        }
        return result;
    }

    /**
     * 转换类型， 使用方法：convertType(skinList, new TypeReference<List<TSkin>>(){});
     */
    public static <T> T convertType(Object content, TypeReference<T> valueType) {
        if (content == null) {
            return null;
        }
        if(content instanceof  String) {
            return JSON.parseObject((String) content, valueType);
        } else {
            return JSON.parseObject(JSON.toJSONString(content), valueType);
        }
    }



    /**
     * 制作一个替换用 map
     */
    public static Map<String, String> makeReplaceMap(RankingTimeEnd event, Rank bean) {
        Map<String, String> map = Maps.newHashMap();
        String member = bean.getMember();
        map.put("##score##", String.valueOf(bean.score));
        map.put("##rank##", String.valueOf(bean.getRank()));
        map.put("##member##", member);
        String[] strs = member.split("\\|");
        // 将 member 分解（若非组合成员  member[1] 会等于 member
        for (int i = 0; i < strs.length; i++) {
            map.put(String.format("##member[%s]##", i + 1), strs[i]);
        }
        map.put("##seq##", event.getSeq());
        map.put("##ekey##", event.getEkey());
        map.put("##endTime##", DateUtil.format(event.getEndTime()));

        map.put("##actId##", String.valueOf(event.getActId()));
        map.put("##rankId##", String.valueOf(event.getRankId()));
        return map;
    }

    public static Map<String, String> makeReplaceMap(PhaseTimeEnd event, Rank bean) {
        Map<String, String> map = Maps.newHashMap();
        String member = bean.getMember();
        map.put("##score##", String.valueOf(bean.score));
        map.put("##rank##", String.valueOf(bean.getRank()));
        map.put("##member##", member);
        map.put("##endTime##", DateUtil.format(event.getEndTime()));
        String[] strs = member.split("\\|");
        for (int i = 0; i < strs.length; i++) {
            map.put(String.format("##member[%s]##", i + 1), strs[i]);
        }
        map.put("##seq##", event.getSeq());
        map.put("##ekey##", event.getEkey());
        map.put("##actId##", String.valueOf(event.getActId()));
        map.put("##rankId##", String.valueOf(event.getRankId()));
        map.put("##phaseId##", String.valueOf(event.getPhaseId()));
        return map;
    }

    /**
     * 将 sql 中匹配 map key 的地方替换成 map 的 value
     */
    public static String replace(String sql, Map<String, String> map) {
        for(Map.Entry<String, String> entry : map.entrySet()) {
            sql = sql.replace(entry.getKey(), entry.getValue());
        }
        return sql;
    }

}
