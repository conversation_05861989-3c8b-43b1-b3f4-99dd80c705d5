package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.event.*;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.NickExt;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.client.WebdbThriftClientForSa;
import com.yy.gameecology.common.consts.BroadcastType;
import com.yy.gameecology.common.consts.FstAppBroadcastType;
import com.yy.gameecology.common.consts.GeActAttrConst;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.MatchBigBannerMember;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.MatchBigBannerComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.MatchBigBannerItem;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.*;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-02-26 20:25
 **/
@RequestMapping("/5083")
@RestController
@Component
public class MatchBigBannerComponent extends BaseActComponent<MatchBigBannerComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    protected UserInfoService userInfoService;

    @Autowired
    private WebdbThriftClientForSa webdbThriftClientForSa;

    /**
     * 【目前只支持3和6】组件类型 1.alert 2.toast 3.svga横幅 4. 通用web弹窗 5. mp4特效 6. svga特效
     */
    private final static int SVGA_CONENT_TYPE = 6;


    @Override
    public Long getComponentId() {
        return ComponentId.MATCH_BIG_BANNER;
    }

    @RequestMapping("/testBro")
    public Response<?> testBro(long actId, long cmptId, long uid, long rankId, long phaseId) {
        if (SysEvHelper.isDeploy()) {
            return Response.success(null);
        }
        MatchBigBannerComponentAttr attr = getComponentAttr(actId, cmptId);

        PhaseTimeEnd phaseTimeEnd1 =new PhaseTimeEnd();
        phaseTimeEnd1.setActId(actId);
        phaseTimeEnd1.setRankId(rankId);
        phaseTimeEnd1.setPhaseId(phaseId);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+
                "_"+phaseTimeEnd1.getPhaseId()+"_"+System.currentTimeMillis());
        handlePhaseTimeEnd(phaseTimeEnd1, attr);

        return Response.ok();
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void handlePhaseTimeEnd(PhaseTimeEnd event, MatchBigBannerComponentAttr attr) {
        if (event.getRankId() != attr.getEventSettleRankId() || event.getPhaseId() != attr.getEventSettlePhaseId()) {
            return;
        }

        log.info("match bro big banner begin,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSON(attr));

        if (attr.getDelayMill() > 0) {
            log.info("match bro big banner  begin delay:{}", attr.getDelayMill());
            SysEvHelper.waiting(attr.getDelayMill());
        }

        final RoleType roleType = RoleType.findByValue(attr.getRoleType());
        String attrValue = cacheService.getActAttrValue(attr.getActId(), GeActAttrConst.USE_NICK_EXT, "0");
        final boolean useNickExt = (roleType == RoleType.ANCHOR || roleType == RoleType.USER || roleType == RoleType.WAITER) && "1".equals(attrValue);
        List<MatchBigBannerMember> broDataList = Lists.newArrayList();
        Set<Long> uids = new HashSet<>(attr.getSettleRankConfig().size() * 5);
        for (long rankId : attr.getSettleRankConfig().keySet()) {
            MatchBigBannerItem config = attr.getSettleRankConfig().get(rankId);
            long top = config.getIndexRank() == null ? config.getTop() : Math.max(config.getIndexRank(), config.getTop());
            List<Rank> ranks = hdztRankingThriftClient.queryRanking(event.getActId(), rankId, config.getPhaseId(), "", top, Maps.newHashMap());
            for (Rank rank : ranks) {
                if (rank.getScore() < config.getMinScore()) {
                    log.info("match bro big banner skip,member:{},score:{}", rank.getMember(), rank.getScore());
                    continue;
                }
                if(config.getIndexRank() != null && config.getIndexRank() > 0) {
                    if(rank.getRank() != config.getIndexRank()) {
                        log.info("match bro big banner skip, index:{} rank:{}", config.getIndexRank(), rank.getRank());
                        continue;
                    }
                }
                MatchBigBannerMember member = new MatchBigBannerMember();
                member.setRankId(rankId);
                member.setRank(rank.getRank());
                if(rank.getMember().contains("|") && useNickExt) {
                    String userUid = rank.getMember().split("\\|")[0];
                    String anchorUid = rank.getMember().split("\\|")[1];
                    List<String> cps = Lists.newArrayList(userUid, anchorUid);
                    member.setCps(cps);
                    member.setCp(true);
                } else {
                    member.setMemberId(rank.getMember());
                }

                if (useNickExt && StringUtils.isNumeric(rank.getMember())) {
                    uids.add(Convert.toLong(rank.getMember()));
                } else if(member.isCp()) {
                    for (String cp : member.getCps()) {
                        uids.add(Convert.toLong(cp));
                    }
                } else {
                    MemberInfo memberInfo = memberInfoService.getMemberInfo(attr.getBusiId(), RoleType.findByValue(attr.getRoleType()), rank.getMember());
                    if (memberInfo != null) {
                        String memberName = memberInfo.getName() == null ? "" : memberInfo.getName();
                        member.setNick(Base64Utils.encodeToString(memberName.getBytes()));
                        member.setHeader(memberInfo.getHdLogo());
                    }
                }

                broDataList.add(member);
            }
        }

        if (CollectionUtils.isEmpty(broDataList)) {
            log.warn("match bro big banner  load empty rank");
            return;
        }

        Map<String, Map<String, MultiNickItem>> multiNickUsers = null;
        if (!uids.isEmpty()) {
            final Map<Long, UserInfoVo> userInfoMap;
            BatchUserInfoWithNickExt batched = webdbThriftClientForSa.batchGetUserInfoWithNickExt(List.copyOf(uids));
            if (batched == null || MapUtils.isEmpty(batched.getUserInfoMap())) {
                log.error("trying to get user info with nick ext fail");
                userInfoMap = userInfoService.getUserInfo(List.copyOf(uids), Template.unknown);
            } else {
                userInfoMap = UserInfoService.getUserInfo(batched.getUserInfoMap());
                if (StringUtils.startsWith(batched.getNickExt(), StringUtil.OPEN_BRACE)) {
                    NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
                    multiNickUsers = nickExt.getUsers();
                }
            }

            for (MatchBigBannerMember bannerMember : broDataList) {
                if (!StringUtils.isNumeric(bannerMember.getMemberId()) && !bannerMember.isCp()) {
                    continue;
                }
                if(bannerMember.isCp()) {
                    List<String> cps = bannerMember.getCps();
                    UserInfoVo userInfo = userInfoMap.get(Convert.toLong(cps.get(0)));
                    UserInfoVo anchorInfo = userInfoMap.get(Convert.toLong(cps.get(1)));
                    List<String> nicks = new ArrayList<>();
                    List<String> headers = new ArrayList<>();
                    nicks.add(Base64Utils.encodeToString(userInfo.getNick().getBytes()));
                    nicks.add(Base64Utils.encodeToString(anchorInfo.getNick().getBytes()));
                    headers.add(userInfo.getAvatarUrl());
                    headers.add(anchorInfo.getAvatarUrl());
                    bannerMember.setNicks(nicks);
                    bannerMember.setHeaders(headers);
                    bannerMember.setNick(Base64Utils.encodeToString(userInfo.getNick().getBytes()));
                    bannerMember.setHeader(userInfo.getAvatarUrl());
                } else {
                    long uid = Long.parseLong(bannerMember.getMemberId());
                    UserInfoVo userInfo = userInfoMap.get(uid);
                    if (userInfo != null) {
                        bannerMember.setNick(Base64Utils.encodeToString(userInfo.getNick().getBytes()));
                        bannerMember.setHeader(userInfo.getAvatarUrl());
                    }
                }
            }
        }

        log.info("match bro big banner  load rank:{}", JSON.toJSONString(broDataList));
        Map<String, Object> extData = Maps.newHashMap();
        extData.put("dataList", broDataList);
        if (MapUtils.isNotEmpty(attr.getAppBannerSvgaUrl())) {
            String url = attr.getAppBannerSvgaUrl().get(broDataList.size());
            extData.put("svgaUrl", url);
        }
        log.info("match bro big banner extData:{}",  JSON.toJSON(extData));

        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(event.getActId()).setJsonData(JSON.toJSONString(extData)).setBannerId(attr.getBannerId()).setBannerType(0)
                .setMultiNickUsers(MapUtils.isEmpty(multiNickUsers) ? StringUtils.EMPTY : JSON.toJSONString(multiNickUsers))
                .build();

        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        String seq = makeKey(attr, StringUtil.isNotBlank(event.getEkey()) ? event.getEkey() : event.getSeq());
        log.info("match bro big banner seq:{}", seq);
        RetryTool.withRetryCheck(event.getActId(), seq, DateUtil.ONE_DAY_SECONDS, () -> {
            broadCastHelpService.broadcast(event.getActId(), BusiId.findByValue((int) attr.getBusiId()), BroadcastType.ALL_TEMPLATE, 0, 0, bannerBroMsg);
            log.info("handlePhaseTimeEnd bro done");
        });

        String appBannerSeq = "appBanner_" + makeKey(attr, StringUtil.isNotBlank(event.getEkey()) ? event.getEkey() : event.getSeq());
        RetryTool.withRetryCheck(event.getActId(), appBannerSeq, DateUtil.ONE_DAY_SECONDS, () -> {
            appBanner(appBannerSeq, attr, broDataList, uids);
        });

    }


    public void appBanner(String seq, MatchBigBannerComponentAttr attr, List<MatchBigBannerMember> broDataList, Set<Long> nickExtUids) {
        if (MapUtils.isEmpty(attr.getAppBannerSvgaUrl())) {
            log.warn("not config appbanner svga url");
            return;
        }

        AppBannerSvgaConfig2 broSvgaConfig = new AppBannerSvgaConfig2();
        //svga内嵌文字
        List<Map<String, AppBannerSvgaText>> broContentLayers = getSvagTextConfig(attr, broDataList, nickExtUids);
        broSvgaConfig.setContentLayers(broContentLayers);
        //svga内嵌图片
        List<Map<String, String>> broImgLayers = getSvgaImageConfig(attr, broDataList);
        broSvgaConfig.setImgLayers(broImgLayers);

        broSvgaConfig.setJump(0);
        broSvgaConfig.setHeight(0);
        broSvgaConfig.setDuration(10);
        broSvgaConfig.setLoops(1);

        AppBannerLayout layout = new AppBannerLayout();
        layout.setType(0);

        broSvgaConfig.setLayout(layout);


        broSvgaConfig.setLayout(layout);

        broSvgaConfig.setLevel(1);
        broSvgaConfig.setWhRatio("20:13");
        String url = attr.getAppBannerSvgaUrl().get(broDataList.size());
        broSvgaConfig.setSvgaURL(url);

        //广播范围，如在黑名单内只广播本频道
        int broType = FstAppBroadcastType.ALL_TEMPLATE;

        long sid = 0L;
        long ssid = 0L;
        try {
            String memberId = broDataList.get(0).getMemberId();
            String[] memberArray = memberId.split("_");
            sid = Convert.toLong(memberArray[0]);
            ssid = memberArray.length == 2 ? Convert.toLong(memberArray[1]) : sid;
        } catch (Exception e) {

        }

        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, attr.getAppBannerBusiId(),
                broType, sid, ssid, "",
                Lists.newArrayList());
        appBannerEvent.setUidList(List.copyOf(nickExtUids));

        appBannerEvent.setContentType(SVGA_CONENT_TYPE);
        appBannerEvent.setAppId(commonService.getTurnoverAppId((int) attr.getBusiId()));
        appBannerEvent.setSvgaConfig(broSvgaConfig);
        //kafkaService.sendAppBannerKafka(appBannerEvent);
        log.info("processSupperAppBanner app done seq:{}, member:{} event:{}", seq, JSON.toJSONString(broDataList), JSON.toJSONString(appBannerEvent));

        List<Map<String, String>> layerKeys = new ArrayList<>();
        for (Map<String, AppBannerSvgaText> broContentLayer : broContentLayers) {
            for (String key : broContentLayer.keySet()) {
                Map<String, String> map = new HashMap<>();
                map.put(key, broContentLayer.get(key).getText());
                layerKeys.add(map);
            }
        }
        for (Map<String, String> broImgLayer : broImgLayers) {
            for (String key : broImgLayer.keySet()) {
                Map<String, String> map = new HashMap<>();
                map.put(key, broImgLayer.get(key));
                layerKeys.add(map);
            }
        }
        broMp42App(attr.getActId(), seq, attr.getAppBannerBusiId(), attr.getBcType(), 0L, 0L, List.copyOf(nickExtUids),
                broSvgaConfig.getSvgaURL(), 999, layerKeys);
    }

    public void broMp42App(long actId, String seq, int business, int bcType, long sid,
                           long ssid, List<Long> uids, String mp4Url, int broLevel, List<Map<String, String>> layerKeyValues) {
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(actId, seq, business,
                bcType, sid, ssid, "",
                Lists.newArrayList());
        appBannerEvent.setAppId(34);
        appBannerEvent.setUid(0);
        appBannerEvent.setUidList(uids);
        appBannerEvent.setContentType(5);
        appBannerEvent.setPushUidlist(uids);
        AppBannerMp4Config appBannerMp4Config = new AppBannerMp4Config();
        appBannerMp4Config.setUrl(mp4Url);
        appBannerMp4Config.setLevel(broLevel);
        appBannerMp4Config.setLayerExtKeyValues(layerKeyValues);
        appBannerEvent.setMp4Config(appBannerMp4Config);
        kafkaService.sendAppBannerKafka(appBannerEvent);
        log.info("app bro mp4 done seq:{}, event:{}", seq, JSON.toJSONString(appBannerEvent));
    }

    /**
     * svga内嵌纯图片内容配置
     */
    private List<Map<String, String>> getSvgaImageConfig(MatchBigBannerComponentAttr attr, List<MatchBigBannerMember> broDataList) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        if (MapUtils.isEmpty(attr.getAppBannerImgLayers())) {
            return broImgLayers;
        }

        for (int i = 0; i < broDataList.size(); i++) {
            MatchBigBannerMember member = broDataList.get(i);
            Map<String, String> keyConfig = attr.getAppBannerImgLayers().get(i);
            for (var entry : keyConfig.entrySet()) {
                String imageKey = entry.getKey(), imageValue = entry.getValue();
                if(member.isCp()) {
                    if ("{header}".equals(imageValue) && member.getHeaders().getFirst() != null) {
                        broImgLayers.add(Map.of(imageKey, member.getHeaders().getFirst()));
                    } else if ("{anchorHeader}".equals(imageValue) && member.getHeaders().get(1) != null) {
                        broImgLayers.add(Map.of(imageKey, member.getHeaders().get(1)));
                    } else {
                        broImgLayers.add(Map.of(imageKey, imageValue));
                    }
                } else {
                    if ("{header}".equals(imageValue) && member.getHeader() != null) {
                        broImgLayers.add(Map.of(imageKey, member.getHeader()));
                    } else {
                        broImgLayers.add(Map.of(imageKey, imageValue));
                    }
                }
            }
        }

        return broImgLayers;
    }


    /**
     * svga 内嵌文字配置
     */
    private List<Map<String, AppBannerSvgaText>> getSvagTextConfig(MatchBigBannerComponentAttr attr, List<MatchBigBannerMember> broDataList, Set<Long> nickExtUids) {
        List<Map<String, AppBannerSvgaText>> broContentLayers = Lists.newArrayList();
        for (int i = 0; i < broDataList.size(); i++) {
            MatchBigBannerMember member = broDataList.get(i);
            String memberId = member.getMemberId();
            boolean useMultiNick = false;
            if (!nickExtUids.isEmpty() && StringUtils.isNumeric(memberId)) {
                useMultiNick = nickExtUids.contains(Long.parseLong(memberId));
            }
            Map<String, AppBannerSvgaText> broSvgaTextLayer = Maps.newHashMap();
            List<BannerSvgaTextConfig> textConfigs = attr.getAppBannerTextTemplate().get(i);
            for (BannerSvgaTextConfig textConfig : textConfigs) {
                if (textConfig == null) {
                    continue;
                }
                AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();

                //配置动态替换文本
                String text = textConfig.getText();
                if (useMultiNick) {
                    text = text.replace("{nick}", String.format("{%s:n}", memberId));
                } else {
                    text = text.replace("{nick}", new String(Base64Utils.decodeFromString(member.getNick())));
                }
                if(member.isCp()) {
                    text = text.replace("{nick}", String.format("{%s:n}", member.getCps().get(0)));
                    text = text.replace("{anchorNick}", String.format("{%s:n}", member.getCps().get(1)));
                }

                appBannerSvgaText.setText(text);
                appBannerSvgaText.setNameCountLimit(textConfig.getNameCountLimit());
                appBannerSvgaText.setGravity(textConfig.getGravity());
                if (StringUtil.isNotBlank(textConfig.getImages())) {
                    appBannerSvgaText.setImgs(Lists.newArrayList(textConfig.getImages().split(",")));
                }
                if (StringUtil.isNotBlank(textConfig.getFontSize())) {
                    appBannerSvgaText.setFontSize(JSON.parseObject(textConfig.getFontSize(), Map.class));
                }
                broSvgaTextLayer.put(textConfig.getKey(), appBannerSvgaText);
            }

            if (MapUtils.isNotEmpty(broSvgaTextLayer)) {
                broContentLayers.add(broSvgaTextLayer);
            }


        }

        return broContentLayers;
    }
}
