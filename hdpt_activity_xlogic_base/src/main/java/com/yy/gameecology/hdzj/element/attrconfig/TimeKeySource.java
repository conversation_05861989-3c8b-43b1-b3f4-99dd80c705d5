package com.yy.gameecology.hdzj.element.attrconfig;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/30 14:11
 **/
@Component
public class TimeKeySource implements DropDownSource {
    @Override
    public List<DropDownVo> listDropDown() {
        // [{"code":"0","desc":"不按时间再分(0)"},{"code":"1","desc":"按日再分(1)"},{"code":"2","desc":"按小时再分(2)"},{"code":"3","desc":"按周分(3)"},{"code":"4","desc":"按月分(4)"},{"code":"5","desc":"按季分(5)"},{"code":"6","desc":"按年分(6)"}]
        return Arrays.asList(
                new DropDownVo("0", "不按时间再分(0)"),
                new DropDownVo("1", "按日再分(1)"),
                new DropDownVo("2", "按小时再分(2)"),
                new DropDownVo("3", "按周分(3)"),
                new DropDownVo("4", "按月分(4)"),
                new DropDownVo("5", "按季分(5)"),
                new DropDownVo("6", "按年分(6)")
        );
    }
}
