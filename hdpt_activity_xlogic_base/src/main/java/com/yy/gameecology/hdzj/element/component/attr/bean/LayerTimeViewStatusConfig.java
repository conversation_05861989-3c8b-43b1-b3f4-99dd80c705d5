package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-12-29 18:05
 **/
@Data
public class LayerTimeViewStatusConfig {
    @ComponentAttrField(labelText = "开始时间")

    private long beginTime;
    @ComponentAttrField(labelText = "结束时间")
    private long endTime;

    @ComponentAttrField(labelText = "挂件状态")
    private int viewStatus;
}
