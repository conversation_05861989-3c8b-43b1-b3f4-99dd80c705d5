package com.yy.gameecology.hdzj.element.component.attr;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.annotation.SkipCheck;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户付费成长体系组件
 *
 * <AUTHOR>
 * @date 2022/3/28 15:56
 **/
@Data
@SkipCheck
public class UserPaidGrowthComponentAttr extends ComponentAttr {

    /**
     * 活动阶段
     **/
    private List<PhaseConfig> phaseList;

    // *** 经验获得相关配置 ***

    /**
     * 活动礼物列表
     **/
    private List<GiftConfig> giftList;

    private List<FreeGiftConfig> freeGiftConfigs;

    /**
     * 奖励属性配置
     */
    private List<AwardAttrConfig> awardList = Collections.emptyList();


    /**
     * 经验倍数
     * 第一层 key --> 对应到活动阶段id
     * 第二层 key --> 对应到阈值
     * 第三层 key --> 对应的经验倍数
     **/
    private Map<Integer, Map<Long, Double>> experienceMultipleMap;

    /**
     * 默认的经验倍数
     * key --> 对应到阈值
     * value --> 对应的经验倍数
     **/
    private Map<Long, Double> defaultExperienceMultipleMap;

    /**
     * 经验榜单的角色id
     **/
    private long rankRoleId = 50003;

    /**
     * 经验榜单的itemId
     **/
    private String experienceRankItemId = "YHCZ_HDJY";

    /**
     * 威望榜单的itemId
     **/
    private String giftItemId = "YHCZ_HDLW";

    /**
     * 经验获得气泡提示次数
     **/
    private long bubbleTipsThreshold = 2;

    /**
     * 经验值每日上限,小于等于0则不限制
     **/
    private long experienceDailyLimit;


    // *** 经验升级相关配置 ***

    /**
     * 等级升级的榜单id
     **/
    private long levelUpgradeRankId;
    /**
     * 等级升级的榜单阶段id
     **/
    private long levelUpgradePhaseId;

    /**
     * 等级升级的奖池id
     **/
    private long levelUpgradeAwardTaskId;
    /**
     * 紫水晶奖包id
     **/
    private long amethystCouponAwardPackageId;

    /**
     * 等级配置
     **/
    private List<LevelConfig> levelConfigs;

    /**
     * 等级升级横幅svga
     **/
    private String levelUpgradeSvgaUrl;

    /**
     * 最大等级
     **/
    private int maxLevel = 4;

    // *** H5相关配置 ***
    /**
     * 威望值总榜id
     **/
    private long rankId;

    /**
     * 威望值总榜阶段id
     **/
    private long phaseId;

    /**
     * 勋章hover h5弹窗宽度
     **/
    private int h5WindowWidth;
    /**
     * 勋章hover h5弹窗高度
     **/
    private int h5WindowHeight;
    /**
     * 勋章hover h5跳转url
     **/
    private String h5Url;

    /**
     * 紫水晶券数量上限
     */
    private long amethystCouponCeiling;

    private String tipsNoticeType;

    private String tipsNoticeValue;

    private String tipsNoticeExtJson;

    public GiftConfig getGiftConfig(long giftId, long now) {
        for (GiftConfig gift : giftList) {
            if (gift.getGiftId() == giftId && gift.getStartTime() <= now && gift.getEndTime() >= now) {
                return gift;
            }
        }

        return null;
    }

    /**
     * 等级升级对应的奖包
     * key --> 等级
     * value --> 奖包id
     **/
    public Map<Long, Long> getLevelUpgradePackageMap() {
        return levelConfigs.stream().collect(Collectors.toMap(LevelConfig::getLevel, LevelConfig::getLevelUpgradePackageId));
    }

    public Map<Long, Map<Long, Integer>> getDelayAwardMap() {
        if (CollectionUtils.isEmpty(levelConfigs)) {
            return Collections.emptyMap();
        }

        Map<Long, Map<Long, Integer>> result = Maps.newHashMapWithExpectedSize(levelConfigs.size());
        for (LevelConfig config : levelConfigs) {
            String delayAwardMap = config.getDelayAwardMap();
            if (StringUtils.startsWith(delayAwardMap, "{")) {
                Map<Long, Integer> map = JSON.parseObject(delayAwardMap, new TypeReference<HashMap<Long, Integer>>() {
                });
                result.put(config.getLevel(), map);
            }
        }

        return result;
    }

    /**
     * 等级升级横幅广播范围
     * key --> 等级
     * value --> 广播范围 2 = 子频道广播，3=顶级频道广播，4=本业务模板内广播，5=活动业务广播
     **/
    public Map<Long, Integer> getLevelUpgradeBroadcastRangeMap() {
        return levelConfigs.stream().collect(Collectors.toMap(LevelConfig::getLevel, LevelConfig::getLevelUpgradeBroadcastRange));
    }

    /**
     * 等级升级对应的勋章url
     * key --> 等级
     * value --> 勋章url
     **/
    public Map<Long, String> getLevelMedalMap() {
        return levelConfigs.stream().collect(Collectors.toMap(LevelConfig::getLevel, LevelConfig::getMedalUrl));
    }

    /**
     * 等级升级对应的勋章url
     * key --> 等级
     * value --> 勋章url
     **/
    public Map<Long, String> getLevelSvgaMap() {
        return levelConfigs.stream().collect(Collectors.toMap(LevelConfig::getLevel, LevelConfig::getSvgaUrl));
    }

    /**
     * 等级对应的经验值
     * key --> 经验值
     * value --> 等级
     **/
    public Map<Long, Long> getLevelExperienceMap() {
        return levelConfigs.stream().collect(Collectors.toMap(LevelConfig::getExperience, LevelConfig::getLevel));
    }

    public FreeGiftConfig getFreeGiftConfig(long giftId) {
        return freeGiftConfigs.stream().filter(gift -> gift.getGiftId() == giftId).findFirst().orElse(null);
    }

    /**
     * 活动阶段
     **/
    @Data
    public static class PhaseConfig {
        /**
         * 阶段开始时间
         **/
        private long phaseStartTime;
        /**
         * 阶段结束时间
         **/
        private long phaseEndTime;
        /**
         * 阶段id
         **/
        private Integer phaseId;
        /**
         * 下一可领取阶段id
         **/
        private Integer nextPhaseId;
        /**
         * 当前活动id
         **/
        private long currentActId;
        /**
         * 当前活动名称
         **/
        private String currentActName;
        /**
         * 当前活动页url
         **/
        private String currentActUrl;
        /**
         * 礼物id,逗号分隔
         **/
        private String giftIds;
    }

    /**
     * 礼物配置
     **/
    @Data
    public static class GiftConfig {
        /**
         * 礼物id
         **/
        private long giftId;
        /**
         * 礼物名称
         **/
        private String giftName;
        /**
         * 开始时间
         **/
        private long startTime;
        /**
         * 结束时间
         **/
        private long endTime;
        /**
         * 礼物图标
         **/
        private String giftIcon;
    }

    /**
     * 经验等级配置
     **/
    @Data
    public static class LevelConfig {
        /**
         * 等级
         **/
        private Long level;
        /**
         * 所需经验值
         **/
        private Long experience;
        /**
         * 升级的奖包id
         **/
        private Long levelUpgradePackageId;

        /**
         * 延迟发放的奖品的packageId=发放的数量
         */
        private String delayAwardMap;

        /**
         * 广播范围：2 = 子频道广播，3=顶级频道广播，4=本业务模板内广播，5=活动业务广播
         **/
        private Integer levelUpgradeBroadcastRange;
        /**
         * 勋章url
         **/
        private String medalUrl;
        /**
         * svga Url
         **/
        private String svgaUrl;
    }

    @Data
    public static class AwardAttrConfig {
        private long packageId;

        private String awardName;

        private String awardIcon;

        private String unit;

        private String tipTemplate;
    }

    @Data
    public static class FreeGiftConfig {
        protected long giftId;

        protected long prestige;

        protected long exp;

        protected long effectLimit;
    }
}


