package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

/**
 * @Author: CXZ
 * @Desciption: 新用户首次模板属性
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
public class NewUserFirstEnterComponentAttr extends ComponentAttr {

    /**
     * 处理的业务
     */
    @ComponentAttrField(labelText = "业务id列表", remark = "多个逗号分隔,200:游戏生态,400:游戏宝贝,500:交友,600:约战,900:陪玩"
            , subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Long[] busiIds;

    @ComponentAttrField(labelText = "老用户缓存key")
    private String oldUserSetKey;

    public Long[] getBusiIds() {
        return busiIds;
    }

    public void setBusiIds(Long[] busiIds) {
        this.busiIds = busiIds;
    }

    public String getOldUserSetKey() {
        return oldUserSetKey;
    }

    public void setOldUserSetKey(String oldUserSetKey) {
        this.oldUserSetKey = oldUserSetKey;
    }
}
