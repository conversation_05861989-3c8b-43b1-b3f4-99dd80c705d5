package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.EnrollmentDto;
import com.yy.gameecology.activity.bean.rankroleinfo.ContractInfo;
import com.yy.gameecology.activity.bean.rankroleinfo.RoleItem;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.HdztActorInfo;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Validated
@Service
public class EnrollmentService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private HdztActorInfoService hdztActorInfoService;
    @Autowired
    private HdztRankGenRoleService hdztRankGenRoleService;

    @Lazy
    @Autowired
    private CommonService commonService;

    @Autowired
    private SignedService signedService;

    @Autowired
    @Lazy
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private RedisConfigManager redisConfigManager;


    private static Long DEFAULT_HIT = 12L;
    Map<String, Long> BUSIID_ROLE_TYPE_HIT_MAP = new ImmutableMap.Builder<String, Long>()
            .put("900-200", 1L)
            .build();

    //接收活动广播专用分组
    public static final String BRO_GROUP_ID = "99999";

    private static final int USE_TYPE_ZT = 1;

    private static final int USE_TYPE_ZK = 2;

    private static final int USE_TYPE_DENY = -1;

    // 第一层key： actId， 第二层key：memberId; value - 报名信息（0 ~ n个）
    private Map<Long, Map<String, List<EnrollmentInfo>>> enrollmentInfoMap = Maps.newConcurrentMap();

    //普通参赛成员，排除广播专用分组 第一层key 活动id , 第二次 key 成员id
    private Map<Long, Map<String, List<EnrollmentInfo>>> enrollmentInfoNormalMap = Maps.newHashMap();

    //活动的免战列表,第一层key 活动id , 第二次 key rankId|phaseId
    private Map<Long, Map<String, List<EnrollmentInfo>>> actFreeBattlesMap = Maps.newHashMap();

    @PostConstruct
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    public void loadEnrollmentInfoData() {
        Clock clock = new Clock();
        Set<Long> actIds = Sets.newHashSet();
        List<ActivityInfoVo> activityInfoVos = hdztRankingThriftClient.queryEffectActInfos();
        if (CollectionUtils.isNotEmpty(activityInfoVos)) {
            actIds.addAll(activityInfoVos.stream().map(ActivityInfoVo::getActId).collect(Collectors.toSet()));
        }
        //加载数据白名单
        String actIdConfig = Const.GEPM.getParamValue("load_enrollment_act_whitelist", "");
        if (!StringUtils.isBlank(actIdConfig)) {
            actIds.addAll(Arrays.stream(actIdConfig.split(",")).map(Convert::toLong).collect(Collectors.toSet()));
        }
        if(CollectionUtils.isEmpty(actIds)){
            log.info("actId is empty");
            return;
        }

        List<EnrollmentInfo> infoList = Lists.newArrayList();

        Map<Long, Map<String, List<EnrollmentInfo>>> actFreeBattlesMap = Maps.newHashMap();
        for (Long actId : actIds) {
            List<EnrollmentInfo> enrollmentInfos = hdztRankingThriftClient.queryEnrollmentInfo(actId);
            if (CollectionUtils.isNotEmpty(enrollmentInfos)) {
                infoList.addAll(enrollmentInfos);
                actFreeBattlesMap.put(actId, getFreeBattlesMap(enrollmentInfos));
            }
        }

        log.info("loadEnrollmentInfoData@size:{} {}", infoList.size(), clock.tag());
        Map<Long, Map<String, List<EnrollmentInfo>>> tmpInfoMaps = Maps.newConcurrentMap();
        Map<Long, Map<String, List<EnrollmentInfo>>> tmpInfoNormalMaps = Maps.newConcurrentMap();
        for (EnrollmentInfo info : infoList) {
            long actId = info.getActId();
            Map<String, List<EnrollmentInfo>> tmpInfoMap = tmpInfoMaps.getOrDefault(actId, Maps.newConcurrentMap());
            List<EnrollmentInfo> list = tmpInfoMap.getOrDefault(info.getMemberId(), Lists.newArrayList());
            list.add(info);
            tmpInfoMap.put(info.getMemberId(), list);
            tmpInfoMaps.put(actId, tmpInfoMap);

            //普通参赛成员（非广播）
            if (!BRO_GROUP_ID.equals(String.valueOf(info.getDestRoleId()))) {
                Map<String, List<EnrollmentInfo>> tmpInfoNormalMap = tmpInfoNormalMaps.getOrDefault(actId, Maps.newConcurrentMap());
                List<EnrollmentInfo> normalList = tmpInfoNormalMap.getOrDefault(info.getMemberId(), Lists.newArrayList());
                normalList.add(info);
                tmpInfoNormalMap.put(info.getMemberId(), normalList);
                tmpInfoNormalMaps.put(actId, tmpInfoNormalMap);
            }
        }

        if (MapUtils.isNotEmpty(tmpInfoMaps)) {
            enrollmentInfoMap = tmpInfoMaps;
        }
        if (!SysEvHelper.isDeploy()) {
            enrollmentInfoNormalMap = tmpInfoNormalMaps;
        } else if (MapUtils.isNotEmpty(tmpInfoNormalMaps)) {
            enrollmentInfoNormalMap = tmpInfoNormalMaps;
        }
        this.actFreeBattlesMap = actFreeBattlesMap;
    }


    public Map<String, List<EnrollmentInfo>> getEntryList(Long actId) {
        return enrollmentInfoMap.getOrDefault(actId, Maps.newHashMap());
    }

    /**
     * 报名配置信息读取
     */
    public List<EnrollmentInfo> getEntryConfigInfo(Long actId, String memberId) {
        return enrollmentInfoMap.getOrDefault(actId, Maps.newHashMap()).get(memberId);
    }

    /**
     * 按角色分类获取成员报名信息，有多个只会返回第一个
     *
     * @param actId
     * @param memberId
     * @param roleType
     * @return
     */
    public EnrollmentInfo getFirstEnrolMember(Long actId, String memberId, Integer roleType) {
        Map<String, List<EnrollmentInfo>> actNormalMember = enrollmentInfoNormalMap.get(actId);
        if (MapUtils.isEmpty(actNormalMember)) {
            return null;
        }
        List<EnrollmentInfo> roleMember = actNormalMember.get(memberId);
        if (CollectionUtils.isEmpty(roleMember)) {
            return null;
        }

        if (roleType == null) {
            return roleMember.get(0);
        }

        for (EnrollmentInfo member : roleMember) {
            if (member.getRoleType() == roleType.longValue()) {
                return member;
            }
        }

        return null;
    }

    //获取成员，如果同1个业务，同1个成员存在相同报名数据，此方法不适用
    public EnrollmentInfo getFirstEnrolMember(Long actId, Long busiId, Integer roleType, String memberId) {
        Map<String, List<EnrollmentInfo>> actNormalMember = enrollmentInfoNormalMap.get(actId);
        if (MapUtils.isEmpty(actNormalMember)) {
            return null;
        }
        List<EnrollmentInfo> roleMember = actNormalMember.get(memberId);
        if (CollectionUtils.isEmpty(roleMember)) {
            return null;
        }
        for (EnrollmentInfo member : roleMember) {
            boolean roleTypeAndBusiMatch = member.getRoleType() == roleType && member.getRoleBusiId() == busiId;
            boolean roleTypeMatchBusiZero = member.getRoleType() == roleType && busiId == 0;
            if (roleTypeAndBusiMatch || roleTypeMatchBusiZero) {
                return member;
            }
        }

        return null;
    }

    //获取成员角色名称，如果同1个业务，同1个成员存在相同报名数据，此方法不适用
    public String getFirstEnrolDestRoleName(Long actId, Long busiId, Integer roleType, String memberId) {
        EnrollmentInfo member = getFirstEnrolMember(actId, busiId, roleType, memberId);
        if (member == null) {
            return "";
        }
        Long roleId = member.getDestRoleId();
        HdztActorInfo hdztActorInfo = hdztActorInfoService.getHdztActorInfo(roleId);
        if (hdztActorInfo == null) {
            return "";
        }

        return hdztActorInfo.getName();
    }

    //获取成员角色名称，如果同1个业务，同1个成员存在相同报名数据，此方法不适用
    public Long getFirstEnrolDestRoleId(Long actId, Long busiId, Integer roleType, String memberId) {
        EnrollmentInfo member = getFirstEnrolMember(actId, busiId, roleType, memberId);
        if (member == null) {
            return 0L;
        }
        return member.getDestRoleId();
    }


    public List<EnrollmentInfo> getNormalEntryConfigInfo(Long actId, Long memberId) {
        return enrollmentInfoNormalMap.getOrDefault(actId, Maps.newHashMap()).get(memberId);
    }

    public int getEntryMemberType(Long actId, Long memberId) {
        List<EnrollmentInfo> entryListInfo = enrollmentInfoMap.getOrDefault(actId, Maps.newHashMap()).get(memberId);
        if (CollectionUtils.isEmpty(entryListInfo)) {
            return 0;
        }
        if (entryListInfo.size() > 1) {
            return -1;
        }
        return Convert.toInt(entryListInfo.get(0).getRoleType(), 0);
    }


    /**
     * 报名配置信息读取
     */
    public EnrollmentInfo getEntryConfigInfo(Long actId, String memberId, Long roleId) {
        List<EnrollmentInfo> enrollmentInfos = enrollmentInfoMap.getOrDefault(actId, Maps.newHashMap()).get(memberId);
        if (CollectionUtils.isEmpty(enrollmentInfos)) {
            return null;
        }
        for (EnrollmentInfo enrollmentInfo : enrollmentInfos) {
            if (enrollmentInfo.getDestRoleId() == roleId) {
                return enrollmentInfo;
            }
        }
        return null;
    }

    public List<EnrollmentInfo> getEntryConfigInfosBySign(Long actId, Long roleId, Long signSid) {
        Map<String, List<EnrollmentInfo>> actNormalMember = enrollmentInfoNormalMap.get(actId);
        if (MapUtils.isEmpty(actNormalMember)) {
            return Lists.newArrayList();
        }
        return actNormalMember.values().stream().flatMap(Collection::stream)
                .filter(info -> signSid.equals(info.getSignSid()) && roleId.equals(info.destRoleId))
                .collect(Collectors.toList());
    }

    public EnrollmentDto queryEnroll(@NotNull Long actId, @NotNull String memberId, @NotNull Long roleType) {
        int useType = getUseType(actId, roleType, memberId);
        return queryEnroll(actId, memberId, roleType, useType);
    }

    public EnrollmentDto queryEnroll(@NotNull Long actId, @NotNull String memberId, @NotNull Long roleType, int useType) {
        EnrollmentDto enrollmentDto = new EnrollmentDto(memberId, 1L, "");
        List<EnrollmentInfo> enrollmentInfos = Lists.newArrayList();
        if (useType == USE_TYPE_ZT) {
            enrollmentInfos = hdztRankingThriftClient.queryEnrollmentInfoNocache(actId, roleType, Lists.newArrayList(memberId));
        } else if (useType == USE_TYPE_DENY) {
            enrollmentDto.setMsg("系统维护中，请30分钟后再试");
        }

        if (CollectionUtils.isNotEmpty(enrollmentInfos)) {
            EnrollmentInfo enrollmentInfo = enrollmentInfos.get(0);
            BeanUtils.copyProperties(enrollmentInfo, enrollmentDto);
            enrollmentDto.setRoleId(enrollmentInfo.getSrcRoleId());
            enrollmentDto.setStatus(0L);
        }
        return enrollmentDto;
    }

    public EnrollmentDto saveEnroll(@NotNull Long actId, @NotNull String memberId, @NotNull Long roleId, @NotNull String seq) {
        //报名校验
        HdztActorInfo actorInfo = hdztActorInfoService.getHdztActorInfo(roleId);

        Assert.notNull(actorInfo, "actorInfo is null");

        Long roleType = actorInfo.getType();
        Long busiId = actorInfo.getBusiId();

        int useType = getUseType(actId, roleType, memberId);
        String message = canEnrollment(actId, roleId + "", roleType, memberId, useType);
        if (StringUtils.isNotBlank(message)) {
            return new EnrollmentDto(memberId, 1L, message);
        }

        return saveEnroll(actId, memberId, roleId, roleType, busiId, seq, useType);
    }

    /**
     * 按报名角色分类查找报名信息,多个报名信息只会取一个
     * 缓存没有报名信息 会实时查找中台报名信息
     *
     * @param actId
     * @param members
     * @param roleType
     * @return
     */
    public Map<String, EnrollmentInfo> getFirstEnrollmentInfoMap(long actId, List<String> members, RoleType roleType) {

        Map<String, EnrollmentInfo> enrollInfoMap = Maps.newHashMap();
        //填充签约信息--报名信息中获取本地缓存
        for (String member : members) {
            EnrollmentInfo enrollmentInfo = getFirstEnrolMember(actId, member, roleType.getValue());
            if (enrollmentInfo != null) {
                enrollInfoMap.put(member, enrollmentInfo);
            }
        }

        List<String> noSignMembers = Lists.newArrayList(members);
        noSignMembers.removeAll(enrollInfoMap.keySet());
        //报名表缓存中没有的，从中台实时查询
        if (!noSignMembers.isEmpty()) {
            List<EnrollmentInfo> enrollmentInfos = hdztRankingThriftClient.queryEnrollmentInfoNocache(actId, (long) roleType.getValue(), noSignMembers);
            if (enrollmentInfos != null) {
                for (EnrollmentInfo enrollmentInfo : enrollmentInfos) {
                    enrollInfoMap.putIfAbsent(enrollmentInfo.getMemberId(), enrollmentInfo);
                }
            }
        }
        return enrollInfoMap;
    }

    /**
     * 报名控制
     *
     * @param actId
     * @param roleId
     * @return
     */
    private String canEnrollment(Long actId, String roleId, Long roleTye, String memberId, int useType) {

        String enrollmentConfig = commonService.getActAttr(actId, "enrollmentConfig");
        if (StringUtils.isBlank(enrollmentConfig) || !enrollmentConfig.contains(roleId)) {
            return "只有陪玩才可以报名哦~";
        }
        JSONArray configs = JSONArray.parseArray(enrollmentConfig);
        Date startTime = null;
        Date endTime = null;
        for (int i = 0; i < configs.size(); i++) {
            JSONObject config = configs.getJSONObject(i);
            String keys = Optional.ofNullable(config.getString("roleIds")).orElse("");

            if (keys.contains(roleId)) {
                startTime = DateUtil.getDate(config.getString("startTime"), DateUtil.DEFAULT_PATTERN);
                endTime = DateUtil.getDate(config.getString("endTime"), DateUtil.DEFAULT_PATTERN);
                break;
            }
        }
        Assert.isTrue(startTime != null && endTime != null, "actId:" + actId + ",roleId:" + roleId + ",enrollmentConfig error," + enrollmentConfig);
        Long now;
        //中控用当前时间
        if (useType == USE_TYPE_ZK) {
            now = System.currentTimeMillis();
        } else {
            now = commonService.getNow(actId).getTime();
        }
        if (startTime.getTime() > now) {
            return "报名未开始";
        }
        if (endTime.getTime() <= now) {
            return "报名已截止";
        }

        return "";
    }

    public EnrollmentDto saveEnroll(Long actId, String memberId, Long roleId, Long roleType, Long busiId, String seq, int useType) {


        EnrollmentDto enrollmentDto = queryEnroll(actId, memberId, roleType, useType);
        if (useType == -1) {
            return enrollmentDto;
        } else if (enrollmentDto.getRoleId() != 0) {
            enrollmentDto.setMsg("不可以重复报名哦~");
            return enrollmentDto;
        }
        List<HdztActorInfo> actorInfos = hdztActorInfoService.getHdztActorInfosByBusiId(busiId);
        //获取业务角色类型的第一位角色ID
        Long firstRoleType = actorInfos.stream().filter(actorInfo -> busiId.equals(actorInfo.getBusiId())
                        && roleType.equals(actorInfo.getType()))
                .map(HdztActorInfo::getRole).sorted().findFirst()
                .orElse(roleType);
        //生成角色信息
        long hit = BUSIID_ROLE_TYPE_HIT_MAP.getOrDefault(busiId + "-" + roleType, DEFAULT_HIT);
        RoleItem roleItem = hdztRankGenRoleService.genRankItemMap(0L, 0L, busiId, roleType
                , firstRoleType, hit, Lists.newArrayList(memberId), 0L).get(memberId);

        //主播，接待，约战主持-有签约才能报名
        //工会/厅-要找的要信息（陪玩的工会要用运营id）
        //其他 不允许报名
        Assert.isTrue(roleItem instanceof ContractInfo, "无法转换成签约对象。");

        enrollmentDto.setStatus(1L);
        Long signSid = ((ContractInfo) roleItem).getConSid();
        Long signASid = ((ContractInfo) roleItem).getConAsid();
        if (signSid == null || signSid <= 0) {
            enrollmentDto.setMsg("只有陪陪才可报名哦~");
            return enrollmentDto;
        }

        EnrollmentInfo enrollmentInfo = new EnrollmentInfo();
        enrollmentInfo.setActId(actId);
        enrollmentInfo.setRoleType(roleType);
        enrollmentInfo.setRoleBusiId(busiId);
        enrollmentInfo.setMemberId(memberId);

        enrollmentInfo.setSrcRoleId(roleId);
        enrollmentInfo.setDestRoleId(roleId);

        enrollmentInfo.setSignSid(signSid);
        enrollmentInfo.setSignAsid(signASid);

        enrollmentInfo.setStatus(1L);
        enrollmentInfo.setStartPhaseId(0L);
        enrollmentInfo.setStartRankId(0L);
        int result = 1;

        if (useType == USE_TYPE_ZT) {
            result = hdztRankingThriftClient.saveEnrollment(actId, enrollmentInfo, false, "web报名"
                    , seq);
        } else if (useType == USE_TYPE_ZK) {
            throw new RuntimeException("不能使用中控报名");
        }


        Assert.isTrue(result >= 0, "thrift 状态码返回异常");
        enrollmentDto = queryEnroll(actId, memberId, roleType, useType);
        enrollmentDto.setStatus(result + 0L);
        enrollmentDto.setMsg(result == 0 ? "不可以重复报名哦~" : "报名成功");

        return enrollmentDto;
    }

    /**
     * @param actId
     * @param roleTye
     * @param memberId
     * @return
     */
    private int getUseType(Long actId, Long roleTye, String memberId) {

        String enrollmentUseTypeConfig = commonService.getActAttr(actId, "enrollmentUseTypeConfig");
        //不允许报名
        if (String.valueOf(USE_TYPE_DENY).equals(enrollmentUseTypeConfig)) {
            return USE_TYPE_DENY;
        }
        //只走中台
        else if (String.valueOf(USE_TYPE_ZT).equals(enrollmentUseTypeConfig)) {
            return USE_TYPE_ZT;
        }
        //只走中控
        else if (String.valueOf(USE_TYPE_ZK).equals(enrollmentUseTypeConfig)) {
            return USE_TYPE_ZK;
        }
        //默认 白名单走中台，非单名单走中控
        else {
            RoleType type = RoleType.findByValue(roleTye.intValue());
            if (commonService.isGrey(actId) && hdztRankingThriftClient.checkWhiteList(actId, type, memberId)) {
                return USE_TYPE_ZT;
            } else {
                return USE_TYPE_ZK;
            }
        }
    }

    /**
     * 成员在指定的榜单和阶段是否在免战白名单内
     */
    public boolean inFreeBattleList(long actId, long rankId, long phaseId, long roleType, String memberId) {
        List<EnrollmentInfo> enrollmentInfos =
                enrollmentInfoMap.getOrDefault(actId, Collections.emptyMap())
                        .getOrDefault(memberId, Collections.emptyList());

        Optional<EnrollmentInfo> enrollmentInfoOptional = enrollmentInfos.stream()
                .filter(info -> info.getRoleType() == roleType).findFirst();

        String extJson = enrollmentInfoOptional.map(EnrollmentInfo::getExtData)
                .map(extMap -> extMap.get("extData")).orElse("");

        if (StringUtils.isBlank(extJson) || !JSONValidator.from(extJson).validate()) {
            return false;
        }
        JSONObject extObject = JSON.parseObject(extJson);
        String key = String.format("freebattle|%s|%s", rankId, phaseId);
        return Const.ONE.equals(extObject.getInteger(key));
    }

    public List<EnrollmentInfo> getFreeBattles(long actId, long rankId, long phaseId) {
        String key = rankId + "|" + phaseId;
        return actFreeBattlesMap.getOrDefault(actId, Collections.emptyMap()).getOrDefault(key, Lists.newArrayList());
    }

    /**
     * 获取报名信息里面的免战key rankId|phaseId
     *
     * @param enrollmentInfo
     * @return
     */
    private Map<String, EnrollmentInfo> getFreeBattleMap(EnrollmentInfo enrollmentInfo) {
        if (MapUtils.isEmpty(enrollmentInfo.getExtData())) {
            return Maps.newHashMap();
        }
        String extJson = enrollmentInfo.getExtData().getOrDefault("extData", "");
        final String freebattle = "freebattle";
        if (extJson.contains(freebattle) && JSONValidator.from(extJson).validate()) {

            JSONObject extObject = JSON.parseObject(extJson);
            return extObject.entrySet().stream()
                    .filter(entry -> entry.getKey().startsWith("freebattle") && "1".equals(entry.getValue().toString()))
                    .map(Map.Entry::getKey).map(key -> key.replace("freebattle|", ""))
                    .collect(Collectors.toMap(Function.identity(), (key) -> enrollmentInfo));

        }
        return Maps.newHashMap();
    }

    private Map<String, List<EnrollmentInfo>> getFreeBattlesMap(List<EnrollmentInfo> enrollmentInfos) {
        return enrollmentInfos.stream().map(this::getFreeBattleMap)
                .filter(MapUtils::isNotEmpty).map(Map::entrySet)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(Map.Entry::getKey
                        , Collectors.mapping(Map.Entry::getValue, Collectors.toList())));
    }


    /**
     * 刷新中台主播报名信息签约频道
     *
     * @param actId 活动id
     */
    public void updateEnrollSignSid(long actId) {

        String groupCode = redisConfigManager.getGroupCode(actId);
        String needRunKey = Const.addActivityPrefix(actId, "update_enroll_sign_sid_need");
        if (!Const.ONESTR.equals(actRedisDao.get(groupCode, needRunKey))) {
            log.info("updateEnrollSignSid return,not need run,actId:{}", actId);
            return;
        }

        String key = Const.addActivityPrefix(actId, "update_enroll_sign_sid_flag");
        if (!actRedisDao.setNX(groupCode, key, DateUtil.format(new Date()))) {
            log.info("updateEnrollSignSid return,already set,actId:{}", actId);
            return;
        }

        log.info("updateEnrollSignSid can run,actId:{}", actId);
        Map<String, List<EnrollmentInfo>> enrollMap = enrollmentInfoNormalMap.get(actId);
        for (String memberId : enrollMap.keySet()) {
            List<EnrollmentInfo> enrollmentInfos = enrollMap.get(memberId);
            for (EnrollmentInfo x : enrollmentInfos) {

                if (x.getRoleType() != RoleType.ANCHOR.getValue()) {
                    continue;
                }
                long signSid = signedService.getSignedSidByBusiId(Convert.toLong(x.getMemberId()), x.getRoleBusiId());
                if (signSid == 0) {
                    log.warn("updateEnrollSignSid not found sign sid,actId:{},memberId:{}", actId, x.getMemberId());
                    continue;
                }
                if (signSid == x.getSignSid()) {
                    continue;
                }

                String uuid = UUID.randomUUID().toString();
                log.info("updateEnrollSignSid begin,actId:{},memberId:{},old signSid:{},new signSid:{},uuid:{}"
                        , actId, x.getMemberId(), signSid, x.getSignSid(), uuid);

                EnrollmentInfo update = new EnrollmentInfo();
                update.setActId(x.getActId());
                update.setMemberId(x.getMemberId());
                update.setSrcRoleId(x.getSrcRoleId());
                update.setRoleBusiId(x.getRoleBusiId());
                update.setRoleType(x.getRoleType());
                update.setDestRoleId(x.getDestRoleId());
                update.setStatus(x.getStatus());
                update.setSignSid(signSid);
                long asId = commonService.getAsid(signSid);
                update.setSignAsid(asId);
                update.setStartRankId(x.getStartRankId());
                update.setStartPhaseId(x.getStartPhaseId());
                update.setExtData(x.getExtData());
                int updateRet = hdztRankingThriftClient.saveEnrollment(actId, update, true, "auto update sign sid", uuid);
                log.info("updateEnrollSignSid done,actId:{},memberId:{},old signSid:{},new signSid:{},uuid:{},ret:{}"
                        , actId, x.getMemberId(), x.getSignSid(), signSid, uuid, updateRet);

            }
        }

    }
}
