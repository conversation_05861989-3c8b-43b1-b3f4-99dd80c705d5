package com.yy.gameecology.activity.client.thrift;

import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.thrift.turnover.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025/6/12 17:36
 */
@Service
@Slf4j
public class TUdbRiskGiftSendServiceClient {

    @Reference(protocol = "nythrift_compact", owner = "${turnoverGiftbagStat_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"})
    @Getter
    private TUdbRiskGiftSendService.Iface proxy;

    @Reference(protocol = "nythrift_compact", owner = "${turnoverGiftbagStat_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"}
            , retries = 2 , cluster = "failover")
    @Getter
    private TUdbRiskGiftSendService.Iface readProxy;

    /**
     * 检查送礼合法性
     * @param appid appid
     * @param sid 顶级频道
     * @param ssid 子频道
     * @param sendUid 送礼uid
     * @param anchorUid 当前房间主持uid
     * @param recvUids 收礼人uids
     * @param gameType 玩法类型
     * @param expand 客户端参数
     */
    @Report
    public TCheckIdentityResult checkUsePropsIdentity(TAppId appid, long sid, long ssid, long sendUid, long anchorUid, List<Long> recvUids, int gameType, String expand) {
        try {
            return this.proxy.checkUsePropsIdentity(appid, sid, ssid, sendUid, anchorUid, recvUids, gameType, expand);
        } catch (TServiceException e) {
            log.warn("checkUsePropsIdentity bizFailed appid:{} sid:{} ssid:{} sendUid:{} anchorUid:{} recvUids:{} gameType:{} expand:{} code:{} msg:{}",
                    sid, ssid, sendUid, anchorUid, recvUids, gameType, expand,
                    e.getCode(), e.getMessage(), e);
            throw new SuperException(e.getMessage(), e.getCode());
        } catch (TException e) {
            log.warn("checkUsePropsIdentity failed appid:{} sid:{} ssid:{} sendUid:{} anchorUid:{} recvUids:{} gameType:{} expand:{} msg:{}",
                    sid, ssid, sendUid, anchorUid, recvUids, gameType, expand,
                    e.getMessage(), e);
            throw new SuperException(e.getMessage(), 500);
        }
    }
}
