package com.yy.gameecology.activity.service.datatransfer.impl;


import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.common.consts.GeParamName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2022-09-02 17:17
 **/
@Component
public class BaseRedisData {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Lazy
    @Autowired
    protected ActRedisGroupDao actRedisGroupDao;

    public boolean checkHashKey(String group, String key) {
        if (actRedisGroupDao.getRedisTemplate(group).hasKey(key)) {
            long rs = actRedisGroupDao.hIncrByKey(group, "HAS_KEY_WARD", key, 1, 60);
            final int ten = 10;
            if (rs <= ten) {
                log.warn("复制key目标库已存在，请确认是否有异常,destGroup:{},key:{}", group, key);
                //sendWardMsg(group, key);
            }else{
                log.warn("复制key目标库已存在，请确认是否有异常,destGroup:{},key:{}", group, key);
            }

            return true;
        }

        return false;
    }

    /**
     * 复制完成通知
     */
    private void sendWardMsg(String group, String key) {
        try {
            String buf = "### <font color=\"red\">【中控->redis数据冷化告警】</font>\n" +
                    "复制key目标库已存在，请确认是否有异常" + "\n" +
                    "key：" + key + "\n" +
                    "destGroup：" + group + "\n";
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, buf, null);
        } catch (Throwable e) {
            log.error("sendWardMsg exception@key:{}, err:{}", key, e.getMessage(), e);
        }
    }
}
