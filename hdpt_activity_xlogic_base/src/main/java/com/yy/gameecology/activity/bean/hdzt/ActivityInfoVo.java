package com.yy.gameecology.activity.bean.hdzt;

import java.util.Map;

/**
 * desc:活动基本信息
 *
 * @createBy 曾文帜
 * @create 2020-07-23 11:21
 **/
public class ActivityInfoVo {
    /**
     * 活动id
     */
    private Long actId;
    private Long busiId;
    private String actName;
    private Long status;
    private String actBgUrl;
    private String detailUrl;
    private Long beginTime;
    /**
     * 毫秒
     */
    private Long endTime;
    private Long actType;
    private Long preparationTime;
    private Long rankEndTime;
    private Long beginTimeShow;
    private Long endTimeShow;



    private Long currentTime;
    /**
     *  扩展 map，协商使用
     */
    private Map<String, Object> extData;

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public Long getBusiId() {
        return busiId;
    }

    public void setBusiId(Long busiId) {
        this.busiId = busiId;
    }

    public String getActName() {
        return actName;
    }

    public void setActName(String actName) {
        this.actName = actName;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getActBgUrl() {
        return actBgUrl;
    }

    public void setActBgUrl(String actBgUrl) {
        this.actBgUrl = actBgUrl;
    }

    public String getDetailUrl() {
        return detailUrl;
    }

    public void setDetailUrl(String detailUrl) {
        this.detailUrl = detailUrl;
    }

    public Long getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Long beginTime) {
        this.beginTime = beginTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Long getActType() {
        return actType;
    }

    public void setActType(Long actType) {
        this.actType = actType;
    }

    public Long getPreparationTime() {
        return preparationTime;
    }

    public void setPreparationTime(Long preparationTime) {
        this.preparationTime = preparationTime;
    }

    public Long getRankEndTime() {
        return rankEndTime;
    }

    public void setRankEndTime(Long rankEndTime) {
        this.rankEndTime = rankEndTime;
    }

    public Map<String, Object> getExtData() {
        return extData;
    }

    public void setExtData(Map<String, Object> extData) {
        this.extData = extData;
    }

    public Long getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(Long currentTime) {
        this.currentTime = currentTime;
    }

    public Long getBeginTimeShow() {
        return beginTimeShow;
    }

    public void setBeginTimeShow(Long beginTimeShow) {
        this.beginTimeShow = beginTimeShow;
    }

    public Long getEndTimeShow() {
        return endTimeShow;
    }

    public void setEndTimeShow(Long endTimeShow) {
        this.endTimeShow = endTimeShow;
    }
}
