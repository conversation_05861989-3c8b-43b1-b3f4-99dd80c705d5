package com.yy.gameecology.activity.worker.timer;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.service.ActInfoBroService;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.thrift.hdztranking.BusiId;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * desc:活动浮层广播通用服务
 *
 * @createBy 曾文帜
 * @create 2020-11-09 21:50
 **/
@ConditionalOnExpression(Const.EXPRESSION_NOT_HISTORY)
@Component
public class ActLayerBroTimer {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private TimerSupport timerSupport;

    @Autowired
    private BroActLayerService broActLayerService;

    @Autowired
    private ActInfoBroService actInfoBroService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;


    /**
     * 活动信息广播#约战宝贝交友
     */
    @Scheduled(cron = "0/10 * * * * ?")
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Report
    public void broadcastActInfo() {
        List<ActivityInfoVo> activityInfoVos = hdztRankingThriftClient.queryEffectActInfos();
        if (CollectionUtils.isEmpty(activityInfoVos)) {
            log.warn("activityInfoVos is null");
            return;
        }
        activityInfoVos.forEach(activityInfoVo -> {
            long actId = activityInfoVo.getActId();
            timerSupport.work("LayerBroTimer_broadcastYBJActInfo_" + actId, 30, () -> actInfoBroService.broadcastActInfo(actId));
        });
    }

    /**
     * 浮层挂件信息广播#交友
     */
    @Scheduled(cron = "0/1 * * * * ? ")
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Report
    public void broJiaoyou() {
        long broJyIndex = DateUtil.getHourSeconds(new Date());

        long fre = broActLayerService.getTimerRefreshFrequency();
        log.info("broJiaoyou,index:{},mode:{}", broJyIndex, broJyIndex % fre);

        if (broJyIndex % fre != 0) {
            return;
        }

        List<ActivityInfoVo> activityInfoVos = hdztRankingThriftClient.queryEffectActInfos();
        if (CollectionUtils.isEmpty(activityInfoVos)) {
            log.warn("activityInfoVos is null");
            return;
        }
        activityInfoVos.forEach(x -> {
            List<Integer> broBusiIds = actInfoBroService.getBroBusiIds(x);
            long actId = x.getActId();
            if (broBusiIds.contains(BusiId.MAKE_FRIEND.getValue())) {
                timerSupport.work("LayerBroTimer_broJiaoYouLayer_" + actId, 60,
                        () -> broActLayerService.beginBroLayerInfo(actId, Template.makefriend));
            }
        });
    }

    /**
     * 浮层挂件信息广播#宝贝
     */
    @Scheduled(cron = "0/1 * * * * ? ")
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Report
    public void broBaby() {
        long broBabyIndex = DateUtil.getHourSeconds(new Date());

        long fre = broActLayerService.getTimerRefreshFrequency();
        if (broBabyIndex % fre != 0) {
            return;
        }

        List<ActivityInfoVo> activityInfoVos = hdztRankingThriftClient.queryEffectActInfos();
        if (CollectionUtils.isEmpty(activityInfoVos)) {
            log.warn("activityInfoVos is null");
            return;
        }
        activityInfoVos.forEach(x -> {
            List<Integer> broBusiIds = actInfoBroService.getBroBusiIds(x);
            long actId = x.getActId();
            if (broBusiIds.contains(BusiId.GAME_BABY.getValue())) {
                timerSupport.work("LayerBroTimer_broBabyLayer_broYuezhan_" + actId, 60,
                        () -> broActLayerService.beginBroLayerInfo(actId, Template.gamebaby));
            }
        });
    }

    /**
     * 浮层挂件信息广播#技能卡
     */
    @Scheduled(cron = "0/1 * * * * ? ")
    @NeedRecycle(author = "wangdonghong", notRecycle = true)
    @Report
    public void broSkillCard() {
        broActLayer("LayerBroTimer_broActLayer_broSkillCard_", BusiId.SKILL_CARD.getValue());
    }

    private void broActLayer(String timeLockPrefix, int busiId) {
        long broIndex = DateUtil.getHourSeconds(new Date());
        long fre = broActLayerService.getTimerRefreshFrequency();
        if (broIndex % fre != 0) {
            return;
        }

        List<ActivityInfoVo> activityInfoVos = hdztRankingThriftClient.queryEffectActInfos();
        if (CollectionUtils.isEmpty(activityInfoVos)) {
            log.warn("activityInfoVos is null");
            return;
        }
        activityInfoVos.forEach(x -> {
            List<Integer> broBusiIds = actInfoBroService.getBroBusiIds(x);
            long actId = x.getActId();
            if (broBusiIds.contains(busiId)) {
                timerSupport.work(timeLockPrefix + actId, 60,
                        () -> broActLayerService.beginBroLayerInfo(actId, Template.getTemplateByBusi(busiId)));
            }
        });
    }
}
