package com.yy.gameecology.activity.bean.hdzt;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-23 17:22
 **/
public class RankingPhaseResponse {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    // 服务器真实的时间，精确到毫秒（不受测试用的虚拟时间影响）- 赖冰冰web前端宣发期要用的 - addded by guoliping/20201009
    private long srvTimestamp;

    private ActivityInfoVo activityInfoVo;

    private List<RankingPhaseGroupVo> items;

    public List<RankingPhaseGroupVo> getItems() {
        return items;
    }

    public ActivityInfoVo getActivityInfoVo() {
        return activityInfoVo;
    }

    public void setActivityInfoVo(ActivityInfoVo activityInfoVo) {
        this.activityInfoVo = activityInfoVo;
    }

    public void setItems(List<RankingPhaseGroupVo> items) {
        this.items = items;
    }

    public long getSrvTimestamp() {
        return srvTimestamp;
    }

    public void setSrvTimestamp(long srvTimestamp) {
        this.srvTimestamp = srvTimestamp;
    }
}
