package com.yy.gameecology.activity.bean.ruliu;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/6 19:39
 */
public class BaiduInfoFlowAtMsg extends BaiduInfoFlowMsg {

    private static final List<String> EMPTY_LIST = new ArrayList<>(0);

    private final List<String> atuserids;
    private final Boolean atall;

    /**
     * @param atall     是否@全体成员
     * @param atUserIds 要 @谁，请使用百度邮箱前缀
     */
    public BaiduInfoFlowAtMsg(boolean atall, List<String> atUserIds) {
        super("AT");
        this.atall = atall;
        if (this.atall) {
            this.atuserids = EMPTY_LIST;
            return;
        }
        this.atuserids = atUserIds == null ? EMPTY_LIST : atUserIds;
    }

    public List<String> getAtuserids() {
        return atuserids;
    }

    public Boolean getAtall() {
        return atall;
    }
}
