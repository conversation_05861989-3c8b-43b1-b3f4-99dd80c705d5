package com.yy.gameecology.activity.worker.timer;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.ScheduledExt;
import com.yy.gameecology.activity.service.OnlineSubChannelCacheService;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.locker.Locker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * desc:子频道信息缓存更新
 *
 * @createBy 曾文帜
 * @create 2021-08-05 12:13
 **/
@Component
public class OnlineSubChannelReloadTimer {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OnlineSubChannelCacheService onlineSubChannelCacheService;
    @Autowired
    private TimerSupport timerSupport;

    @Autowired
    private Locker locker;

    /**
     * 刷新到本业务的redis中
     */
    @Scheduled(cron = "0/3 * * * * ? ")
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @ScheduledExt(historyRun = true)
    @Report
    public void update2redis() {
        int lockSeconds = Const.GEPM.getParamValueToInt(GeParamName.ONLINE_SUB_CHANNEL_LOAD_DATA_LOCK_SECONDS, 5);
        timerSupport.workByLocker("gameecology_online_sub_channel_info_cache_update2redis_locker",
                lockSeconds, () -> onlineSubChannelCacheService.load2Redis(), locker, true);
    }

    /**
     * 从redis更新到内存
     */
    @Scheduled(cron = "0/4 * * * * ? ")
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @ScheduledExt(historyRun = true)
    @Report
    public void update2memory() {
        onlineSubChannelCacheService.load2Memory();
    }
}
