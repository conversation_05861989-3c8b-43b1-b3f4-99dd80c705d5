package com.yy.gameecology.activity.service.aov.enroll;

import com.yy.gameecology.activity.client.yrpc.HdptAovClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AovEnrollmentService {

    @Autowired
    private HdptAovClient hdptAovClient;

    public boolean canMobileSignUp(long uid, String mobileHash, int mobileUidLimit, String remark) {
        return hdptAovClient.canMobileEnroll(uid, mobileHash, mobileUidLimit, remark);
    }
}
