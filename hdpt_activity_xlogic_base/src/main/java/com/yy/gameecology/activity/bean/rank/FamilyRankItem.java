package com.yy.gameecology.activity.bean.rank;

import com.yy.gameecology.common.utils.Convert;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/19 15:16
 **/
@Data
public class FamilyRankItem extends PkRankItemBase {
    private Long familyId;
    private Long value;
    private String name;
    private String avatarInfo;

    @Override
    public void setKey(String key) {
        this.familyId = Convert.toLong(key);
    }

    @Override
    public String getKey() {
        return this.familyId + "";
    }

    @Override
    public void setValue(Long value) {
        this.value = value;
    }
}
