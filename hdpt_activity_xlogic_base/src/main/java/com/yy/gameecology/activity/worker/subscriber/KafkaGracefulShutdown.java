package com.yy.gameecology.activity.worker.subscriber;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.core.Ordered;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.stereotype.Component;

import java.util.concurrent.CountDownLatch;

/**
 * 处理kafka 优雅退出 逻辑
 * <h2>背景：</h2>
 * <p>
 * 业务处理kafka消息的时候，会用到spring中的bean（比如 yrpc client，数据库等）<br>
 * 我们业务收到 kafka 消息时，如果此时 spring 容器销毁了, 那么处理 kafka 消息的 bean 可能还没结束 <br>
 * 这个时候就有可能会调用到已被销毁了的 bean （比如 yrpc client， 数据库，redis 等），此时将会报错。
 * 业务处理消息时 调用 spring bean 处理逻辑不报错（kafka 要求先于 spring 销毁）
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/27 18:27
 */
@Slf4j
@Component
public class KafkaGracefulShutdown implements InitializingBean, ApplicationListener<ContextClosedEvent>, Ordered, Runnable {

    @Autowired(required = false)
    private KafkaListenerEndpointRegistry kafkaListenerEndpointRegistry;

    private final CountDownLatch shutdownLatch = new CountDownLatch(1);

    /** stop 后等多久时间允许spring容器关闭 */
    @Value("${shutdown.kafka.after-stop-wait-millis:5000}")
    private long afterStopWaitMillis = 5000;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 注册 shutdown hook
        log.info("shutdown register kafka shutdown hook, afterStopWaitMillis:{} kafkaListenerEndpointRegistry = {}", afterStopWaitMillis, kafkaListenerEndpointRegistry);
        Runtime.getRuntime().addShutdownHook(new Thread(this));
    }

    @Override
    public void run() {
        try {
            // 优雅停机逻辑处理
            long startTime = System.currentTimeMillis();
            log.info("shutdown receive shutdown hook, will shutdown kafka consumer");
            if (this.kafkaListenerEndpointRegistry != null) {
                try {
                    this.kafkaListenerEndpointRegistry.stop();
                } catch (Exception e) {
                    log.warn("shutdown stop kafkaListenerEndpointRegistry error {}", e.getMessage(), e);
                }
                long stopCost = System.currentTimeMillis() - startTime;
                // sleep 足够的时间，让还没处理完的消息继续处理完
                afterStopWaitMillis = afterStopWaitMillis <= 1000 ? 5000 : afterStopWaitMillis;
                try {
                    Thread.sleep(afterStopWaitMillis);
                    log.info("shutdown stop kafkaListenerEndpointRegistry finished stopCost:{} afterStopWaitMillis:{}", stopCost, afterStopWaitMillis);
                } catch (InterruptedException e) {
                    log.warn("shutdown sleep error afterStopWaitMillis:{} errMsg:{}", afterStopWaitMillis, e.getMessage(), e);
                }
            }
        } finally {
            shutdownLatch.countDown();
        }
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        try {
            long startTime = System.currentTimeMillis();
            log.info("shutdown wait kafka shutdownLatch started");
            shutdownLatch.await();
            log.info("shutdown wait kafka shutdownLatch finished, now allow spring closed, cost:{}", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.warn("shutdown kafka shutdownLatch wait error:{}", e.getMessage());
        }
    }

    @Override
    public int getOrder() {
        // 比 yrpc 的早一点
        return -1;
    }
}
