package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.service.datatransfer.RedisDataTransferService;
import com.yy.gameecology.common.consts.ActStatus;
import com.yy.gameecology.common.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-12-23 16:12
 **/
@Component
public class RedisScanTool {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private RedisDataTransferService redisDataTransferService;

    public Set<String> scanActRedisKey(long actId, String keyword) {
        ActivityInfoVo activityInfo = hdztRankingThriftClient.queryActivityNoCache(actId);
        Assert.isTrue(ActStatus.ARCHIVE.equals(activityInfo.getStatus()), "活动非归档状态:" + actId);


        Set<String> keys = redisDataTransferService.scanActRedisKey(actId, false);
        if (StringUtil.isNotBlank(keyword)) {
            keys = keys.stream().filter(p -> p.contains(keyword)).collect(Collectors.toSet());
        }

        return keys;
    }
}
