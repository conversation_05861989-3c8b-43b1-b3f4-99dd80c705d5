package com.yy.gameecology.activity.bean.jiaoyou;

import com.alibaba.fastjson.JSON;

import java.util.List;

/**
 * <AUTHOR> 2019/9/2
 */
public class PropsUseInfo {

    private String SeqID;
    /**
     * 送礼人
     */
    private Long SendUid;
    /**
     * 收礼人
     */
    private Long RecvUid;
    /**
     * 主持人 大部分玩法的计分是给主持人的
     */
    private Long CompereUid;
    private Long Sid;
    private Long Ssid;
    private Long signSid;
    private Long Platform;
    private long Timestamp;
    private List<GiftList> GiftList;
    /**
     * 0 = PC
     * 9 = 交友ios
     * 10 = 交友Android
     * 29 = 手y ios
     * 30 = 手y Android
     * 64 = yy小米运联ios
     * 64 = yy小米运联 Android
     * 77 78 = 追玩
     */
    private Long Channel;
    private Expand Expand;

    public String getSeqID() {
        return SeqID;
    }

    public void setSeqID(String seqID) {
        SeqID = seqID;
    }

    public Long getSendUid() {
        return SendUid;
    }

    public void setSendUid(Long sendUid) {
        SendUid = sendUid;
    }

    public Long getRecvUid() {
        return RecvUid;
    }

    public void setRecvUid(Long recvUid) {
        RecvUid = recvUid;
    }

    public Long getCompereUid() {
        return CompereUid;
    }

    public void setCompereUid(Long compereUid) {
        CompereUid = compereUid;
    }

    public Long getSid() {
        return Sid;
    }

    public void setSid(Long sid) {
        Sid = sid;
    }

    public Long getSsid() {
        return Ssid;
    }

    public void setSsid(Long ssid) {
        Ssid = ssid;
    }

    public Long getSignSid() {
        return signSid;
    }

    public void setSignSid(Long signSid) {
        this.signSid = signSid;
    }

    public Long getPlatform() {
        return Platform;
    }

    public void setPlatform(Long platform) {
        Platform = platform;
    }

    public long getTimestamp() {
        return Timestamp;
    }

    public void setTimestamp(long timestamp) {
        Timestamp = timestamp;
    }

    public List<GiftList> getGiftList() {
        return GiftList;
    }

    public void setGiftList(List<GiftList> giftList) {
        GiftList = giftList;
    }

    public Long getChannel() {
        return Channel;
    }

    public void setChannel(Long channel) {
        Channel = channel;
    }

    public Expand getExpand() {
        return Expand;
    }

    public void setExpand(Expand expand) {
        Expand = expand;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
