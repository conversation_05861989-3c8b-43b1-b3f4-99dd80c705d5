package com.yy.gameecology.activity.processor.ranking;

import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;

import java.util.List;
import java.util.Map;

public interface RankingProcessor {

    List<Object> getRankInfo(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, Map<String, String> ext);

    Long getActId();

    List<Long> getActRankId();

}
