package com.yy.gameecology.activity.bean.rank;


import com.yy.gameecology.common.utils.Convert;


public class WaiterItem extends PkRankItemBase {
    private Long uid;
    private String nick;
    private String avatarInfo;

    /**
     * 签约频道id
     */
    private Long contractSid;

    /**
     * 签约频道短号
     */
    private Long contractAsid;


    @Override
    public void setKey(String key) {
        setUid(Convert.toLong(key));
    }

    @Override
    public String getKey() {
        return getUid()+"";
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public String getAvatarInfo() {
        return avatarInfo;
    }

    public void setAvatarInfo(String avatarInfo) {
        this.avatarInfo = avatarInfo;
    }

    public Long getContractSid() {
        return contractSid;
    }

    public void setContractSid(Long contractSid) {
        this.contractSid = contractSid;
    }

    public Long getContractAsid() {
        return contractAsid;
    }

    public void setContractAsid(Long contractAsid) {
        this.contractAsid = contractAsid;
    }
}
