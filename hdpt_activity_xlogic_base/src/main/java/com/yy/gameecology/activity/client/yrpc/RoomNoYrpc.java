package com.yy.gameecology.activity.client.yrpc;


import com.yy.protocol.pb.zhuiwan.roomno.RoomNo;
import org.apache.dubbo.common.annotation.Yrpc;

/**
 * <AUTHOR>
 * @since 2024/10/31 19:07
 */
public interface RoomNoYrpc {

    @Yrpc(functionName = "skill/card/channel/info/queryRoomNo")
    RoomNo.ListRoomInfoResp queryRoomNo(RoomNo.ListRoomInfoReq req);

    @Yrpc(functionName = "skill/card/channel/info/listFamilyValidRoom")
    RoomNo.ListFamilyValidRoomRsp listFamilyValidRoom(RoomNo.ListFamilyValidRoomReq req);
}
