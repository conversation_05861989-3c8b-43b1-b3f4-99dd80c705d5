
顶层类包说明：
--------------------------------------------
	activity - 当前项目
	
	bean - 高度自定义的数据类，一般只有 get、set方法，不含业务逻辑
	
	client - 本应属于 service 层面，但归类到client的好处是让大家明白这是 封装 “外部服务接口” 的调用，可有简单逻辑
	
	commons - 和本项目相关的公共类，包括一些独立的通用第三方组件
	
	config - 存放各种配置对象，这里主要是 spring boot 的配置
	
	dao - 数据访问，可以是 db、redis、file、 mongdb 等，一般不按业务再分包，但可按技术再划分一次包。
	
	exception - 异常类，代表出现系统错误或业务错误
		
	protocol - 存放各种网络通信框架的协议bean
	
	service - 存放业务逻辑类，会引用 dao 包中的类，本包下可按业务模块再细分
	
	worker - 工作者/工人; 程序入口，包括 命令行、网络服务器、消息订阅处理器、定时器、web请求等，能响应外部请求 或者 主动发起对内、对外的访问
	
	Aomi.java - 奥秘监控相关的辅助类
	
	Main.java - 使用spring boot 标记的应用入口类
	
	