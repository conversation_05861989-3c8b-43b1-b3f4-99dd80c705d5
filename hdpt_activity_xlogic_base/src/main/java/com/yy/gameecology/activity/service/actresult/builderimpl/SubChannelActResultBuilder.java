package com.yy.gameecology.activity.service.actresult.builderimpl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.activity.service.actresult.ActResultMemberLoader;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.java.webdb.WebdbSubChannelInfo;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-11-17 16:33
 **/
@Component
public class SubChannelActResultBuilder extends BuilderBase implements ActResultMemberLoader {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MemberInfoService memberInfoService;

    @Override
    public Map<String, Map<String, MemberInfo>> loadMemberInfo(long actId, long type, long roleType, List<String> memberIds) {
        Map<String, MemberInfo> memberInfoMap = memberInfoService.querySubChannelMemberInfo(memberIds);

        return ImmutableMap.of(builderKey(type, roleType), memberInfoMap);
    }


}
