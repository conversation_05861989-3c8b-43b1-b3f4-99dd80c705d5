package com.yy.gameecology.activity.bean.acttask;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-02-23 10:31
 **/
@Data
public class TaskShowConfig {
    @ComponentAttrField(labelText = "任务名称")
    private String taskName;

    @ComponentAttrField(labelText = "榜单Id")
    private long rankId;

    @ComponentAttrField(labelText = "阶段Id")
    private long phaseId;

    @ComponentAttrField(labelText = "日期分榜", remark = "format(yyyy-MM-dd),留空不按日期分榜")
    private String rankDateFormat = "";

    @ComponentAttrField(labelText = "除以分数转换比例", remark = "不配置默认不转换。有些特殊场景分数，配置单位和展示不一样，需要转换。例如配置是1000厘过任务，页面展示是1块钱过任务，这里要配置1000")
    private String scoreScale = "";

    @ComponentAttrField(labelText = "奖品url")
    private String awardUrl="";

}
