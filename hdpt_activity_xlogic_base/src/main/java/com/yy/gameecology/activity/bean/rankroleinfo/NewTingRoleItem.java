package com.yy.gameecology.activity.bean.rankroleinfo;

import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021.09.13 15:04
 * 交友新厅体系
 * warn: 必须重写set/getKey
 */
public class NewTingRoleItem extends RoleItem {
    private String tingId;

    private long asid;

    private long sid;

    private long ssid;

    @Override
    public void setKey(String key) {
        tingId = key;

        if (StringUtil.ZERO.equals(key)) {
            this.asid = 0;
            this.sid = 0;
            this.ssid = 0;
        } else {
            String[] members = key.split(StringUtil.UNDERSCORE);
            this.sid = Convert.toLong(members[0]);
            this.ssid = Convert.toLong(members[1]);
            this.asid = sid;
        }
    }

    @Override
    public String getKey() {
        return tingId;
    }

    public long getAsid() {
        return asid;
    }

    public void setAsid(long asid) {
        this.asid = asid;
    }

    public long getSid() {
        return sid;
    }

    public void setSid(long sid) {
        this.sid = sid;
    }

    public long getSsid() {
        return ssid;
    }

    public void setSsid(long ssid) {
        this.ssid = ssid;
    }
}
