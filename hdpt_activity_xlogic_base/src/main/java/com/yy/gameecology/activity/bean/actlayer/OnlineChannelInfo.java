package com.yy.gameecology.activity.bean.actlayer;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-28 21:33
 **/
public class OnlineChannelInfo {

    //1-交友 2-约战 3-宝贝 810 技能卡
    private long templateType;

    private long sid;
    private long ssid;
    //首麦主播,交友主持
    private long compereUid;
    //开播视频的主播
    private List<Long> guestList;

    /**
     * 交友玩法
     */
    private long playMode = -1;

    //交友多主持模式下的多个主持的列表
    private List<Long> compereList;

    public List<Long> getEffectAnchorId() {
        Set<Long> all = Sets.newLinkedHashSet();
        if (compereUid != 0) {
            all.add(compereUid);
        }

        if (CollectionUtils.isNotEmpty(compereList)) {
            compereList.stream().filter(uid -> uid != null && uid != 0).forEach(all::add);
        }

        if (CollectionUtils.isNotEmpty(guestList)) {
            guestList.stream().filter(uid -> uid != null && uid != 0).forEach(all::add);
        }

        return Lists.newArrayList(all);
    }

    public int getLayerBabyType() {
        //1-单人视频 2-双人视频 3-多人视频
        int openType = 1;
        final int two = 2;
        if (CollectionUtils.isNotEmpty(guestList) && guestList.size() == two) {
            openType = 2;
        } else if (CollectionUtils.isNotEmpty(guestList) && guestList.size() > two) {
            openType = 3;
        }
        return openType;
    }


    public long getSid() {
        return sid;
    }

    public void setSid(long sid) {
        this.sid = sid;
    }

    public long getSsid() {
        return ssid;
    }

    public void setSsid(long ssid) {
        this.ssid = ssid;
    }

    public long getCompereUid() {
        return compereUid;
    }

    public void setCompereUid(long compereUid) {
        this.compereUid = compereUid;
    }

    public List<Long> getGuestList() {
        return guestList;
    }

    public void setGuestList(List<Long> guestList) {
        this.guestList = guestList;
    }

    public long getPlayMode() {
        return playMode;
    }

    public void setPlayMode(long playMode) {
        this.playMode = playMode;
    }

    public List<Long> getCompereList() {
        return compereList;
    }

    public void setCompereList(List<Long> compereList) {
        this.compereList = compereList;
    }

    public long getTemplateType() {
        return templateType;
    }

    public void setTemplateType(long templateType) {
        this.templateType = templateType;
    }
}
