package com.yy.gameecology.activity.rolebuilder.impl;


import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.rankroleinfo.UserBaseItem;
import com.yy.gameecology.activity.rolebuilder.RoleBuilder;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.support.SpringBeanAwareFactory;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.java.webdb.WebdbUserInfo;
import org.apache.commons.collections.MapUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
public abstract class UserRoleBaseBuilder<T extends UserBaseItem> implements RoleBuilder<T> {

//    protected WebdbThriftClient webdbThriftClient = SpringBeanAwareFactory.getBean(WebdbThriftClient.class);


    /**
     * 从yy获取信息
     *
     * @param members
     * @return
     */
    @Override
    public Map<String, T> buildRankByYy(Set<String> members) {
        if (CollectionUtils.isEmpty(members)){
            return Maps.newHashMap();
        }
        List<Long> uids = members.stream().map(p -> Convert.toLong(p, 0)).toList();
        WebdbThriftClient webdbThriftClient = SpringBeanAwareFactory.getBean(WebdbThriftClient.class);
        Map<Long, WebdbUserInfo> userInfos = webdbThriftClient.batchGetUserInfo(uids);
        T defaultObject = getDefaultObject();
        return members.stream().map(member -> {
            WebdbUserInfo userInfo = userInfos.get(Convert.toLong(member,0));
            String nick = userInfo == null ? defaultObject.getNick() : userInfo.getNick();
            String avatar = WebdbUtils.getLogo(userInfo, defaultObject.getAvatarInfo());
            T rankItem = createBankObject();
            rankItem.setUid(Convert.toLong(member,0));
            rankItem.setNick(nick);
            rankItem.setAvatarInfo(avatar);
            return rankItem;
        }).collect(Collectors.toMap(UserBaseItem::getKey, Function.identity()));
    }

    @Override
    public Map<String, T> buildRankByYyWithNickExt(Set<String> members) {
        if (CollectionUtils.isEmpty(members)) {
            return Collections.emptyMap();
        }

        List<Long> uids = members.stream().map(p -> Convert.toLong(p, 0)).filter(uid -> uid != 0).toList();
        if (uids.isEmpty()) {
            return Collections.emptyMap();
        }
        WebdbThriftClient webdbThriftClient = SpringBeanAwareFactory.getBean(WebdbThriftClient.class);
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(uids);
        if (batched == null || MapUtils.isEmpty(batched.getUserInfoMap())) {
            throw new RuntimeException("can not get user info with nick ext");
        }

        Map<String, WebdbUserInfo> userInfos = batched.getUserInfoMap();
        T defaultObject = getDefaultObject();
        Map<String, T> result = new HashMap<>(members.size());
        boolean set = false;
        for (String member: members) {
            long uid = Convert.toLong(member, 0);
            WebdbUserInfo userInfo = userInfos.get(String.valueOf(uid));
            String nick = userInfo == null ? defaultObject.getNick() : userInfo.getNick();
            String avatar = WebdbUtils.getLogo(userInfo, defaultObject.getAvatarInfo());
            T rankItem = createBankObject();
            rankItem.setUid(uid);
            rankItem.setNick(nick);
            rankItem.setAvatarInfo(avatar);
            if (!set) {
                Map<String,String> viewExt = rankItem.getViewExt();
                if (viewExt == null) {
                    viewExt = new HashMap<>(5);
                    rankItem.setViewExt(viewExt);
                }

                viewExt.put("nickExt", batched.getNickExt());
                set = true;
            }

            result.put(member, rankItem);
        }

        return result;
    }
}
