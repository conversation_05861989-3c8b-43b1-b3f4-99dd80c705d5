package com.yy.gameecology.activity.bean;


import com.yy.gameecology.common.consts.WebdbHost;
import lombok.Data;

import java.util.UUID;

/**
 * @Author: CXZ
 * @Desciption: 桃花还愿屋组件属性
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
@Data
public class UserEnterTemplateEvent {
    private long uid;
    private long sid;
    private long ssid;
    private long busiId;
    private long template;

    private String ip;

    private String extJson;

    private String host;

    private String hdid;

    private WebdbHost hostName = WebdbHost.yomi;

    private String seq = UUID.randomUUID().toString();

    public UserEnterTemplateEvent() {
    }

    public UserEnterTemplateEvent(long uid, long sid, long ssid, long busiId, long template) {
        this.uid = uid;
        this.sid = sid;
        this.ssid = ssid;
        this.busiId = busiId;
        this.template = template;
    }

    public UserEnterTemplateEvent(long uid, long sid, long ssid, long busiId, long template, String extJson) {
        this.uid = uid;
        this.sid = sid;
        this.ssid = ssid;
        this.busiId = busiId;
        this.template = template;
        this.extJson = extJson;
    }
}
