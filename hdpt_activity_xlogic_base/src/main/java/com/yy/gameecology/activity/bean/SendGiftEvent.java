package com.yy.gameecology.activity.bean;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yy.thrift.broadcast.Template;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 2019/8/23
 */
public class SendGiftEvent {

    /**
     * 模板类型
     */
    private Template template;
    /**
     * 消息序列号
     */
    private String seq;
    /**
     * 送礼uid
     */
    private Long sendUid;
    /**
     * 收礼uid
     */
    private Long recvUid;
    /**
     * 礼物id
     */
    private String giftId;
    /**
     * 礼物数量
     */
    private Long giftNum;

    /**
     * <pre>
     * 礼物单价
     * 交友：giftList[x].GiftAmount 单位：厘
     * 追玩：usedInfos[x].amount 单位：厘
     * </pre>
     */
    private Long giftAmount;

    /**
     * 顶级频道号
     */
    private Long sid;
    /**
     * 子频道号
     */
    private Long ssid;
    /**
     * 送礼时间
     */
    private Date eventTime;
    /**
     * 礼物来源渠道
     */
    private GiftSourceChannel sourceChannel;

    private Long signSid;
    /**
     * 拓展字段
     */
    private JSONObject jsonMap = new JSONObject();

    public Template getTemplate() {
        return template;
    }

    public void setTemplate(Template template) {
        this.template = template;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public Long getSendUid() {
        return sendUid;
    }

    public void setSendUid(Long sendUid) {
        this.sendUid = sendUid;
    }

    public Long getRecvUid() {
        return recvUid;
    }

    public void setRecvUid(Long recvUid) {
        this.recvUid = recvUid;
    }

    public String getGiftId() {
        return giftId;
    }

    public void setGiftId(String giftId) {
        this.giftId = giftId;
    }

    public Long getGiftNum() {
        return giftNum;
    }

    public void setGiftNum(Long giftNum) {
        this.giftNum = giftNum;
    }

    public Long getGiftAmount() {
        return giftAmount;
    }

    public void setGiftAmount(Long giftAmount) {
        this.giftAmount = giftAmount;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getSsid() {
        return ssid;
    }

    public void setSsid(Long ssid) {
        this.ssid = ssid;
    }

    public Date getEventTime() {
        return eventTime;
    }

    public void setEventTime(Date eventTime) {
        this.eventTime = eventTime;
    }

    public JSONObject getJsonMap() {
        return jsonMap;
    }

    public void addJsonData(String key, Object value) {
        jsonMap.put(key, value);
    }

    public GiftSourceChannel getSourceChannel() {
        return sourceChannel;
    }

    public void setSourceChannel(GiftSourceChannel sourceChannel) {
        this.sourceChannel = sourceChannel;
    }

    public void setJsonMap(JSONObject jsonMap) {
        this.jsonMap = jsonMap;
    }

    public Long getSignSid() {
        return signSid;
    }

    public void setSignSid(Long signSid) {
        this.signSid = signSid;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    /**
     * 提取简要信息
     */
    public String outline() {
        return String.format("SendGiftEvent[%s, seq:%s, sendUid:%s, recvUid:%s, sid:%s, %s %s]",
                template, seq, sendUid, recvUid, sid, giftId, giftNum);
    }
}
