package com.yy.gameecology.activity.listener;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.core.MethodIntrospector;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import org.springframework.util.ReflectionUtils;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.Set;

@Slf4j
@Component
public class ApplicationDetectorBeanFactoryPostProcessor implements BeanFactoryPostProcessor {

    private static final String APPLICATION_PACKAGE = "com.yy.gameecology";

    private static final String COMMON_PACKAGE = "common";

    private static final String DAO_PACKAGE = "DAO";

    private static final String CLIENT_PACKAGE = "client";

    private static final String DD = "$$";

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        for (String beanName : beanFactory.getBeanDefinitionNames()) {
            BeanDefinition definition = beanFactory.getBeanDefinition(beanName);
            String beanClassName = definition.getBeanClassName();
            if (StringUtils.isEmpty(beanClassName)) {
                continue;
            }

            if (!beanClassName.startsWith(APPLICATION_PACKAGE) || beanClassName.contains(COMMON_PACKAGE) ||
                    beanClassName.contains(DAO_PACKAGE) || beanClassName.contains(CLIENT_PACKAGE)) {
                continue;
            }

            if (beanClassName.contains(DD)) {
                int end = beanClassName.indexOf(DD);
                beanClassName = beanClassName.substring(0, end);
            }

            log.info("application detector get beanName:{} beanClass:{}", beanName, beanClassName);
            Class<?> type = ClassUtils.resolveClassName(beanClassName, null);

            Set<Method> methods = MethodIntrospector.selectMethods(type, (ReflectionUtils.MethodFilter) method ->
                    AnnotatedElementUtils.hasAnnotation(method, PostConstruct.class) || AnnotatedElementUtils.hasAnnotation(method, Scheduled.class));
            if (CollectionUtils.isEmpty(methods)) {
                continue;
            }

            for (Method method : methods) {
                if (!AnnotatedElementUtils.hasAnnotation(method, NeedRecycle.class)) {
                    String name = method.getDeclaringClass().getName() + "." + method.getName();
                    throw new RuntimeException(name + "需要加上NeedRecycle注解,明确是否能进行回收");
                }
            }
        }
    }
}
