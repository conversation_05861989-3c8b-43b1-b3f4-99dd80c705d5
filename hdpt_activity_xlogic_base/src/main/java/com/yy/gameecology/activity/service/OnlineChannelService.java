package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.activity.client.thrift.TurnoverFamilyThriftClient;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.client.yrpc.OnlineChannelClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeActAttrConst;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.protocol.pb.online.channel.OnlineChannel;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.zhuiwan_skillcard.ChannelSeatInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.checkerframework.checker.units.qual.A;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-29 11:04
 **/
@Service
public class OnlineChannelService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public static final int SID_BLOCK_SIZE = 10;

    public static final String GAMEECOLOGY_ONLINE_CHANNEL_INFO_CACHE = Const.addActTempPrefix("gameecology_online_channel_info_cache");

    public static final String SID_GROUP_ = "sid_group_";

    //第一层key sid , 第二层 key ssid
    private Map<Long, Map<Long, OnlineChannelInfo>> onlineChannelMap = Maps.newConcurrentMap();

    //第一层key 主播uid 第二层key 主播在线开播的频道
    private Map<Long, ChannelInfoVo> anchorOnLineChannel = Maps.newHashMap();

    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Lazy
    @Autowired
    private CommonService commonService;

    @Autowired
    private ActRedisGroupDao redisGroupDao;

    @Autowired
    private RedisConfigManager redisConfigManager;
    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;
    @Autowired
    private TurnoverFamilyThriftClient turnoverFamilyThriftClient;
    @Autowired
    private CacheService cacheService; //两个开关，1 加载内存缓存开关 2 加载到redis缓存开关
    @Autowired
    private OnlineChannelClient onlineChannelClient;

    /**
     * 更新到内存中
     */
    @PostConstruct
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    public void update2memory() {
        onlineChannelMap = loadDataFromSourceNew();
        //映射多一份根据主播查所在开播频道数据
        if (MapUtils.isNotEmpty(onlineChannelMap)) {
            Map<Long, ChannelInfoVo> tempChannelVo = Maps.newHashMap();
            for (Long sid : onlineChannelMap.keySet()) {
                Map<Long, OnlineChannelInfo> ssidAnchorOnlineChannelMap = onlineChannelMap.get(sid);
                for (Long ssid : ssidAnchorOnlineChannelMap.keySet()) {
                    OnlineChannelInfo onlineChannelInfo = ssidAnchorOnlineChannelMap.get(ssid);
                    if (onlineChannelInfo != null && CollectionUtils.isNotEmpty(onlineChannelInfo.getEffectAnchorId())) {
                        for (Long uid : onlineChannelInfo.getEffectAnchorId()) {
                            ChannelInfoVo vo = new ChannelInfoVo();
                            vo.setSid(sid);
                            vo.setSsid(ssid);
                            vo.setFirtOnMicUid(uid);
                            tempChannelVo.put(uid, vo);
                        }
                    }
                }
            }
            anchorOnLineChannel = tempChannelVo;
        }
    }

    /**
     * 从在线频道进程获取data
     *
     * @return
     */
    public Map<Long, Map<Long, OnlineChannelInfo>> loadDataFromSourceNew() {
        log.info("use online ser data");
        Map<Long, Map<Long, OnlineChannelInfo>> tmpMap = new HashMap<>();
        com.yy.protocol.pb.online.channel.OnlineChannel.AllOnlieChannelReq req
                = com.yy.protocol.pb.online.channel.OnlineChannel.AllOnlieChannelReq.newBuilder().setBusid("act").build();
        try {
            com.yy.protocol.pb.online.channel.OnlineChannel.AllOnlineChannelRsp rsp = onlineChannelClient.getProxy().getAllOnlineChannel(req);
            if (rsp != null && rsp.getCode() == 0) {
                rsp.getOnlineChannelMapMap().forEach((sid, ssidOnlineChannelMapObject) -> {
                    Map<Long, OnlineChannelInfo> subMap = new HashMap<>();
                    Map<Long, com.yy.protocol.pb.online.channel.OnlineChannel.ChannelInfo> ssidOnlineChannelMap = ssidOnlineChannelMapObject.getChannelInfoMapMap();
                    ssidOnlineChannelMap.forEach((ssid, onlineChannel) -> {
                        OnlineChannelInfo onlineChannelInfo = new OnlineChannelInfo();
                        onlineChannelInfo.setTemplateType(onlineChannel.getTemplateType());
                        onlineChannelInfo.setSid(onlineChannel.getSid());
                        onlineChannelInfo.setSsid(onlineChannel.getSsid());
                        onlineChannelInfo.setCompereUid(onlineChannel.getCompereUid());
                        onlineChannelInfo.setCompereList(onlineChannel.getCompereListList());
                        onlineChannelInfo.setGuestList(onlineChannel.getGuestListList());
                        onlineChannelInfo.setPlayMode(onlineChannel.getPlayMode());
                        subMap.put(ssid, onlineChannelInfo);
                    });
                    tmpMap.put(sid, subMap);
                });
            } else {
                log.error("load online data error, rsp:{}", JsonUtil.toJson(rsp));
            }
        } catch (Exception e) {
            log.error("load online data error, e:{}", ExceptionUtils.getStackTrace(e));
        }
        return tmpMap;
    }

    /**
     * 获取当前在线频道信息
     */
    public OnlineChannelInfo get(Long sid, Long ssid) {
        if (onlineChannelMap.containsKey(sid)) {
            return onlineChannelMap.get(sid).get(ssid);
        }

        return null;
    }

    public Map<Long, Map<Long, OnlineChannelInfo>> getAllOnlineChannelMap() {
        return onlineChannelMap;
    }

    public ChannelInfoVo getChannelInfoVo(long uid) {
        return anchorOnLineChannel.get(uid);
    }

    public List<ChannelInfo> queryOnlineChannelNoCache(Template template) {
        log.info("use online ser data");
        List<ChannelInfo> list = new ArrayList<>();
        com.yy.protocol.pb.online.channel.OnlineChannel.TemplateOnlineChannelReq req = com.yy.protocol.pb.online.channel.OnlineChannel.TemplateOnlineChannelReq.newBuilder().setTemplate(template.getValue()).build();
        com.yy.protocol.pb.online.channel.OnlineChannel.TemplateOnlineChannelRsp rsp = onlineChannelClient.getProxy().getTemplateOnlineChannel(req);
        if (rsp != null && rsp.getCode() == 0) {
            List<com.yy.protocol.pb.online.channel.OnlineChannel.SimpleChannelInfo> simpleChannelInfos = rsp.getChannelInfoList();
            for (OnlineChannel.SimpleChannelInfo simpleChannelInfo : simpleChannelInfos) {
                ChannelInfo channelInfo = new ChannelInfo();
                channelInfo.setSid(simpleChannelInfo.getSid());
                channelInfo.setSsid(simpleChannelInfo.getSsid());
                list.add(channelInfo);
            }
        } else {
            log.error("online channel client getTemplateOnlineChannel error rsp:{}", JsonUtil.toJson(rsp));
        }
        return list;
    }


    public List<OnlineChannelInfo> queryOnlineChannelInfoNoCache(Template template) {
        List<OnlineChannelInfo> result = new ArrayList<>();

        Map<Long, Map<Long, OnlineChannelInfo>> allOnLineChannelInfo = loadDataFromSourceNew();
        List<ChannelInfo> channelInfos = queryOnlineChannelNoCache(template);
        for (ChannelInfo simpleChannelInfo : channelInfos) {
            if (allOnLineChannelInfo.containsKey(simpleChannelInfo.getSid()) && allOnLineChannelInfo.get(simpleChannelInfo.getSid()).containsKey(simpleChannelInfo.getSsid())) {
                result.add(allOnLineChannelInfo.get(simpleChannelInfo.getSid()).get(simpleChannelInfo.getSsid()));
            }
        }
        return result;
    }
}

