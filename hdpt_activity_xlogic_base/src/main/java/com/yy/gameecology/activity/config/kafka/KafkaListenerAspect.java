package com.yy.gameecology.activity.config.kafka;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.MDCUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/12/1
 */
@Aspect
@Component
public class KafkaListenerAspect {

    private static final Logger log = LoggerFactory.getLogger(KafkaListenerAspect.class);

    @Autowired
    private ActInfoService actInfoService;

    @Around(value = "execution(* com.yy.ent.mobile.kafka.client.service.KafkaTopicListener.callback(..))")
    public Object kafkaListenerAdvice(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        ConsumerRecord<String, ByteBuffer> record = (ConsumerRecord<String, ByteBuffer>) proceedingJoinPoint.getArgs()[0];
        String origin = "kafka=" + record.topic();

        //没活动运行
        if (actInfoService.noActActive()) {
            log.info("act not run,return,topic:{}", record.topic());
            return true;
        }

        SysEvHelper.checkHistory(origin, true);
        MDCUtils.putContext(origin);
        try {
            return proceedingJoinPoint.proceed();
        } finally {
            MDCUtils.clearContext();
        }
    }

    @Around(value = "@annotation(org.springframework.kafka.annotation.KafkaListener)")
    public Object doAroundAdvice(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        ConsumerRecord<String, String> consumerRecord = (ConsumerRecord<String, String>) proceedingJoinPoint.getArgs()[0];

        //没活动运行
        if (actInfoService.noActActive()) {
            log.info("act not run,return,topic:{}", consumerRecord.topic());
            return true;
        }
        SysEvHelper.checkHistory(consumerRecord.topic(), true);

        //kafka消息消费滞后5分钟告警
        //因为活动停了要停止部署，所以关了服务治理kafka消息 lag告警，为避免消息堆积无法被发小，特加上消费时间滞后告警
        long messageCreateTime = consumerRecord.timestamp();
        long maxOffset = Const.GEPM.getParamValueToLong(GeParamName.KAFKA_MAX_TIME_OFFSET, 300000);
        if (System.currentTimeMillis() > (messageCreateTime + maxOffset)) {
            String msgTime = DateUtil.format(new Date(messageCreateTime));
            log.error("kafka consumer lag more than:{} mill,topic:{},message time:{}", maxOffset, consumerRecord.topic(), msgTime);
        }

        return proceedingJoinPoint.proceed();
    }
}
