package com.yy.gameecology.activity.service.actresult;

import com.yy.gameecology.activity.service.actresult.builderimpl.*;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.lang.NotImplementedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-11-17 16:23
 **/
@Service
public class ActResultFactory {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    @Autowired
    @Qualifier("cpActResultBuilder")
    private CpActResultBuilder cpActResultBuilder;

    @Autowired
    @Qualifier("familyActResultBuilder")
    private FamilyActResultBuilder familyActResultBuilder;

    @Autowired
    @Qualifier("channelActResultBuilder")
    private ChannelActResultBuilder channelActResultBuilder;

    @Autowired
    @Qualifier("pwChannelActResultBuilder")
    private PwChannelActResultBuilder pwChannelActResultBuilder;

    @Autowired
    @Qualifier("roomActResultBuilder")
    private RoomActResultBuilder roomActResultBuilder;

    @Autowired
    @Qualifier("subChannelActResultBuilder")
    private SubChannelActResultBuilder subChannelActResultBuilder;

    @Autowired
    @Qualifier("userActResultBuilder")
    private UserActResultBuilder userActResultBuilder;

    @Autowired
    @Qualifier("pwTuanActResultBuilder")
    private PwTuanActResultBuilder pwTuanActResultBuilder;

    @Autowired
    @Qualifier("ftsHallActResultBuilder")
    private FtsHallActResultBuilder ftsHallActResultBuilder;

    public ActResultMemberLoader getActResultMemberBuilder(long actId, long type, long roleType) {
        //陪玩公会
        if (type == 5 && roleType == RoleType.GUILD.getValue()) {
            return pwChannelActResultBuilder;
        }
        //cp
        else if (roleType == 100200) {
            return cpActResultBuilder;

        } else if (roleType == RoleType.GUILD.getValue()) {
            return channelActResultBuilder;
        }
        //厅
        else if (roleType == RoleType.HALL.getValue()) {
            // 技能卡的厅：兼容老逻辑，正常应该配置成RoleType.ROOM
            if (type == 6) {
                return roomActResultBuilder;
            } else if (type == 1) {
                return ftsHallActResultBuilder;
            }
            //常规厅
            else {
                return subChannelActResultBuilder;
            }
        }
        //技能卡的厅
        else if (roleType == RoleType.ROOM.getValue()) {
            return roomActResultBuilder;
        }
        //主播、用户
        else if (roleType == RoleType.ANCHOR.getValue()
                || roleType == RoleType.USER.getValue()
                || roleType == RoleType.WAITER.getValue()
                || roleType == RoleType.TING_MGR.getValue()) {
            return userActResultBuilder;

        }
        //家族
        else if (roleType == RoleType.FAMILY.getValue()) {
            return familyActResultBuilder;
        } else if (roleType == RoleType.PWTUAN.getValue()) {
            return pwTuanActResultBuilder;
        }

        throw new NotImplementedException(String.format("未实现Builder,actId:%s,type:%s,roleType:%s", actId, type, roleType));
    }

}


