/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.yy.gameecology.activity.exception.handler;


import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.exception.BadRequestException;
import com.yy.gameecology.activity.exception.ParameterException;
import com.yy.gameecology.activity.worker.web.BaseController;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.RequestUtil;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.io.EofException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MultipartException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.UnexpectedTypeException;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;



/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/12/31 17:15
 * @Modified:
 */
@RestControllerAdvice
public class GlobalExceptionHandler extends BaseController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    private final HttpServletRequest request;
    private final HttpServletResponse response;

    public GlobalExceptionHandler(HttpServletRequest request, HttpServletResponse response) {
        this.request = request;
        this.response = response;
    }

    /**
     * 处理所有不可知的异常
     */
    @ExceptionHandler(Throwable.class)
    public Response handleException(Throwable e) {
        // 打印堆栈信息
        final String brokenPipe = "Broken pipe";
        if (e instanceof IOException && StringUtils.contains(e.getMessage(), brokenPipe)) {
            log.warn("{} error:{}", getErrorLogMsg(e), e.getMessage(), e);
            return new Response(Code.E_SYS_BUSY);
        } else if (isIgnoreError(e.getMessage())) {
            log.warn("{} error:{}", getErrorLogMsg(e), e.getMessage(), e);
            return new Response(Code.E_SYS_BUSY);
        }
        //恶意扫描
        if (e instanceof MultipartException) {
            log.warn("multipartException {} error:{}", getErrorLogMsg(e), e.getMessage(), e);
            return new Response(Code.E_SYS_BUSY);
        }
        log.error("{} error:{}", getErrorLogMsg(e), e.getMessage(), e);
        return new Response(Code.E_SYS_BUSY);
    }

    /**
     * 配置错误降级错误信息关键词
     */
    private boolean isIgnoreError(String errorMessage) {
        String keyWords = Const.GEPM.getParamValue("ignore_error_message", "Connection reset by peer");
        String[] array = keyWords.split(",");
        for (String keyWord : array) {
            if (keyWord.equals(errorMessage)) {
                log.warn("is ignore error,keyword:{}", keyWord);
                return true;
            }
        }

        return false;
    }



    /**
     * 处理自定义异常
     */
	@ExceptionHandler(value = BadRequestException.class)
	public Response badRequestException(BadRequestException e) {
        // 打印堆栈信息
        log.error("{} error:{}", getErrorLogMsg(e), e.getMessage(), e);
        return buildResponseEntity(new SuperException(e.getMessage(),e.getStatus()));
	}

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(value = SuperException.class)
    public Response superException(SuperException e) {
        // 打印堆栈信息
        log.error("{} error:{}", getErrorLogMsg(e), e.getMessage(), e);
        return buildResponseEntity(e);
    }


    /**
     * 统一处理参数校验异常
     * <pre>
     * 对象参数接收请求体校验不通过会抛出 MethodArgumentNotValidException
     * 普通参数校验校验不通过会抛出 ConstraintViolationException
     * 必填参数没传校验不通过会抛出 ServletRequestBindingException
     * 请求参数绑定到对象上校验不通过会抛出 BindException
     * </pre>
     */
    @ExceptionHandler({ConstraintViolationException.class,
            MethodArgumentNotValidException.class,
            ServletRequestBindingException.class,
            BindException.class,
            MissingServletRequestParameterException.class,
            MissingPathVariableException.class,
            UnexpectedTypeException.class,
            ParameterException.class})
    public Response handleValidationException(Exception e) {
        String logMsg = getErrorLogMsg(e);
        String msg = "";
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException t = (MethodArgumentNotValidException) e;
            msg = getBindingResultMsg(t.getBindingResult());
        } else if (e instanceof BindException) {
            BindException t = (BindException) e;
            msg = getBindingResultMsg(t.getBindingResult());
        } else if (e instanceof ConstraintViolationException) {
            ConstraintViolationException t = (ConstraintViolationException) e;
            msg = t.getConstraintViolations().stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(","));
        } else if (e instanceof MissingServletRequestParameterException) {
            MissingServletRequestParameterException t = (MissingServletRequestParameterException) e;
            msg = t.getParameterName() + " 不能为空";
        } else if (e instanceof MissingPathVariableException) {
            MissingPathVariableException t = (MissingPathVariableException) e;
            msg = t.getVariableName() + " 不能为空";
        } else if (e instanceof UnexpectedTypeException) {
            msg = "参数错误";
        } else if (e instanceof ParameterException) {
            msg = e.getMessage();
        } else {
            // 其他类型的错误当成未知异常处理
            return handleException(e);
        }
        log.warn("参数校验不通过, {}, msg: {}", logMsg, msg, e);
        return buildResponseEntity(new SuperException(msg, Code.E_DATA_ERROR.getCode()));
    }

    /**
     * 请求链接断开，状态码499，日志降级
     *
     * @param e
     * @return
     */
    @ExceptionHandler(EofException.class)
    @ResponseBody
    public Response ConstraintViolationExceptionHandler(EofException e) {
        String logMsg = getErrorLogMsg(e);
        log.warn("请求链接已经断开, {}", logMsg, e);
        return new Response(Code.E_SYS_BUSY);
    }

    /**
     * 统一返回
     */
    private  Response buildResponseEntity(SuperException SuperException) {
        return new Response(SuperException);
    }

    /**
     * 获取错误的调用函数信息
     * @param e
     * @return
     */
    private String getCallingName(Throwable e) {
        if (e.getStackTrace().length > 0) {
            String methodName = e.getStackTrace()[0].getMethodName();
            String className = e.getStackTrace()[0].getClassName();
            String SimpleClassName = className.substring(className.lastIndexOf(".") + 1);
            return SimpleClassName + " " + methodName;
        }
        return "";
    }
    /**
     * 获取堆栈信息
     */
    public String getStackTrace(Throwable throwable){
        StringWriter sw = new StringWriter();
        try (PrintWriter pw = new PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }

    private String getBindingResultMsg(BindingResult result) {
        return result.getFieldErrors().stream()
                .map(info -> info.getField() + " " + info.getDefaultMessage())
                .collect(Collectors.joining());
    }
    /**
     * 异常信息应包含 url + queryString(若有) + 请求参数(这里只能拿到表单提交的参数) + username(若有)
     */
    private String getErrorLogMsg(Throwable t) {
        StringBuilder errorLogMsg = new StringBuilder();
        // url，包括查询 queryString
        errorLogMsg.append("url: ").append(request.getRequestURL().toString());
        if (StringUtils.isNotBlank(request.getQueryString())) {
            errorLogMsg.append("?").append(request.getQueryString());
        }
        // 获取参数，这里只能拿到查询参数和以表单形式提交的参数，requestBody 的拿不到
        Map<String, String[]> params = request.getParameterMap();
        if (params != null && !params.isEmpty()) {
            StringBuilder builder = new StringBuilder();
            params.forEach((k, v) -> {
                builder.append(",").append(k).append("=").append(Arrays.toString(v));
            });
            errorLogMsg.append(", params:").append(builder.substring(1));
        }
        String ip = RequestUtil.getRealIp(request);
        errorLogMsg.insert(0, "ip: " + ip + ", ");

        // 如果能获取到当前登录人信息，则添加到最前面
        Long uid  = getLoginYYUid(request,response);
        if (uid>0) {
            errorLogMsg.insert(0, "loginUid: " + uid + ", ");
        }


        return errorLogMsg.toString();
    }

}
