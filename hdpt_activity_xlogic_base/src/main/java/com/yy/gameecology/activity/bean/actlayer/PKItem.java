package com.yy.gameecology.activity.bean.actlayer;


/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-28 21:17
 **/
public class PKItem {

    private String nickName;
    private String avatar;
    //签约公会短号
    private String signAsId;
    private long pkValue;
    private int rank;
    private String memberId;

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public long getPkValue() {
        return pkValue;
    }

    public void setPkValue(long pkValue) {
        this.pkValue = pkValue;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getSignAsId() {
        return signAsId;
    }

    public void setSignAsId(String signAsId) {
        this.signAsId = signAsId;
    }
}
