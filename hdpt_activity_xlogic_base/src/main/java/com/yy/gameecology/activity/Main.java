
package com.yy.gameecology.activity;

import com.yy.aomi.sdk.AomiSdk;
import com.yy.boot.component.kafka.MultiKafkaAutoConfiguration;
import com.yy.gameecology.common.notify.StopServerNotifyService;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.handler.annotation.EnableGeHandler;
import com.yy.service.SvcSDK;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.cache.CacheMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAsync(proxyTargetClass = true)
@EnableGeHandler
@EnableCaching
@EnableKafka
@EnableDubbo
@SpringBootApplication(exclude = {RedisAutoConfiguration.class, KafkaAutoConfiguration.class, CacheMetricsAutoConfiguration.class, MongoAutoConfiguration.class, MultiKafkaAutoConfiguration.class})
@MapperScan(basePackages = "com.yy.gameecology.common.db.mapper")
public class Main implements CommandLineRunner, WebServerFactoryCustomizer<ConfigurableServletWebServerFactory> {

    private static final Logger log = LoggerFactory.getLogger(Main.class);

    public static void main(String[] args) throws Exception {
        SysEvHelper.checkGroup();
        String desc = SysEvHelper.getEnvDesc();
        String appcfg = SysEvHelper.getAppConfig();
        String grpCfg = SysEvHelper.getAppGroupConfig();
        String aomiCfg = SysEvHelper.getAomiConfig();

        // 必须在IOC框架以及相关被监控框架初始化前完成对AomiSdk的初始化
        // 所以请将初始化代码放在main函数入口最开头
        // 测试环境忽略 aomi，加速启动
        if (SysEvHelper.isDeploy()) {
            AomiSdk.initAgent(aomiCfg);
            // 准备全局固定的 context
            Aomi.readyAomiContext();
        }

        StopServerNotifyService.NAME = "活动系统(" + Main.class.getName() + ")";

        SpringApplication app = new SpringApplication(Main.class) {

        };
        app.setDefaultProperties(SysEvHelper.properties);

        log.info("########################################################################################");
        log.info("============>> 系统启动，使用配置： 【" + desc + "】 -> " + appcfg + ", " + grpCfg);
        log.info("########################################################################################");
        app.run(args);

        // 等待防退出
        SysEvHelper.waiting();
    }


    @Override
    public void run(String... args) throws Exception {
        //testThriftClients();
        //testCaches();
        // 添加hook thread，重写其run方法
        Runtime.getRuntime().addShutdownHook(new Thread() {
            @Override
            public void run() {
                log.warn("svc sdk shutdown begin");
                if (SysEvHelper.isLocal() || SysEvHelper.isYuFa() || SysEvHelper.skipSvcsdkInit()) {
                    return;
                }

                //调用后，将自身从s2s摘掉，以免service平台继续发消息过来
                log.warn("svc sdk shutdown invoke ...");
                SvcSDK.getInstance().sdkDestory();
                log.warn("svc sdk shutdown done !!!");
            }
        });
    }

    /*
     * 定制web端口, 用于测试，用完后要关闭
     */
    @Override
    public void customize(ConfigurableServletWebServerFactory factory) {
        int port = Convert.toInt(SysEvHelper.properties.get("web.server.port"), 7036);
        factory.setPort(port);
        log.info("customize ok@web server port:{}", port);
    }
}
