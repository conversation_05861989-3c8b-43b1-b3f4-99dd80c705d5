package com.yy.gameecology.activity.service.aov.match;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.client.WebdbUinfoClient;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.common.db.mapper.aov.AovMatchNodeExtMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseRoundExtMapper;
import com.yy.gameecology.common.db.model.gameecology.aov.AovMatchNode;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhase;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseRound;
import com.yy.gameecology.hdzj.bean.aov.AovMatchInfo;
import com.yy.gameecology.hdzj.bean.aov.AovMatchNodeInfo;
import com.yy.gameecology.hdzj.bean.aov.AovNodePairInfo;
import com.yy.gameecology.hdzj.bean.aov.AovRoundInfo;
import com.yy.gameecology.hdzj.element.component.attr.AovMatchComponentAttr;
import com.yy.java.webdb.WebdbUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AovMatchService {

    @Resource
    private AovPhaseMapper aovPhaseMapper;

    @Resource
    private AovMatchNodeExtMapper aovMatchNodeExtMapper;

    @Resource
    private AovPhaseRoundExtMapper aovPhaseRoundExtMapper;

    @Autowired
    private WebdbUinfoClient webdbUinfoClient;

    @Autowired
    private CommonService commonService;


    public Response<List<AovMatchNodeInfo>> queryMatchNodes(AovMatchComponentAttr attr, long phaseId, long teamId, Date now) {
        AovPhase phase = aovPhaseMapper.selectByPhaseId(phaseId);
        if (phase == null) {
            return Response.fail(400, "param error");
        }

        if (now.before(phase.getAdjustEndTime())) {
            return Response.fail(461, "敬请期待");
        }

        List<AovMatchNode> nodes = aovMatchNodeExtMapper.batchSelectByNodes(phaseId, null);
        if (CollectionUtils.isEmpty(nodes)) {
            return Response.success(Collections.emptyList());
        }

        Set<Long> uids = nodes.stream().map(AovMatchNode::getUid).collect(Collectors.toSet());

        Map<Long, WebdbUserInfo> userInfoMap = webdbUinfoClient.batchGetUserInfo(List.copyOf(uids));

        List<AovMatchNodeInfo> result = new ArrayList<>(nodes.size());
        for (AovMatchNode node : nodes) {
            AovMatchNodeInfo info = getAovMatchNodeInfo(teamId, node, userInfoMap);

            result.add(info);
        }

        //无参赛时，把最新轮次中的中间节点设置成选中状态
        if (CollectionUtils.isNotEmpty(result) && result.stream().noneMatch(AovMatchNodeInfo::isCurrent)) {
            long maxRoundId = result.stream().filter(p -> p.getUid() > 0).mapToLong(AovMatchNodeInfo::getRoundId).max().orElse(0);
            List<AovMatchNodeInfo> latestNodes = result.stream()
                    .filter(p -> p.getRoundId() == maxRoundId)
                    .sorted(Comparator.comparing(AovMatchNodeInfo::getNodeIndex))
                    .toList();
            AovMatchNodeInfo middleNodes = latestNodes.get(latestNodes.size() / 2);

            Optional<AovMatchNodeInfo> current = result.stream().filter(p -> p.getNodeId() == middleNodes.getNodeId()).findFirst();
            current.ifPresent(aovMatchNodeInfo -> aovMatchNodeInfo.setCurrent(true));
        }

        return Response.success(result);
    }

    @NotNull
    private static AovMatchNodeInfo getAovMatchNodeInfo(long teamId, AovMatchNode node, Map<Long, WebdbUserInfo> userInfoMap) {
        AovMatchNodeInfo info = new AovMatchNodeInfo();
        info.setNodeId(node.getId());
        info.setNodeIndex(node.getNodeIndex());
        info.setRoundId(node.getRoundId());
        info.setTeamId(node.getTeamId());
        long uid = node.getUid();
        info.setUid(uid);
        info.setState(node.getState());
        info.setScore(node.getScore());

        if (uid > 0 && userInfoMap.containsKey(uid)) {
            WebdbUserInfo userInfo = userInfoMap.get(uid);
            info.setNick(userInfo.getNick());
            info.setAvatar(userInfo.getAvatar());
        }

        if (teamId == node.getTeamId()) {
            if (node.getState() == AovConst.MatchNodeState.GAME_CREATABLE ||
                    node.getState() == AovConst.MatchNodeState.GAME_CREATED ||
                    node.getState() == AovConst.MatchNodeState.RESULTED) {
                info.setCurrent(true);
            }
        }
        return info;
    }

    public Response<List<AovRoundInfo>> queryRoundInfos(AovMatchComponentAttr attr, long phaseId, long teamId, Date now) {
        AovPhase phase = aovPhaseMapper.selectByPhaseId(phaseId);
        if (phase == null) {
            return Response.fail(400, "param error");
        }

        if (phase.getState() != AovConst.PhaseState.INITIALIZED) {
            return Response.fail(462, "phase not initialized");
        }

        if (now.before(phase.getAdjustEndTime())) {
            return Response.fail(461, "敬请期待");
        }

        List<AovMatchNode> nodes = aovMatchNodeExtMapper.batchSelectByNodes(phaseId, null);
        if (CollectionUtils.isEmpty(nodes)) {
            return Response.success(Collections.emptyList());
        }

        Map<Integer, AovMatchNode> nodeMap = new HashMap<>(nodes.size());
        Set<Long> uids = new HashSet<>(nodes.size());
        for (AovMatchNode node : nodes) {
            nodeMap.put(node.getNodeIndex(), node);
            if (node.getUid() > 0) {
                uids.add(node.getUid());
            }
        }

        Map<Long, WebdbUserInfo> userInfoMap = webdbUinfoClient.batchGetUserInfo(List.copyOf(uids));
        List<AovPhaseRound> rounds = aovPhaseRoundExtMapper.selectRounds(phaseId);
        Map<Integer, AovRoundInfo> roundInfoMap = new LinkedHashMap<>(rounds.size());
        for (AovPhaseRound round : rounds) {
            int roundNum = round.getRoundNum();
            AovRoundInfo roundInfo = roundInfoMap.get(roundNum);
            if (roundInfo != null) {
                if (round.getStartTime().before(roundInfo.getStartTime())) {
                    roundInfo.setStartTime(round.getStartTime());
                }

                if (round.getEndTime().after(roundInfo.getEndTime())) {
                    roundInfo.setEndTime(round.getEndTime());
                }

                continue;
            }

            int startNodeIndex = 1 << (roundNum - 1);
            // 判断是否为有效的round
            AovMatchNode startNode = nodeMap.get(startNodeIndex);
            if (startNode == null) {
                continue;
            }

            roundInfo = new AovRoundInfo();
            roundInfo.setRoundNum(roundNum);
            roundInfo.setRoundName(round.getRoundName());
            roundInfo.setBo(round.getBo());
            roundInfo.setStartTime(round.getStartTime());
            roundInfo.setEndTime(round.getEndTime());
            roundInfo.setState(round.getState());

            final int roundNodeSize = startNodeIndex;
            int actualNodeSize = roundNodeSize;
            List<AovNodePairInfo> matches = new ArrayList<>(roundNodeSize / 2);
            for (int i = 0; i < roundNodeSize; i+=2) {
                int nodeIndex = startNodeIndex + i;
                AovMatchNode node1 = nodeMap.get(nodeIndex);
                AovMatchNode node2 = nodeMap.get(nodeIndex + 1);
                Assert.notNull(node1, "node1 cannot be null");
                Assert.notNull(node2, "node2 cannot be null");

                if (node2.getUid() < 0) {
                    actualNodeSize -= 1;
                }

                AovMatchNodeInfo nodeInfo1 = getAovMatchNodeInfo(teamId, node1, userInfoMap);
                AovMatchNodeInfo nodeInfo2 = getAovMatchNodeInfo(teamId, node2, userInfoMap);

                int nodeScore1 = node1.getScore(), nodeScore2 = node2.getScore();
                int advanceRequireScore = round.getBo() / 2 + 1;
                setNodeWin(node1, nodeInfo1, nodeScore1, advanceRequireScore);
                setNodeWin(node2, nodeInfo2, nodeScore2, advanceRequireScore);

                AovNodePairInfo match = new AovNodePairInfo();
                match.setAdvanceNodeIndex(nodeIndex / 2);
                match.setNode1(nodeInfo1);
                match.setNode2(nodeInfo2);
                int bye = node1.getTeamId() < 0 || node2.getTeamId() < 0 ? 1 : 0;
                match.setBye(bye);
                int matchState = node1.getState() == AovConst.MatchNodeState.ADVANCED || node1.getState() == AovConst.MatchNodeState.ELIMINATED ? AovConst.MatchState.SETTLED : node1.getState();
                match.setState(matchState);
                matches.add(match);
            }

            // 有轮空的，替换默认的roundName
            if (actualNodeSize < roundNodeSize) {
                String roundName = actualNodeSize + "进" + (roundNodeSize / 2);
                roundInfo.setRoundName(roundName);
            }

            // 将非轮空的对局排在轮空对局前面
            matches.sort(Comparator.comparingInt(AovNodePairInfo::getBye));
            roundInfo.setMatches(matches);

            roundInfoMap.put(roundNum, roundInfo);
        }

        return Response.success(roundInfoMap.values().stream().toList());
    }

    private void setNodeWin(AovMatchNode node, AovMatchNodeInfo nodeInfo, int score, int requireScore) {
        // 只处理费轮空节点
        if (node.getTeamId() > 0) {
            // 被淘汰的节点必然是失败的
            if (node.getState() == AovConst.MatchNodeState.ELIMINATED) {
                nodeInfo.setWin(-1);
            } else if (node.getState() == AovConst.MatchNodeState.ADVANCED) {
                // 需要达到bo完成所需的分数才算赢
                nodeInfo.setWin(score >= requireScore ? 1 : -1);
            }
        }
    }

    public List<AovMatchInfo> queryAovMatchList(AovMatchComponentAttr attr, long actId, long phaseId) {
        List<AovMatchInfo> aovMatchInfos = Lists.newArrayList();

        //正在进行中的比赛
        List<AovMatchNode> playingList = aovMatchNodeExtMapper.selectMatchListNodes(phaseId, Lists.newArrayList(AovConst.MatchNodeState.GAME_CREATED), null);
        //已完成，未流局的对局数据
        List<AovMatchNode> closeList = aovMatchNodeExtMapper.selectMatchListNodes(phaseId, null, AovConst.GameTeamState.COMPLETED);

        List<AovMatchNode> needShowNodes = Lists.newArrayList(playingList);
        needShowNodes.addAll(closeList);

        Date now = commonService.getNow(actId);
        List<AovPhaseRound> allRound = aovPhaseRoundExtMapper.selectRounds(phaseId);
        Map<Long,AovPhaseRound> beginRoundMap = allRound
                .stream()
                .filter(p -> p.getStartTime().before(now))
                .collect(Collectors.toMap(AovPhaseRound::getId,p->p));

        needShowNodes = needShowNodes
                .stream()
                .filter(p -> beginRoundMap.containsKey(p.getRoundId()))
                .collect(Collectors.toList());

        Map<Integer, AovMatchNode> nodeMap = needShowNodes.stream()
                .collect(Collectors.toMap(AovMatchNode::getNodeIndex, p -> p, (existing, replacement) -> existing));
        List<Long> allUids = needShowNodes.stream().map(AovMatchNode::getUid).collect(Collectors.toList());
        Map<Long, UserBaseInfo> allUserInfo = commonService.batchGetUserInfos(allUids, false);
        int maxNode = needShowNodes.stream().mapToInt(AovMatchNode::getNodeIndex).max().orElse(0);

        for (int i = 2; i <= maxNode; i = i + 2) {
            AovMatchNode node1 = nodeMap.get(i);
            AovMatchNode node2 = nodeMap.get(i + 1);
            if (node1 == null || node2 == null) {
                continue;
            }
            AovPhaseRound round = beginRoundMap.get(node1.getRoundId());
            AovMatchInfo aovMatchInfo = new AovMatchInfo();
            aovMatchInfo.setRoundName(round.getRoundName());
            aovMatchInfo.setStartTime(round.getStartTime().getTime());
            aovMatchInfo.setScore1(node1.getScore());
            aovMatchInfo.setScore2(node2.getScore());
            //目前只显示进行中以及已完成
            int matchState = AovConst.GameTeamState.COMPLETED.contains(node1.getTeamState())
                    && AovConst.MatchNodeState.GAME_CREATED != node1.getState()
                    ? AovConst.MatchListState.COMPLETE : AovConst.MatchListState.PLAYING;
            aovMatchInfo.setMatchState(matchState);

            UserBaseInfo userBaseInfo1 = allUserInfo.get(node1.getUid());
            if (userBaseInfo1 != null) {
                aovMatchInfo.setNick1(userBaseInfo1.getNick());
                aovMatchInfo.setAvatar1(userBaseInfo1.getHdLogo());
            }

            UserBaseInfo userBaseInfo2 = allUserInfo.get(node2.getUid());
            if (userBaseInfo2 != null) {
                aovMatchInfo.setNick2(userBaseInfo2.getNick());
                aovMatchInfo.setAvatar2(userBaseInfo2.getHdLogo());
            }


            aovMatchInfos.add(aovMatchInfo);
        }

        return aovMatchInfos;
    }
}
