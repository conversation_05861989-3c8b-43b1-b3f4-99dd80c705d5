package com.yy.gameecology.activity.bean.mq;

import lombok.Data;

import java.util.List;

@Data
public class ZhuiwanGiftComboKafkaEvent {

    private String sequenceId;

    /**
     * 34 追玩语音房
     */
    private int appid;

    /**
     * 送礼渠道，133 ios语音房，134 安卓语音房
     */
    private int usedChannel;

    /**
     * 顶级频道号
     */
    private long sid;

    /**
     * 子频道号
     */
    private long ssid;

    /**
     * 送礼uid
     */
    private long uid;

    private long targetUid;

    private long anchorUid;

    /**
     * 礼物id
     */
    private long propId;

    private int count;

    /**
     * 连送次数
     */
    private int comboHits;

    private long virtPrice;

    private long virtPriceSum;

    /**
     * 扩展字段
     */
    private String expand;

    private ZhuiwanGiftComboEventMeta meta;

    private List<ZhuiwanComboEventChannels> channels;
}
