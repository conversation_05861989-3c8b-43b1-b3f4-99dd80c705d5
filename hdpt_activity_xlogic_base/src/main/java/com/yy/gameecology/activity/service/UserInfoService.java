package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.NickExt;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.utils.MyListUtils;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.thrift.fts_base_info_bridge.UserInfo;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-30 15:32
 **/
@Service
public class UserInfoService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private CommonService commonService;

    public Map<Long, UserInfoVo> getUserInfo(List<Long> uids, Template templateType) {
        if (uids == null) {
            return Collections.emptyMap();
        }
        uids = uids.stream().filter(Objects::nonNull).toList();
        if (CollectionUtils.isEmpty(uids)) {
            return Collections.emptyMap();
        }

        templateType = templateType==null ? Template.unknown : templateType;

        Map<Long, UserInfoVo> userInfoVoMap = Maps.newHashMap();
        switch (templateType) {
            case makefriend:
            case all:
                Map<Long, UserInfo> ftsUserInfoMap = ftsBaseInfoBridgeClient.getFtsUserInfoMap(uids);
                if (MapUtils.isEmpty(ftsUserInfoMap)) {
                    return userInfoVoMap;
                }
                for (Long uid : ftsUserInfoMap.keySet()) {
                    UserInfo userInfo = ftsUserInfoMap.get(uid);
                    UserInfoVo vo = new UserInfoVo();
                    vo.setAvatarUrl(userInfo.getAvatar_url());
                    vo.setEcologyNobleId(userInfo.getEcology_noble_id() + "");
                    vo.setNick(userInfo.getNick());
                    vo.setNobleId(userInfo.getNoble_id() + "");
                    vo.setUid(userInfo.getUid());
                    vo.setYyno(userInfo.getImid() + "");
                    userInfoVoMap.put(uid, vo);
                }
                break;

            default:
                Map<Long, WebdbUserInfo> commonUserInfoMap = commonService.batchYyUserInfo(uids);
                if (MapUtils.isEmpty(commonUserInfoMap)) {
                    return userInfoVoMap;
                }
                for (Long uid : commonUserInfoMap.keySet()) {
                    WebdbUserInfo userInfo = commonUserInfoMap.get(uid);
                    UserInfoVo vo = new UserInfoVo();
                    vo.setUid(uid);
                    vo.setEcologyNobleId("");
                    vo.setNobleId("");
                    if (userInfo != null) {
                        vo.setAvatarUrl(WebdbUtils.getLogo(userInfo));
                        vo.setNick(userInfo.getNick());
                        vo.setYyno(userInfo.getYyno());
                    }
                    userInfoVoMap.put(uid, vo);
                }
                break;
        }

        return userInfoVoMap;
    }

    public static Map<Long, UserInfoVo> getUserInfo(Map<String, WebdbUserInfo> commonUserInfoMap) {
        return getUserInfo(commonUserInfoMap,false);
    }

    public static Map<Long, UserInfoVo> getUserInfo(Map<String, WebdbUserInfo> commonUserInfoMap, boolean nickBase64) {
        if (MapUtils.isEmpty(commonUserInfoMap)) {
            return Collections.emptyMap();
        }

        Map<Long, UserInfoVo> result = new HashMap<>(commonUserInfoMap.size());
        for (String uidStr : commonUserInfoMap.keySet()) {
            long uid = Long.parseLong(uidStr);
            WebdbUserInfo userInfo = commonUserInfoMap.get(uidStr);
            UserInfoVo vo = new UserInfoVo();
            vo.setUid(uid);
            vo.setEcologyNobleId("");
            vo.setNobleId("");
            if (userInfo != null) {
                vo.setAvatarUrl(WebdbUtils.getLogo(userInfo));
                vo.setNick(userInfo.getNick());
                if (nickBase64 && StringUtil.isNotBlank(vo.getNick())) {
                    vo.setNick(Base64.encodeBase64String(vo.getNick().getBytes()));
                }
                vo.setYyno(userInfo.getYyno());
            }
            result.put(uid, vo);
        }

        return result;
    }

    public Map<String, Map<String, MultiNickItem>> batchGetMultiNickUsers(List<Long> uids) {
        Map<String, Map<String, MultiNickItem>> multiNickUsers = null;
        // 设置多昵称条件
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(uids);
        if (batched == null || MapUtils.isEmpty(batched.getUserInfoMap())) {
            log.error("trying to get user info with nick ext fail");
        } else {
            Map<Long, UserInfoVo> userInfoMap = UserInfoService.getUserInfo(batched.getUserInfoMap());
            if (StringUtils.startsWith(batched.getNickExt(), StringUtil.OPEN_BRACE)) {
                NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
                multiNickUsers = nickExt.getUsers();
            }
        }
        return multiNickUsers;
    }

    public Map<Long, UserInfoVo> getCpUserInfoWithNickExt(long userUid, long anchorUid, Map<String, Map<String, MultiNickItem>> multiNickUsers) {
        List<Long> uids = new ArrayList<>(2);
        uids.add(userUid);
        if (userUid != anchorUid) {
            uids.add(anchorUid);
        }

        return getUserInfoWithNickExt(uids, multiNickUsers, true, Template.unknown.getCode());
    }

    public Map<Long, UserInfoVo> getUserInfoWithNickExt(List<Long> uids, Map<String, Map<String, MultiNickItem>> multiNickUsers) {
        return getUserInfoWithNickExt(uids, multiNickUsers, false, Template.unknown.getCode());
    }

    public Map<Long, UserInfoVo> getUserInfoWithNickExt(List<Long> uids, Map<String, Map<String, MultiNickItem>> multiNickUsers, int templateType) {
        return getUserInfoWithNickExt(uids, multiNickUsers, false, templateType);
    }

    public Map<Long, UserInfoVo> getUserInfoWithNickExt(List<Long> uids, Map<String, Map<String, MultiNickItem>> multiNickUsers, boolean nickBase64, int templateType) {
        if (CollectionUtils.isEmpty(uids)) {
            return Maps.newHashMap();
        }

        Map<Long, UserInfoVo> userInfoVoMap = null;

        final int maxBatchSize = 499;
        List<List<Long>> batchUids = MyListUtils.subList(uids, maxBatchSize);
        for (List<Long> uidList : batchUids) {
            BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(uidList);
            if (batched == null || MapUtils.isEmpty(batched.getUserInfoMap())) {
                log.warn("trying to get user info with nick ext fail,uids size:{}", uids.size());
            } else {
                Map<Long, UserInfoVo> userBatchVoMap = UserInfoService.getUserInfo(batched.getUserInfoMap(), nickBase64);
                if (MapUtils.isNotEmpty(userBatchVoMap)) {
                    if (userInfoVoMap == null) {
                        userInfoVoMap = Maps.newHashMap();
                    }
                    userInfoVoMap.putAll(userBatchVoMap);
                }
                if (StringUtils.startsWith(batched.getNickExt(), StringUtil.OPEN_BRACE)) {
                    NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
                    if (MapUtils.isNotEmpty(nickExt.getUsers())) {
                        multiNickUsers.putAll(nickExt.getUsers());
                    }
                }
            }
        }

        //base64
        if (MapUtils.isNotEmpty(multiNickUsers) && nickBase64) {
            for (String uid : multiNickUsers.keySet()) {
                Map<String, MultiNickItem> multiNickItemMap = multiNickUsers.get(uid);
                for (String host : multiNickItemMap.keySet()) {
                    MultiNickItem multiNickItem = multiNickItemMap.get(host);
                    if (StringUtil.isNotBlank(multiNickItem.getNick())) {
                        multiNickItem.setNick(Base64.encodeBase64String(multiNickItem.getNick().getBytes()));
                    }
                }
            }
        }


        if (userInfoVoMap == null) {
            userInfoVoMap = getUserInfo(uids, Template.getTemplate(templateType));
        }
        return userInfoVoMap;

    }
}
