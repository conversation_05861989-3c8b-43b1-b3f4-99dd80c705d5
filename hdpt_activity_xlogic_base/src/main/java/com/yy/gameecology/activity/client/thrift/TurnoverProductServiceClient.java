package com.yy.gameecology.activity.client.thrift;


import com.yy.thrift.turnover.*;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-15 15:33
 **/
@Component
public class TurnoverProductServiceClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Reference(protocol = "nythrift_compact", owner = "${turnoverService_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"})
    public TProductService.Iface proxy = null;

    public TProductService.Iface getProxy() {
        return proxy;
    }

    public TConsumeProductResult consumeProductNew(long uid, int productId, int productType, long amount, int appid, String seqId, String description, String expand, String userIp) {
        TConsumeProductRequest request = new TConsumeProductRequest();
        request.setUid(uid);
        request.setProductId(productId);
        request.setProductType(productType);
        request.setAmount(amount);
        request.setAppid(TAppId.findByValue(appid));
        request.setSeqId(seqId);
        request.setDescription(description);
        request.setExpand(expand);
        request.setUserIp(userIp);
        try {
            var result = getProxy().consumeProductNew(request);
            log.info("consumeProductNew,req:{},result:{}", request, result);
            return result;
        } catch (Exception e) {
            log.error("consumeProductNew,request:{},e:{}", request, e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    public TConsumeProductResult reverseConsumeProduct(TReverseConsumeProductRequest request) {
        try {
            log.info("reverseConsumeProduct begin req:{}", request);
            var result = getProxy().reverseConsumeProduct(request);
            log.info("reverseConsumeProduct done req:{},result:{}", request, result);
            return result;
        } catch (Exception e) {
            log.error("consumeProductNew,request:{},e:{}", request, e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
