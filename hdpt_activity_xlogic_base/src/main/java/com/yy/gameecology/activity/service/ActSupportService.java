package com.yy.gameecology.activity.service;


import com.yy.gameecology.activity.rolebuilder.RoleBuilder;
import com.yy.gameecology.common.consts.RankDataSource;
import com.yy.gameecology.common.support.SpringBeanAwareFactory;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import com.yy.thrift.hdztranking.RoleType;

import java.util.List;
import java.util.Map;

public interface ActSupportService {

    Map<String, Map<String, MemberItemInfo>> queryMember(long actId, long rankId, Map<String, List<String>> memberIdMap);


    static ActSupportService getInstance(int dataSource) {
        switch (dataSource) {
            case RankDataSource.PEIWAN:
                return (ActSupportService) SpringBeanAwareFactory.getBean("actPwSupportService");
            case RankDataSource.YUEZHAN:
            case RankDataSource.JIAOYOU:
                return (ActSupportService) SpringBeanAwareFactory.getBean("actJySupportService");
            case RankDataSource.BAOBEI:
                return (ActSupportService) SpringBeanAwareFactory.getBean("actBbSupportService");
            case RankDataSource.ZHUIWAN:
                return (ActSupportService) SpringBeanAwareFactory.getBean("actZySupportService");
            case RankDataSource.YULE:
                return (ActSupportService) SpringBeanAwareFactory.getBean("actYuleSupportService");
            case RankDataSource.SKILL_CARD:
                return (ActSupportService) SpringBeanAwareFactory.getBean("actSkillCardSupportService");
            case RankDataSource.GAMEECOLOGY:
            default:
                return (ActSupportService) SpringBeanAwareFactory.getBean("actGeSupportService");
        }
    }

    /**
     * 获取模板
     * @return
     */
    /* Template getTemplate();*/

    /**
     * 获取用户信息的构建器
     *
     * @return
     */
    RoleBuilder getUserRankBuilder();

    /**
     * 获取主播信息的构建器
     *
     * @return
     */
    RoleBuilder getBabyRankBuilder();

    /**
     * 获取主持信息的构建器
     *
     * @return
     */
    RoleBuilder getHostRankBuilder();

    /**
     * 获取工会信息的构建器
     *
     * @return
     */
    RoleBuilder getGuildRankBuilder();

    /**
     * 获取厅信息的构建器
     *
     * @return
     */
    RoleBuilder getSubGuildRankBuilder();

    default RoleBuilder getFamilyRankBuilder() {
        return null;
    }

    default RoleBuilder getRoomRankBuilder(){return null;}

    /**
     * 获取团信息的构建器
     *
     * @return
     */
    RoleBuilder getTeamRankBuilder();

    default RoleBuilder getTingRoleBuilder() {
        return null;
    }

    /**
     * 获取客服接待信息的构建器
     *
     * @return
     */
    default RoleBuilder getWaiterRoleBuilder() {
        return null;
    }

    /**
     * 获取厅管信息的构建器
     *
     * @return
     */
    default RoleBuilder getTingMgrRoleBuilder() {
        return null;
    }


    /**
     * 通过角色类型获取相应的构造器
     *
     * @param roleType
     * @return
     */
    default RoleBuilder getRankBuilder(int roleType) {
        switch (RoleType.findByValue(roleType)) {
            case ANCHOR:
                return getBabyRankBuilder();
            case HOST:
                return getHostRankBuilder();
            case GUILD:
                return getGuildRankBuilder();
            case HALL:
                return getSubGuildRankBuilder();
            case PWTUAN:
                return getTeamRankBuilder();
            case WAITER:
                return getWaiterRoleBuilder();
            case TING:
                return getTingRoleBuilder();
            case TING_MGR:
                return getTingMgrRoleBuilder();
            case FAMILY:
                return getFamilyRankBuilder();
            case ROOM:
                return getRoomRankBuilder();
            default:
                return getUserRankBuilder();
        }
    }
}
