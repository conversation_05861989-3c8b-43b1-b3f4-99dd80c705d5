package com.yy.gameecology.activity.worker.web.test;

import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.worker.web.BaseController;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.hdzj.element.component.RankingTaskBannerComponent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/2/24 17:14
 **/
@RestController
@RequestMapping(value = "/test/broadCast")
public class TestBroadCastController extends BaseController {
    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private RankingTaskBannerComponent rankingTaskBannerComponent;


    @RequestMapping("testCommonNoticeUnicast")
    public Response<String> testCommonNoticeUnicast(@RequestParam(name = "actId", defaultValue = "0") long actId,
                                                    String noticeType, String noticeValue, String noticeExt,
                                                    @RequestParam(name = "uid", defaultValue = "0") long uid) {

        if (SysEvHelper.isDeploy()) {
            return Response.success("测试接口线上不执行");
        }

        if (actId <= 0 || uid <= 0 || StringUtils.isEmpty(noticeType) || StringUtils.isEmpty(noticeType) || StringUtils.isEmpty(noticeExt)) {
            return Response.success("参数缺失");
        }

        // long actId, String noticeType, String noticeValue, String noticeExt, long uid
        commonBroadCastService.commonNoticeUnicast(actId, noticeType, noticeValue, noticeExt, uid);
        return Response.success("调用成功");
    }

    @RequestMapping("testCommonBannerBroadcastToMakeFriend")
    public Response<String> testCommonBannerBroadcastToMakeFriend(@RequestParam(name = "actId", defaultValue = "0") long actId, String svgaUrl,
                                                                  @RequestParam(name = "uid", defaultValue = "0") long uid,
                                                                  @RequestParam(name = "score", defaultValue = "0") long score,
                                                                  @RequestParam(name = "bannerId", defaultValue = "0") long bannerId) {

        if (SysEvHelper.isDeploy()) {
            return Response.success("测试接口线上不执行");
        }

        if (actId <= 0 || uid <= 0 || StringUtils.isEmpty(svgaUrl)) {
            return Response.success("参数缺失");
        }

        // long actId, long uid, long score, String svgaUrl, long bannerId
        commonBroadCastService.commonBannerBroadcastToMakeFriend(actId, uid, score, svgaUrl, bannerId);
        return Response.success("调用成功");
    }
}
