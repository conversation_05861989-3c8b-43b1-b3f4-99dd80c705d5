package com.yy.gameecology.activity.client.thrift;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.consts.CacheTimeout;
import com.yy.gameecology.common.consts.HdztRoleId;
import com.yy.gameecology.common.utils.Clock;
import com.yy.thrift.act_ext_support.ActSupportService;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import com.yy.thrift.act_ext_support.QueryMemberRequest;
import com.yy.thrift.act_ext_support.QueryMemberResponse;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * desc: 陪玩 活动信息相关支持
 *
 * @createBy 曾文帜
 * @create 2020-08-20 15:23
 **/
@Component
public class PeiwanThriftClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    //陪玩团角色id
    private static final String PEWAN_TEAM_ROLE_ID = "90060";

    //陪玩公会角色id
    private static final String PEWAN_CHANNEL_ROLE_ID = "90004";

    //陪玩陪陪角色id
    private static final String PEWAN_PP_ROLE_ID = "90001";

    /*@Reference(protocol = "attach_nythrift", owner = "${actPwSupportService_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"})
    private ActSupportService.Iface proxy;

    @Reference(protocol = "attach_nythrift", owner = "${actPwSupportService_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"}
            , retries = 2 , cluster = "failover")
    private ActSupportService.Iface readProxy;*/

   /* public ActSupportService.Iface getProxy() {
        return proxy;
    }

    public ActSupportService.Iface getReadProxy() {
        return readProxy;
    }*/

    //陪玩团
    @Cached(timeToLiveMillis = CacheTimeout.YY_CHANNEL_INFO)
    public MemberItemInfo queryTeam(String teamId) {
        Map<String, List<String>> memberIdMap = Maps.newHashMap();
        List<String> memberIds = Lists.newArrayList();
        memberIds.add(teamId);
        memberIdMap.put(PEWAN_TEAM_ROLE_ID, memberIds);
        Map<String, Map<String, MemberItemInfo>> res = queryMember(0, 0, memberIdMap);
        if (res != null) {
            Map<String, MemberItemInfo> memberItemInfoMap = res.getOrDefault(PEWAN_TEAM_ROLE_ID, Maps.newHashMap());
            return memberItemInfoMap.get(teamId);
        }

        return null;
    }

    @Cached(timeToLiveMillis = CacheTimeout.YY_CHANNEL_INFO)
    public Map<String, MemberItemInfo> queryTeams(List<String> memberIds) {
        Map<String, List<String>> memberIdMap = Maps.newHashMap();
        memberIdMap.put(PEWAN_TEAM_ROLE_ID, memberIds);
        Map<String, Map<String, MemberItemInfo>> res = queryMember(0, 0, memberIdMap);
        if (res != null) {
            return res.getOrDefault(PEWAN_TEAM_ROLE_ID, Maps.newHashMap());
        }
        return Maps.newHashMap();
    }

    //陪玩公会
    @Cached(timeToLiveMillis = CacheTimeout.YY_CHANNEL_INFO)
    public MemberItemInfo queryChannel(String channelId) {
        Map<String, List<String>> memberIdMap = Maps.newHashMap();
        List<String> memberIds = Lists.newArrayList();
        memberIds.add(channelId);
        memberIdMap.put(PEWAN_CHANNEL_ROLE_ID, memberIds);
        Map<String, Map<String, MemberItemInfo>> res = queryMember(0, 0, memberIdMap);
        if (res != null) {
            Map<String, MemberItemInfo> memberItemInfoMap = res.getOrDefault(PEWAN_CHANNEL_ROLE_ID, Maps.newHashMap());
            return memberItemInfoMap.get(channelId + "");
        }

        return null;
    }

    // 批量查询公会信息
    @Cached(timeToLiveMillis = CacheTimeout.YY_CHANNEL_INFO)
    public Map<String, MemberItemInfo> batchQueryChannel(List<String> memberIds) {
        Map<String, List<String>> memberIdMap = Maps.newHashMap();
        memberIdMap.put(PEWAN_CHANNEL_ROLE_ID, memberIds);
        Map<String, Map<String, MemberItemInfo>> res = queryMember(0, 0, memberIdMap);
        if (res != null) {
            return res.getOrDefault(PEWAN_CHANNEL_ROLE_ID, Maps.newHashMap());
        }

        return null;
    }

    // 批量查询陪玩用户信息
    @Cached(timeToLiveMillis = CacheTimeout.YY_CHANNEL_INFO)
    public Map<String, MemberItemInfo> batchQueryPwInfo(List<String> memberIds) {
        Map<String, List<String>> memberIdMap = Maps.newHashMap();
        memberIdMap.put(PEWAN_PP_ROLE_ID, memberIds);
        Map<String, Map<String, MemberItemInfo>> res = queryMember(0, 0, memberIdMap);
        if (res != null) {
            return res.getOrDefault(PEWAN_PP_ROLE_ID, Maps.newHashMap());
        }

        return null;
    }

    /**
     * 查询用户信息
     *
     * @param actId
     * @param rankId
     * @param memberIdMap《角色id，用户IDs》
     * @return
     */
    public Map<String, Map<String, MemberItemInfo>> queryMember(long actId, long rankId, Map<String, List<String>> memberIdMap) {
        /*QueryMemberRequest request = new QueryMemberRequest();
        request.setActId(actId);
        request.setRankId(rankId);
        request.setMemberIdMap(memberIdMap);
        Clock clock = new Clock();
        try {
            QueryMemberResponse response = getReadProxy().queryMember(request);
            if (response != null && response.getCode() == 0) {
                return response.getMemberMap();
            } else {
                log.error("queryMember error,actId:{},rankId:{},member:{},clock:{},res:{}"
                        , actId, rankId, JSON.toJSONString(memberIdMap), clock.tag(), JSON.toJSONString(response));

            }

        } catch (Exception e) {
            log.error("queryMember error,actId:{},rankId:{},member:{},clock:{},e:{}"
                    , actId, rankId, JSON.toJSONString(memberIdMap), clock.tag(), e.getMessage(), e);
        }*/

        return Maps.newHashMap();
    }

    @Cached(timeToLiveMillis = CacheTimeout.PW_TUAN)
    public Set<String> queryPwChannelTuan(long actId, long sid) {
        /*QueryMemberRequest request = new QueryMemberRequest();
        request.setActId(actId);
        Map<String, List<String>> memberIdMap = Maps.newHashMap();
        memberIdMap.put(HdztRoleId.PW_CHANNEL + "", Collections.singletonList(sid + ""));
        request.setMemberIdMap(memberIdMap);
        Clock clock = new Clock();
        try {
            QueryMemberResponse response = getReadProxy().queryChannelAllTuan(request);
            if (response != null && response.getCode() != 0) {
                log.error("queryMember error,actId:{},sid:{},member:{},clock:{},res:{}"
                        , actId, sid, JSON.toJSONString(memberIdMap), clock.tag(), JSON.toJSONString(response));
                return null;
            } else {
                return response.getMemberMap()
                        .get(HdztRoleId.PW_CHANNEL + "").keySet();

            }

        } catch (Exception e) {
            log.error("queryMember error,actId:{},sid:{},member:{},clock:{},e:{}"
                    , actId, sid, JSON.toJSONString(memberIdMap), clock.tag(), e.getMessage(), e);
            return null;
        }*/
        return new HashSet<>();
    }
}
