package com.yy.gameecology.activity.bean.anchorwelfare;

import lombok.Data;

import java.util.List;

@Data
public class WelfareTaskInfoVo {

    /**
     * 已累计领取
     */
    private long totalWithdraw;

    /**
     * 待领取
     */
    private long waitWithdraw;


    private boolean newUser;


    /**
     * 登录信息
     */
    private LoginTaskVo loginInfo;

    /**
     * 签到任务列表
     */
    private List<TaskGroupVo>  taskGroupList;
}
