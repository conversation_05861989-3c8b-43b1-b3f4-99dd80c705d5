package com.yy.gameecology.activity.bean.hdzt;


import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-01-13 18:19
 **/
public class RankPhasePair {
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;
    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "是否日榜，-1则忽略请求中的date字段")
    private long ignoreDate;

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public long getIgnoreDate() {
        return ignoreDate;
    }

    public void setIgnoreDate(long ignoreDate) {
        this.ignoreDate = ignoreDate;
    }
}
