package com.yy.gameecology.activity.bean.acttask;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-02-23 10:48
 **/
@Data
public class TaskGroupShowConfig {

    @ComponentAttrField(labelText = "任务组名称")
    private String name;

    @ComponentAttrField(labelText = "排序", remark = "数字小优先级高")
    private int sort;

    @ComponentAttrField(labelText = "最小完成任务数", remark = "大于0时有效，定义展示完成任务组任务的最小任务数。如果不配置，则完成任务组所有任务才算完成任务")
    private int minCompleteTask;

    @ComponentAttrField(labelText = "是否展示详细任务", remark = "true===展示 false===不展示，给前端用")
    private boolean showDetails;
}
