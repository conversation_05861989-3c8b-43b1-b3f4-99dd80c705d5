package com.yy.gameecology.activity.retry;

import com.yy.fostress.api.RetryContext;
import com.yy.fostress.sitter.api.StatelessRetryCallback;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("batchWelfareRetryCallback")
public class BatchWelfareRetryCallback extends StatelessRetryCallback<BatchWelfareRetryCallback.BatchWelfareParams, Throwable> {

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Override
    public Object execute(RetryContext context, BatchWelfareParams obj) throws Throwable {
        BatchWelfareResult result = hdztAwardServiceClient.doBatchWelfare(obj.time, obj.busiId, obj.uid, obj.taskId, obj.taskPackageIds, obj.seq,obj.getExtData());
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("doBatchWelfare fail:" + (result == null ? StringUtils.EMPTY : result.getReason()));
        }

        return result;
    }

    @Override
    public Class<BatchWelfareParams> getMsgClass() {
        return BatchWelfareRetryCallback.BatchWelfareParams.class;
    }

    @Data
    public static class BatchWelfareParams {
        public BatchWelfareParams() {
        }

        public BatchWelfareParams(String time, long busiId, long uid, long taskId, Map<Long, Map<Long, Integer>> taskPackageIds, String seq,Map<String,String> extData) {
            this.time = time;
            this.busiId = busiId;
            this.uid = uid;
            this.taskId = taskId;
            this.taskPackageIds = taskPackageIds;
            this.seq = seq;
            this.extData = extData;
        }

        protected String time;
        protected long busiId;
        protected long uid;
        protected long taskId;
        protected Map<Long, Map<Long, Integer>> taskPackageIds;
        protected String seq;
        protected Map<String,String> extData;
    }
}
