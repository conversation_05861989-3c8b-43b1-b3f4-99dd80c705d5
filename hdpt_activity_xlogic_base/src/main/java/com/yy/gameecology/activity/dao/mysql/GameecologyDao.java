package com.yy.gameecology.activity.dao.mysql;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.gameecology.common.db.BaseMysqlDao;
import com.yy.gameecology.common.db.model.gameecology.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2019/11/28
 */
@Repository
public class GameecologyDao extends BaseMysqlDao {

    @Override
    @Autowired
    @Qualifier("gameecologyJdbcTemplate")
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 更新参数内容, 注意 ge_parameter.name 并不是主键！！！
     *
     * <AUTHOR>
     * @date 2020年4月24日 上午10:38:22
     */
    public int updateGeParameterByName(String paramName, String paramValue) {
        String sql = "update ge_parameter set value = ? where name = ?";
        int ret = this.update(sql, paramValue, paramName);
        return ret;
    }

    /**
     * 挂件展示榜单映射配置
     */
    public List<ActRoleRankMap> getAllEffectActRoleRankMap() {
        ActRoleRankMap where = new ActRoleRankMap();
        where.setStatus(1);

        return this.select(ActRoleRankMap.class, where);
    }

    public ActTaskScore getActTaskScore(long actId, long uid, long level) {
        ActTaskScore where = new ActTaskScore();
        where.setActId(actId);
        where.setUid(uid);
        where.setLevel(level);

        return this.selectOne(ActTaskScore.class, where, "");
    }

    public ActTaskScore queryAnchorTask(long actId, long uid, String taskType, String taskTime, long level) {
        ActTaskScore where = new ActTaskScore();
        where.setActId(actId);
        where.setUid(uid);
        where.setLevel(level);
        where.setTaskTime(taskTime);
        where.setTaskType(taskType);

        return this.selectOne(ActTaskScore.class, where, "");
    }

    public List<ActTaskScore> queryAnchorTasksByTime(long actId, long uid, String taskTime) {
        ActTaskScore where = new ActTaskScore();
        where.setActId(actId);
        where.setUid(uid);
        where.setTaskTime(taskTime);

        return this.select(ActTaskScore.class, where);
    }

    public List<ActTaskScore> queryAnchorTasksByType(long actId, long uid, String taskTime, String type) {
        ActTaskScore where = new ActTaskScore();
        where.setActId(actId);
        where.setUid(uid);
        where.setTaskTime(taskTime);
        where.setTaskType(type);
        return this.select(ActTaskScore.class, where);
    }

    public List<ActTaskScore> queryAnchorTasks(ActTaskScore actTaskScore) {
        return this.select(ActTaskScore.class, actTaskScore);
    }


    public int copyLastDayTask(long actId, String lastDay, String taskType, String taskTime) {
        String sql = "INSERT IGNORE INTO gameecology.act_task_score (act_id, uid, task_type, task_time, level, score, award, c_time) " +
                "select act_id, uid, task_type, " + taskTime + ", level, score, award, now() from act_task_score where task_type = ?   " +
                "and task_time= ? and act_id= ?";
        return this.update(sql, taskType, lastDay, actId);
    }

    public int copyLastDayUncompletedTask(long actId, String lastDay, String taskTime, List<Long> uids) {
        String collect = uids.stream().map(item -> item + "").collect(Collectors.joining(","));
        String sql = "INSERT IGNORE INTO gameecology.act_task_score (act_id, uid, task_type, task_time, level, score, award, c_time) " +
                "select act_id, uid, task_type, " + taskTime + ", level, score, award, now() from act_task_score where completed = 0 " +
                "and task_time=" + lastDay + " and act_id=" + actId + " and uid in (" + collect + ")";
        return this.update(sql);
    }

    public int updateTaskScoreCompleted(ActTaskScore actTaskScore) {
        String sql = "update act_task_score set completed=1 where act_id=? and uid=? and task_time=? and task_type=? and level=?";

        return this.update(sql, actTaskScore.getActId(),
                actTaskScore.getUid(), actTaskScore.getTaskTime(), actTaskScore.getTaskType(), actTaskScore.getLevel());
    }

    public List<Integer> batchUpdateTaskScoreCompleted(List<ActTaskScore> taskScores) {
        if (CollectionUtils.isEmpty(taskScores)) {
            return Lists.newArrayList();
        }

        List<Integer> result = new ArrayList<>();

        for (ActTaskScore taskScore : taskScores) {
            result.add(updateTaskScoreCompleted(taskScore));
        }
        return result;
    }


    //INSERT INTO gameecology.act_task_score (act_id, uid, task_type, task_time, level, score, busi_id, completed, award, remark, c_time, u_time) VALUES (202010002, 7482, null, null, 0, 150000, 0, null, null, null, null, '2021-10-15 15:57:33');
    public int[] batchUpdateTaskScore(List<ActTaskScore> scores) {
        log.info("batchUpdateTaskScore param:{} ", JSON.toJSONString(scores));
        return this.jdbcTemplate.batchUpdate("update act_task_score set completed=1,u_time=? where act_id=? " +
                        "and uid=? and task_time=? and task_type=? and level=?",
                new BatchPreparedStatementSetter() {
                    @Override
                    public void setValues(PreparedStatement ps, int i) throws SQLException {
                        ActTaskScore taskScore = scores.get(i);
                        if (taskScore.getuTime() == null) {
                            taskScore.setuTime(new java.util.Date());
                        }
                        ps.setTimestamp(1, new Timestamp(taskScore.getuTime().getTime()));
                        ps.setLong(2, taskScore.getActId());
                        ps.setLong(3, taskScore.getUid());
                        ps.setString(4, taskScore.getTaskTime());
                        ps.setString(5, taskScore.getTaskType());
                        ps.setLong(6, taskScore.getLevel());
                        log.info("batchUpdateTaskScore setValues param:{}", JSON.toJSONString(taskScore));
                    }

                    @Override
                    public int getBatchSize() {
                        return scores.size();
                    }
                });
    }

    public List<ActTaskScore> queryCompletedTaskScore(long actId, String lastDay, String taskType, long level, long complete) {
        ActTaskScore where = new ActTaskScore();
        where.setActId(actId);
        where.setTaskTime(lastDay);
        where.setTaskType(taskType);
        where.setCompleted(complete);
        where.setLevel(level);
        String afterWhere = " order by level asc ";
        return this.select(ActTaskScore.class, where, afterWhere);
    }

    public List<Long> queryTaskUidByCompleted(long actId, String lastDay, String taskType, int complete) {
        String sql = "select uid from act_task_score where act_id=" + actId + " and task_type='" + taskType + "' and completed=" + complete
                + " and task_time='" + lastDay + "'";
        return this.queryForList(sql, Long.class);
    }

    public int insertActTaskScore(ActTaskScore actTaskScore) {
        return this.insert(ActTaskScore.class, actTaskScore);
    }

    /**
     * TODO 需要定时移除无效活动数据
     */
    public List<ActTaskMember> getAllActTaskMember() {
        return this.select(ActTaskMember.class, null);
    }

    /**
     * 获取定时广播配置
     *
     * @param where
     * @return
     */
    public List<BroadcastTimerConfig> getBroadcastTimerConfigs(BroadcastTimerConfig where) {
        return this.select(BroadcastTimerConfig.class, where);
    }

    /**
     * 获取版榜单结果广播配置
     *
     * @param where
     * @return
     */
    public List<BroadcastRankResultConfig> geBroadcastRankResultConfig(BroadcastRankResultConfig where) {
        return this.select(BroadcastRankResultConfig.class, where, " ORDER BY calc_type, calc_value ");
    }

    //INSERT INTO gameecology.act_task_score (act_id, uid, task_type, task_time, level, score, busi_id, completed, award, remark, c_time, u_time) VALUES (202010002, 7482, null, null, 0, 150000, 0, null, null, null, null, '2021-10-15 15:57:33');
    public int[] batchInsertActTaskScore(List<ActTaskScore> scores) {
        return this.jdbcTemplate.batchUpdate("INSERT IGNORE INTO gameecology.act_task_score (act_id, uid, task_type, " +
                        "task_time, level, score, award, c_time, completed, u_time) VALUES (?,?,?,?,?,?,?,?,?, ?)",
                new BatchPreparedStatementSetter() {
                    @Override
                    public void setValues(PreparedStatement ps, int i) throws SQLException {
                        ActTaskScore taskScore = scores.get(i);
                        ps.setLong(1, taskScore.getActId());
                        ps.setLong(2, taskScore.getUid());
                        ps.setString(3, taskScore.getTaskType());
                        ps.setString(4, taskScore.getTaskTime());
                        ps.setLong(5, taskScore.getLevel());
                        ps.setLong(6, taskScore.getScore());
                        ps.setString(7, taskScore.getAward());
                        ps.setTimestamp(8, new Timestamp(taskScore.getcTime().getTime()));
                        if (taskScore.getCompleted() == null) {
                            taskScore.setCompleted(0L);
                        }
                        ps.setLong(9, taskScore.getCompleted());
                        if (taskScore.getuTime() == null) {
                            taskScore.setuTime(new java.util.Date());
                        }
                        ps.setTimestamp(10, new Timestamp(taskScore.getuTime().getTime()));
                    }

                    @Override
                    public int getBatchSize() {
                        return scores.size();
                    }
                });
    }

    /**
     * 查询时间区间内奖励的最大值
     *
     * @param taskTime 任务时间
     * @param taskType 任务类型
     */
    public List<ActTaskScore> queryMaxAward(long actId, String taskTime, List<String> taskType) {
        String sql = String.format("select max(award+0) award,uid from gameecology.act_task_score where completed=1 and act_id=?  and task_time= ? and task_type in ('%s') group by uid"
                , StringUtils.join(taskType, "','"));
        return this.queryForList(sql, ActTaskScore.class, actId, taskTime);
    }

    /**
     * 查询一天的奖励
     */
    public List<ActTaskScore> queryAward(long actId, String taskTime, List<String> taskType) {
        ActTaskScore where = new ActTaskScore();
        where.setActId(actId);
        where.setTaskTime(taskTime);
        where.setCompleted(1L);
        String afterWhere = String.format(" and task_type in ('%s') ", StringUtils.join(taskType, "','")) + " order by u_time asc  ";
        return this.select(ActTaskScore.class, where, afterWhere);
    }

    /**
     * 批量更新
     *
     * @param actResultList
     * @return
     */
    public int[] batchActResultUpdate(List<ActResult> actResultList) {

        return this.jdbcTemplate.batchUpdate(
                "update act_result set member_id=? ,status =?, gen_data_time = NOW(), contributor = ? where act_id = ? and group_id=? and rank =?",
                new BatchPreparedStatementSetter() {
                    public void setValues(PreparedStatement ps, int i)
                            throws SQLException {
                        ActResult actResult = actResultList.get(i);
                        ps.setString(1, actResult.getMemberId());
                        ps.setInt(2, actResult.getStatus());
                        ps.setString(3, actResult.getContributor());
                        ps.setLong(4, actResult.getActId());
                        ps.setString(5, actResult.getGroupId());
                        ps.setInt(6, actResult.getRank());
                    }

                    public int getBatchSize() {
                        return actResultList.size();
                    }
                });

    }
}
