package com.yy.gameecology.activity.bean.mq;


import java.util.List;

/**
 * <AUTHOR> 2021/4/19
 */

public class FriendSealEvent {

    private String seqID;
    private long sendUid;
    private long recvUid;
    private long compereUid;
    private long sid;
    private long ssid;
    private long signSid;
    /**
     * 时间: 微秒
     */
    private long timestamp;
    private int platform;
    private int channel;
    private List<FriendGift> giftList;
    private FriendGiftExpand expand;

    public String getSeqID() {
        return seqID;
    }

    public void setSeqID(String seqID) {
        this.seqID = seqID;
    }

    public long getSendUid() {
        return sendUid;
    }

    public void setSendUid(long sendUid) {
        this.sendUid = sendUid;
    }

    public long getRecvUid() {
        return recvUid;
    }

    public void setRecvUid(long recvUid) {
        this.recvUid = recvUid;
    }

    public long getCompereUid() {
        return compereUid;
    }

    public void setCompereUid(long compereUid) {
        this.compereUid = compereUid;
    }

    public long getSid() {
        return sid;
    }

    public void setSid(long sid) {
        this.sid = sid;
    }

    public long getSsid() {
        return ssid;
    }

    public void setSsid(long ssid) {
        this.ssid = ssid;
    }

    public long getSignSid() {
        return signSid;
    }

    public void setSignSid(long signSid) {
        this.signSid = signSid;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public int getPlatform() {
        return platform;
    }

    public void setPlatform(int platform) {
        this.platform = platform;
    }

    public int getChannel() {
        return channel;
    }

    public void setChannel(int channel) {
        this.channel = channel;
    }

    public List<FriendGift> getGiftList() {
        return giftList;
    }

    public void setGiftList(List<FriendGift> giftList) {
        this.giftList = giftList;
    }

    public FriendGiftExpand getExpand() {
        return expand;
    }

    public void setExpand(FriendGiftExpand expand) {
        this.expand = expand;
    }
}
