package com.yy.gameecology.activity.client.yrpc;

import com.yy.hdpt.aov.proto.HdptAov;
import org.apache.dubbo.common.annotation.Yrpc;

public interface HdptAovService {
    @Yrpc(functionName = "tryAddFirstAward")
    HdptAov.SimpleRsp tryAddFirstAward(HdptAov.AddFirstAwardReq req);

    @Yrpc(functionName = "tryAddAwardAccount")
    HdptAov.AddAwardAccountRsp tryAddAwardAccount(HdptAov.AddAwardAccountReq req);

    @Yrpc(functionName = "tryAddMobileEnrollment")
    HdptAov.SimpleRsp tryAddMobileEnrollment(HdptAov.AddMobileEnrollmentReq req);

    @Yrpc(functionName = "rollbackAddAwardAccount")
    HdptAov.SimpleRsp rollbackAddAwardAccount(HdptAov.RollbackAddAwardAccountReq req);
}
