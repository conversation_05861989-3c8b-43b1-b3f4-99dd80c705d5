package com.yy.gameecology.activity.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.Lifecycle;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.core.Ordered;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/5/19 15:22
 **/
@Component
@Slf4j
public class CloseKafkaContainer implements ApplicationContextAware, ApplicationListener<ContextClosedEvent>, Ordered {
    private ApplicationContext applicationContext;

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        KafkaListenerEndpointRegistry registry = applicationContext.getBean(KafkaListenerEndpointRegistry.class);
        for (MessageListenerContainer container : registry.getAllListenerContainers()) {
            Lifecycle lifecycle = container;
            lifecycle.stop();
            log.info("[krista] stop kafka container:{}", container.getListenerId());
        }
    }

    @Override
    public int getOrder() {
        return -1;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
