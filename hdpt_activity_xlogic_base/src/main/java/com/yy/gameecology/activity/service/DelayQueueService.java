package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.duowan.udb.util.GsonUtil;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.ShutdownHolder;
import com.yy.gameecology.common.utils.StringUtil;
import io.opentelemetry.context.Context;
import org.apache.dubbo.common.utils.NamedThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static java.util.Collections.singletonList;

/**
 * @warn: 重要提示：底层存的是同一个redis，所以，如果需要数据隔离，一定要将key严格区分开来，否则会被别的分组的进程消费掉你的延迟消息
 * @Author: CXZ
 * @Desciption: redis延迟队列服务
 * @Date: 2021/4/9 14:14
 * @Modified:
 * @deprecated 使用组件里的 registerDelayQueue、publishDelayEvent
 */
@Service
@Deprecated
public class DelayQueueService implements InitializingBean, DisposableBean {

    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    private static final String Z_GET_AND_DEL_BY_SCORE_LUA = "zGetAndDelByScore.lua";
    /**
     * 存放排队信息的redis hash key
     */
    private static final String LINE_UP_KEY = "DelayQueueService_LINE_UP_HASH";
    /**
     * 排队执行的线程池
     */
    private ScheduledExecutorService executorService;

    @Override
    public void destroy() throws Exception {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.executorService = new ScheduledThreadPoolExecutor(20, new NamedThreadFactory("DelayQueueService"));
    }

    /**
     * 调用方，建议使用 @EventListener(ContextRefreshedEvent.class) 事件处理器来启动延迟事件消费线程
     * 一定要记得加判断，防止重复启用消费线程<br/>
     * if (event.getApplicationContext().getParent() != null) {
     *     return;
     * }
     * @param delaySettleKey
     * @param count
     * @param eventClass
     * @param delayHandle
     * @param <T>
     */
    public <T> void startDelaySettlement(String delaySettleKey, int count, Class<T> eventClass, Consumer<T> delayHandle) {
        final String delayKey = decorateDelayKey(delaySettleKey);
        log.info("startDelaySettlement with delaySettleKey:{}", delaySettleKey);
        threadPoolManager.get("delay_queue_consumer").execute(
                Context.current().wrap(()->{
                    delaySettlement(delayKey, count, eventClass, delayHandle);
                }));
    }

    /**
     * 延迟结算-需要自启一个线程执行
     * 从redis中获取延时时间结束的pk事件进行处理
     */
    protected <T> void delaySettlement(String delaySettleKey, int count, Class<T> EventClass, Consumer<T> delayHandle) {
        //历史广场不处理
        if (SysEvHelper.checkHistory(this.getClass().getName(), false)) {
            return;
        }

        while (true) {
            try {
                // 优雅停机：已关闭，不再去抢锁, 直接返回-避免重启报错
                if (ShutdownHolder.isShuttingDown()) {
                    log.warn("delaySettlement fail@application is shutdown. stop timer!key:{}", delaySettleKey);
                    return;
                }

                List<T> delayEvents = getDelayEvent(delaySettleKey, count, EventClass);
                if (delayEvents.isEmpty()) {
                    SysEvHelper.waiting(500);
                    continue;
                }
                for (T delayEvent : delayEvents) {
                    delayHandle.accept(delayEvent);
                }
            } catch (Exception e) {
                log.error("delaySettlement error,key:{} {}", delaySettleKey, e.getMessage(), e);
            }
        }
    }

    private String decorateDelayKey(String delayKey) {
        return delayKey + StringUtil.COLON + SysEvHelper.getGroup();
    }

    /**
     * 把事件放入延迟队列
     *
     * @param key
     * @param delaySec
     * @param event
     */
    public void setDelayEvent(String key, long delaySec, Object event) {
        key = decorateDelayKey(key);
        try {
            long expire = System.currentTimeMillis() + delaySec * 1000;
            String data = GsonUtil.getGson().toJson(event);
            //
            boolean rs = actRedisDao.zAdd(RedisConfigManager.DEFAULT_GROUP_CODE, key, data, expire);
            log.info("setDelayEvent with key:{}, data:{}, expire:{},rs={}", key, data, expire, rs);
        } catch (Exception e) {
            log.error("setDelayEvent error ,key:{} delaySec:{} event:{}", key, delaySec, JSON.toJSONString(event), e);
        }
    }


    /**
     * 获取延迟队列里面的事件
     *
     * @param key
     * @param count
     * @param EventClass
     * @param <T>
     * @return
     */
    private <T> List<T> getDelayEvent(String key, int count, Class<T> EventClass) {
        //历史广场不处理
        if (SysEvHelper.checkHistory(this.getClass().getName(), false)) {
            return Lists.newArrayList();
        }
        List<String> keys = singletonList(key);
        long min = 0;
        long max = System.currentTimeMillis();
        List<String> argv = Lists.newArrayList(String.valueOf(min), String.valueOf(max), String.valueOf(count));
        try {
            String result = actRedisDao.executeLua(RedisConfigManager.DEFAULT_GROUP_CODE, Z_GET_AND_DEL_BY_SCORE_LUA, String.class, keys, argv);
            Object dataObject = JSONObject.parseObject(result).get("result");
            if (dataObject instanceof JSONArray) {
                Gson gson = GsonUtil.getGson();
                return ((JSONArray) dataObject).stream().map(data -> gson.fromJson(data.toString(), EventClass))
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("getDelayEvent error,key:{} {}", key, e.getMessage(), e);
        }
        return Lists.newArrayList();
    }

    /**
     * 一个用redis排队的方法。返回延迟执行的时间，field key相同就需要排队，延迟执行
     * 相同key和lineUpKey 的任务执行的间隔时间必须大于intervalSec，第一个任务不等待直接执行
     *
     * @param key
     * @param field
     * @param startValue
     * @param intervalSec
     * @return
     */
    private Long lineUp(final String key, final String field, final long startValue, final long intervalSec) {
        //历史广场不处理
        if (SysEvHelper.checkHistory(this.getClass().getName(), false)) {
            return -1L;
        }

        List<String> keys = Lists.newArrayList(key, field);
        List<String> argv = Lists.newArrayList(String.valueOf(startValue), String.valueOf(intervalSec * 1000));
        Long result = actRedisDao.executeLua(RedisConfigManager.DEFAULT_GROUP_CODE, "lineUp.lua", Long.class, keys, argv);
        return result;
    }

    /**
     * 排队执行，本地jvm延迟，重启的会丢失未执行的任务 ----线程池有限，请不要执行耗时过多的操作
     * 相同的lineUpKey 的任务执行的间隔时间必须大于intervalSec，第一个任务不等待直接执行
     *
     * @param lineUpKey   排队的的唯一key ，key相同需要排队
     * @param intervalSec 排队的间隔时间
     * @param runnable
     */
    public void lineUpLocal(String lineUpKey, final long intervalSec, Runnable runnable) {
        //历史广场不处理
        if (SysEvHelper.checkHistory(this.getClass().getName(), false)) {
            return;
        }
        try {
            long nowTime = System.currentTimeMillis();
            long executionTime = lineUp(LINE_UP_KEY, lineUpKey, nowTime, intervalSec);
            //延迟时间
            long delayTime = executionTime - nowTime;
            if (delayTime > 0) {
                executorService.schedule(() -> {
                    runnable.run();
                    log.info("lineUp delay info: 执行前时间:{} 延迟执行的目标时间:{} 执行时间:{}", DateUtil.getPattenStrFromTime(nowTime, "HH:mm:ss")
                            , DateUtil.getPattenStrFromTime(executionTime, "HH:mm:ss")
                            , DateUtil.getPattenStrFromTime(System.currentTimeMillis(), "HH:mm:ss"));

                }, delayTime, TimeUnit.MILLISECONDS);
            } else {
                runnable.run();
            }
        } catch (Exception e) {
            log.error("lineUp error@lineUpKey:{} intervalSec:{} {}", lineUpKey, intervalSec, e.getMessage(), e);
        }

    }


}
