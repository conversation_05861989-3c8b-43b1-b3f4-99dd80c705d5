package com.yy.gameecology.activity.service.wzry;

import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.wzry.GameResult;
import com.yy.gameecology.activity.client.thrift.SaiBaoClient;
import com.yy.gameecology.common.consts.BattleMode;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.db.mapper.aov.AovGameMemberExtMapper;
import com.yy.gameecology.common.db.model.gameecology.aov.AovGameMember;
import com.yy.gameecology.common.db.model.gameecology.wzry.WzryGame;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.thrift.saibao.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-04-28 19:42
 **/
@Service
public class GameGatewayService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private SaiBaoClient saiBaoClient;

    @Autowired
    private AovGameMemberExtMapper aovGameMemberExtMapper;

    /**
     * 去赛宝查询游戏信息
     * 用户在赛宝房间，没准备好，赛宝返回的游戏房间数据会为空
     */
    public GameResult queryGameResult(String childId) {
        var gameResult = saiBaoClient.queryGameResult(childId, 0);
        return resolveGameResult(gameResult);
    }

    /**
     * @return seat_team
     */
    private GameResult resolveGameResult(RoomGameResultVO gameResultVO) {
        if (gameResultVO == null) {
            return null;
        }
        GameResult gameResult = new GameResult();

        Set<String> inGameSeat = Sets.newHashSet();
        Set<String> winnerSeat = Sets.newHashSet();

        if (MapUtils.isNotEmpty(gameResultVO.getTeamMap())) {
            for (int team : gameResultVO.getTeamMap().keySet()) {
                GameTeamVO teamVO = gameResultVO.getTeamMap().get(team);
                if (CollectionUtils.isNotEmpty(teamVO.getMemebers())) {
                    for (GameMember memeber : teamVO.getMemebers()) {
                        if (memeber.getSeatId() != 0 && memeber.isInGameRoom()) {
                            inGameSeat.add(memeber.getSeatId() + "_" + team);
                        }
                        //胜利队伍
                        if (teamVO.getScore() > 0) {
                            gameResult.setWinnerTeam(team);
                            winnerSeat.add(memeber.getSeatId() + "_" + team);
                        }
                    }
                }
            }
        }

        gameResult.setInGameSeat(inGameSeat);
        gameResult.setWinnerSeat(winnerSeat);

        gameResult.setState(gameResultVO.getState());
        gameResult.setRoomStatus(gameResultVO.getRoomStatus());
        gameResult.setBattleStatus(gameResultVO.getBattleStatus());
        gameResult.setGameResult(gameResultVO.toString());

        return gameResult;
    }

    public boolean allInGame(WzryGame wzryGame, GameResult gameResult) {
        if (wzryGame.getBattleMode() == BattleMode.GAME_1V1) {
            return gameResult.getInGameSeat().size() >= BattleMode.GAME_1V1_ENTER_LIMIT;
        } else if (wzryGame.getBattleMode() == BattleMode.GAME_5V5) {
            return gameResult.getInGameSeat().size() >= BattleMode.GAME_5V5_ENTER_LIMIT;
        } else {
            throw new SuperException("不支持的对战模式", SuperException.E_DATA_ERROR);
        }
    }


    public void closeGameRoom(long gameId, String childid) {
        if (Const.ONESTR.equals(Const.GEPM.getParamValue(GeParamName.REAL_CLOSE_SAI_BAO_GAME, Const.ONESTR))) {
            boolean closeResult = saiBaoClient.closeRoom(childid);
            if (!closeResult) {
                //TODO这里关闭失败了要一直卡主吗？
                log.error("closeSbTimeOutGame error saibao close room failed,game:{}", gameId);
            }
        }
    }

    /**
     * 查询房间信息，并修正腾讯返回的错误uid
     */
    public QuickMatchRoomInfoRsp queryRoomInfoTryFixUid(long aovGameId, String roomId, long userId) {
        QuickMatchRoomInfoRsp quickMatchRoomInfoRsp = saiBaoClient.queryRoomInfo(roomId, userId);
        if (quickMatchRoomInfoRsp == null || quickMatchRoomInfoRsp.getRoomInfo() == null) {
            return quickMatchRoomInfoRsp;
        }
        boolean needTryFixCampaMem = quickMatchRoomInfoRsp.getRoomInfo().getCampaMem() != null
                && quickMatchRoomInfoRsp.getRoomInfo().getCampaMem().stream()
                .filter(Objects::nonNull).anyMatch(member -> member.getUnionUid() > 0 && member.getUid() != member.getUnionUid());

        boolean needTryFixCampbMem = quickMatchRoomInfoRsp.getRoomInfo().getCampbMem() != null
                && quickMatchRoomInfoRsp.getRoomInfo().getCampbMem().stream()
                .filter(Objects::nonNull).anyMatch(member -> member.getUnionUid() > 0 && member.getUid() != member.getUnionUid());

        //有关联大账号才需要修正，减少数据库查询
        if (needTryFixCampaMem || needTryFixCampbMem) {
            List<AovGameMember> aovGameMembers = aovGameMemberExtMapper.batchSelectGameMembersByGameId(aovGameId);
            quickMatchRoomInfoRsp = tryFixRoomInfoUid(quickMatchRoomInfoRsp, aovGameMembers, roomId);
        }

        return quickMatchRoomInfoRsp;
    }


    public QuickMatchRoomInfoRsp tryFixRoomInfoUid(QuickMatchRoomInfoRsp quickMatchRoomInfoRsp, List<AovGameMember> aovGameMembers,String childId) {
        if (quickMatchRoomInfoRsp == null || quickMatchRoomInfoRsp.getRoomInfo() == null) {
            return quickMatchRoomInfoRsp;
        }
        Set<Long> realJoinGameUid = aovGameMembers.stream().map(AovGameMember::getUid).collect(Collectors.toSet());

        fixMembersUid(quickMatchRoomInfoRsp.getRoomInfo().getCampaMem(), realJoinGameUid, childId);
        fixMembersUid(quickMatchRoomInfoRsp.getRoomInfo().getCampbMem(), realJoinGameUid, childId);

        return quickMatchRoomInfoRsp;
    }

    private void fixMembersUid(List<RoomMemeberVO> members, Set<Long> realJoinGameUid, String childId) {
        if (CollectionUtils.isNotEmpty(members)) {
            for (RoomMemeberVO roomMemeberVO : members) {
                if (roomMemeberVO == null) {
                    continue;
                }
                boolean useUnionUid = !realJoinGameUid.contains(roomMemeberVO.getUid())
                        && roomMemeberVO.getUnionUid() > 0
                        && realJoinGameUid.contains(roomMemeberVO.getUnionUid());
                if (useUnionUid) {
                    log.info("tryFixRoomInfoUid childId:{},uid:{},unionUid:{}", childId, roomMemeberVO.getUid(), roomMemeberVO.getUnionUid());
                    roomMemeberVO.setUid(roomMemeberVO.getUnionUid());
                }
            }
        }
    }

    /**
     * 查询赛事结果，并修正腾讯返回的错误uid
     */
    public RoomGameResultVO queryGameResultTryFixUid(long aovGameId, String roomId, long userId) {
        RoomGameResultVO gameResultVO = saiBaoClient.queryGameResult(roomId, userId);
        boolean needTryFix = gameResultVO != null
                && gameResultVO.getTeamMap() != null
                && !gameResultVO.getTeamMap().isEmpty()
                && gameResultVO.getTeamMap().values().stream()
                .filter(Objects::nonNull)
                .flatMap(team -> team.getMemebers().stream())
                .filter(Objects::nonNull)
                .anyMatch(member -> member.getUnionUid() > 0 && member.getUid() != member.getUnionUid());

        //有关联大账号才需要修正，减少数据库查询
        if (needTryFix) {
            List<AovGameMember> aovGameMembers = aovGameMemberExtMapper.batchSelectGameMembersByGameId(aovGameId);
            gameResultVO = tryFixGameResultUid(gameResultVO, aovGameMembers, roomId);
        }

        return gameResultVO;
    }

    public RoomGameResultVO tryFixGameResultUid(RoomGameResultVO gameResultVO, List<AovGameMember> aovGameMembers,String roomId) {
        if (gameResultVO == null || MapUtils.isEmpty(gameResultVO.getTeamMap())) {
            return gameResultVO;
        }
        if (CollectionUtils.isEmpty(aovGameMembers)) {
            return gameResultVO;
        }
        Set<Long> realJoinGameUid = aovGameMembers.stream().map(AovGameMember::getUid).collect(Collectors.toSet());

        for (Integer team : gameResultVO.getTeamMap().keySet()) {
            GameTeamVO gameTeamVO = gameResultVO.getTeamMap().get(team);
            if (gameTeamVO == null || gameTeamVO.getMemebers() == null) {
                continue;
            }
            for (GameMember gameMember : gameTeamVO.getMemebers()) {
                if (gameMember == null) {
                    continue;
                }
                boolean useUnionUid = !realJoinGameUid.contains(gameMember.getUid())
                        && gameMember.getUnionUid() > 0
                        && realJoinGameUid.contains(gameMember.getUnionUid());
                if (useUnionUid) {
                    log.info("fixGameResultUid childId:{},uid:{},unionUid:{}", roomId, gameMember.getUid(), gameMember.getUnionUid());
                    gameMember.setUid(gameMember.getUnionUid());
                }
            }
        }

        return gameResultVO;
    }
}
