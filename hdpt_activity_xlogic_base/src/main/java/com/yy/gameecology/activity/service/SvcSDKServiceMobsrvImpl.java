package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.turnover.TFamilySsid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.yy.rpc.protocol.entmob.ProtocolEnum;
import org.apache.dubbo.yy.rpc.protocol.entmob.cast.MobCastService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SvcSDKServiceMobsrvImpl implements SvcSDKService {

    public static final int ECOLOGY_MSG_MIN_TYPE = Integer.parseInt(SysEvHelper.getGroup()) * 1000 + 888;

    @Resource
    private MobCastService mobCastService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private WhiteListService whiteListService;

    @Override
    public void sdkBcUsergroup(GameecologyActivity.GameEcologyMsg message) {
        throw new RuntimeException("not support");
    }

    @Override
    public void unicastUid(long uid, GameecologyActivity.GameEcologyMsg message) {
        try {
            mobCastService.unicast(uid, Const.ENT_MAX_TYPE, ECOLOGY_MSG_MIN_TYPE, message, ProtocolEnum.protobuf.name());
        } catch (Exception e) {
            log.error("SvcSDKServiceYYPImpl mob unicast exception:", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void broadcastSub(long sid, long ssid, GameecologyActivity.GameEcologyMsg message) {
        try {
            mobCastService.broadcastSubSid(sid, ssid, Const.ENT_MAX_TYPE, ECOLOGY_MSG_MIN_TYPE, message, ProtocolEnum.protobuf.name());
        } catch (Exception e) {
            log.error("SvcSDKServiceYYPImpl mob broadcast sub exception:", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void broadcastTop(long sid, GameecologyActivity.GameEcologyMsg message) {
        try {
            mobCastService.broadcastTopSid(sid, Const.ENT_MAX_TYPE, ECOLOGY_MSG_MIN_TYPE, message, ProtocolEnum.protobuf.name());
        } catch (Exception e) {
            log.error("SvcSDKServiceYYPImpl mob broadcast top exception:", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void broadcastTemplate(Template template, GameecologyActivity.GameEcologyMsg message) {
        Assert.notNull(template, "template is null");
        if (isNotNeedSend(message)) {
            return;
        }
        List<ChannelInfo> channelInfos = commonService.queryOnlineChannel(template);
        try {
            broadcastTemplate(template, message, channelInfos);
        } catch (Exception e) {

        }
    }

    @Override
    public void broadcastTemplate(long actId, Template template, GameecologyActivity.GameEcologyMsg message) {
        Assert.notNull(template, "template is null");
        if (isNotNeedSend(message)) {
            return;
        }
        List<ChannelInfo> channelInfos = commonService.queryBroChannels(actId, template);
        try {
            broadcastTemplate(template, message, channelInfos);
        } catch (Exception e) {

        }
    }

    @Override
    public void broadcastAllChanelsInPW(long actId, GameecologyActivity.GameEcologyMsg message) {
        List<ChannelInfo> channelInfos = whiteListService.getPwAllChannel(actId);

        log.info("begin bro,actId:{},size:{},uri:{}", actId, channelInfos.size(), message.getUri());
        for (ChannelInfo channelInfo : channelInfos) {
            broadcastTop(channelInfo.getSid(), message);
        }
        log.info("bro end,actId:{},channel size:{},uri:{}", actId, channelInfos.size(), message.getUri());
    }

    @Override
    public void broadcastAllChanelsInSkillCard(GameecologyActivity.GameEcologyMsg message) {
        List<ChannelInfo> channelInfos = commonService.getSkillCardAllChannel();

        log.info("begin broadcastAllChanelsInSkillCard,size:{},uri:{}", channelInfos.size(), message.getUri());
        for (ChannelInfo channelInfo : channelInfos) {
            broadcastTop(channelInfo.getSid(), message);
        }
        log.info("bro broadcastAllChanelsInSkillCard,channel size:{},uri:{}", channelInfos.size(), message.getUri());
    }

    @Override
    public void broadcastFamilyInSkillCard(long familyId, GameecologyActivity.GameEcologyMsg message) {
        List<ChannelInfo> familySsids = commonService.listFamilySsid2(familyId);
        log.info("begin broadcastFamilyInSkillCard,size:{},uri:{}", familySsids.size(), message.getUri());
        for (ChannelInfo ting : familySsids) {
            broadcastSub(ting.getSid(), ting.getSsid(), message);
        }
        log.info("bro broadcastFamilyInSkillCard done,ting size:{},uri:{}", familySsids.size(), message.getUri());
    }

    @Override
    public void broadcastTemplate(Template template, GameecologyActivity.GameEcologyMsg message, long sid, long ssid) {
        Assert.notNull(template, "template is null");
        if (isNotNeedSend(message)) {
            return;
        }
        List<ChannelInfo> channelInfos = commonService.queryOnlineChannel(template);
        if (CollectionUtils.isEmpty(channelInfos)) {
            return;
        }
        List<ChannelInfo> matchChannelInfos = channelInfos.parallelStream()
                .filter(channelInfo -> (sid == channelInfo.getSid() || sid == 0)
                        && ssid != channelInfo.getSsid())
                .collect(Collectors.toList());
        broadcastTemplate(template, message, matchChannelInfos);
    }

    private boolean isNotNeedSend(GameecologyActivity.GameEcologyMsg message) {
        Assert.notNull(message, "message is null");
        // || SysEvHelper.skipSvcsdkInit()
        if (SysEvHelper.isLocal() || SysEvHelper.isYuFa()) {
            return true;
        }
        if (isClose(message.getUri())) {
            return true;
        }
        return false;
    }

    private boolean isClose(int uri) {
        boolean close =
                Const.GEPM.getParamValueToInt(GeParamName.SVCSDK_BROADCAST_SWITCH + uri, 1) == 0;
        if (close) {
            log.info("svcsdk isClose uri:{} close:{}", uri, close);
        }
        return close;
    }

    private void broadcastTemplate(Template template, GameecologyActivity.GameEcologyMsg message,
                                   List<ChannelInfo> channelInfos) {
        if (CollectionUtils.isEmpty(channelInfos)) {
            return;
        }

        for (ChannelInfo channelInfo : channelInfos) {
            long sid = channelInfo.getSid(), ssid = channelInfo.getSsid();
            try {
                if (ssid <= 0 || ssid == sid) {
                    broadcastTop(sid, message);
                } else {
                    broadcastSub(sid, ssid, message);
                }
            } catch (Throwable e) {
                log.error("broadcastTemplate topSid:{}, subSid:{}", sid, ssid, e);
            }
        }
        log.info("broadcastTemplate is completed, template:{}, message:{}", template, message);
    }
}
