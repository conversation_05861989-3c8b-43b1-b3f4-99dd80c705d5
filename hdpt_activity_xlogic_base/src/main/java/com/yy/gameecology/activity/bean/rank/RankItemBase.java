package com.yy.gameecology.activity.bean.rank;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.rankroleinfo.RoleKey;

import java.util.Map;


/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-21 17:49
 **/
public class RankItemBase implements RoleKey {
    private Integer rank;

    private String itemDesc = "";

    /**
     * 业务透传给前端的扩展信息
     */
    private Map<String, String> viewExt = Maps.newHashMap();



    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public String getItemDesc() {
        return itemDesc;
    }

    public void setItemDesc(String itemDesc) {
        this.itemDesc = itemDesc;
    }


    @Override
    public void setKey(String key) {

    }

    @Override
    public String getKey() {
        return null;
    }

    public void setValue(Long value) {

    }

    public Map<String, String> getViewExt() {
        return viewExt;
    }

    public void setViewExt(Map<String, String> viewExt) {
        this.viewExt = viewExt;
    }
}
