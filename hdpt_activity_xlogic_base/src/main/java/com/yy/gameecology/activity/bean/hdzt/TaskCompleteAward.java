package com.yy.gameecology.activity.bean.hdzt;


/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-09-27 22:14
 **/
public class TaskCompleteAward {
    private long uid;
    private long score;
    private String awardCode;
    private long awardValue;

    //活动中台发奖任务id
    private long hdztAwardTaskId;
    //活动中台发奖奖品id
    private long hdztAwardPackageId;

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getScore() {
        return score;
    }

    public void setScore(long score) {
        this.score = score;
    }

    public String getAwardCode() {
        return awardCode;
    }

    public void setAwardCode(String awardCode) {
        this.awardCode = awardCode;
    }

    public long getAwardValue() {
        return awardValue;
    }

    public void setAwardValue(long awardValue) {
        this.awardValue = awardValue;
    }

    public long getHdztAwardTaskId() {
        return hdztAwardTaskId;
    }

    public void setHdztAwardTaskId(long hdztAwardTaskId) {
        this.hdztAwardTaskId = hdztAwardTaskId;
    }

    public long getHdztAwardPackageId() {
        return hdztAwardPackageId;
    }

    public void setHdztAwardPackageId(long hdztAwardPackageId) {
        this.hdztAwardPackageId = hdztAwardPackageId;
    }
}
