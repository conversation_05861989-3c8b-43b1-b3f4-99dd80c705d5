package com.yy.gameecology.activity.service.yule.lpf;

import com.google.common.collect.Maps;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.service.yule.lpf.domain.pb.lpfm2YYP;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/18 16:15
 **/
@Component
@Data
@Slf4j
public class Lpfm2YYP {
    private final static String SERVICE_NAME = "revenue-micrank";

    @Reference(protocol = "yyp", owner = "svc_lpfm2_liveroominfo_yyp_yy_test", timeout = 3000)
    private Lpfm2SdkLiveRoomInfoYypService lpfm2SdkLiveRoomInfoYypServiceTest;

//    @Reference(protocol = "yyp", owner = "svc_lpfm2_liveroominfo_yyp_yy_test", timeout = 3000, url = "yyp://**************:10053")
//    private Lpfm2SdkLiveRoomInfoYypService lpfm2SdkLiveRoomInfoYypServiceLocal;

    @Reference(protocol = "yyp", owner = "server_lpfm2_liveroominfo_yyp_yy", timeout = 3000)
    private Lpfm2SdkLiveRoomInfoYypService lpfm2SdkLiveRoomInfoYypService;

    public Lpfm2YYP() {
    }

    // * sid 顶级频道
    // * ssid 子频道
    public Map<String, Map<String, String>> getPublishInfoByUids(int hostId, List<Long> uids) {
        try {
            if (CollectionUtils.isEmpty(uids)) {
                return Maps.newHashMap();
            }

            var req = lpfm2YYP.GetPublishInfoByUidsReq.newBuilder()
                    .setSrvname(SERVICE_NAME)
                    .setAppID(60035)
                    .addAllUids(uids)
                    .setHostID(hostId);
            var service = lpfm2SdkLiveRoomInfoYypServiceTest;
            if (SysEvHelper.isDeploy()) {
                req.setAppID(15013);
                service = lpfm2SdkLiveRoomInfoYypService;
            }

            Map<String, Map<String, String>> resultMap = Maps.newHashMap();
            Clock clock = new Clock();
            lpfm2YYP.GetPublishInfoByUidsRsp resp = service.getPublishInfoByUids(req.build());
            log.info("getPublishInfoByUids done,hostId={},uids={},req={},resp={} {}", hostId, uids
                    , JsonFormat.printToString(req.build()), JsonFormat.printToString(resp), clock.tag());
            if (resp.getResult() == 0) {
                List<lpfm2YYP.QueryLivingDataMap> list = resp.getCurLiveListList();
                for (lpfm2YYP.QueryLivingDataMap map : list) {
                    resultMap.put(map.getExtendMap().get("uid"), map.getExtendMap());
                }
            }

            return resultMap;
        } catch (Exception ex) {
            log.error("getPublishInfoByUids error,hostId={},uids={}", hostId, uids, ex);
            return Maps.newHashMap();
        }
    }
}
