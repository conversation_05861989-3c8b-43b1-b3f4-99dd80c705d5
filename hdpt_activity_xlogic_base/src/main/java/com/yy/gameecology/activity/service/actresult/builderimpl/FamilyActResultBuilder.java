package com.yy.gameecology.activity.service.actresult.builderimpl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.rankroleinfo.FamilyRoleItem;
import com.yy.gameecology.activity.service.actresult.ActResultMemberLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-11-17 16:26
 **/
@Component
public class FamilyActResultBuilder extends BuilderBase implements ActResultMemberLoader {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    public Map<String, Map<String, MemberInfo>> loadMemberInfo(long actId, long type, long roleType, List<String> memberIds) {
        Map<String, FamilyRoleItem> itemMap = actSkillCardSupportService.getFamilyRankBuilder().buildRankByYy(new HashSet<>(memberIds));
        Map<String, MemberInfo> memberInfoMap = Maps.newHashMap();
        for (Map.Entry<String, FamilyRoleItem> entry : itemMap.entrySet()) {
            FamilyRoleItem memberItemInfo = entry.getValue();
            MemberInfo memberInfo = new MemberInfo();
            memberInfo.setName(memberItemInfo.getName());
            memberInfo.setLogo(memberItemInfo.getAvatarInfo());
            memberInfoMap.put(entry.getKey(), memberInfo);
        }

        return ImmutableMap.of(builderKey(type, roleType), memberInfoMap);
    }
}
