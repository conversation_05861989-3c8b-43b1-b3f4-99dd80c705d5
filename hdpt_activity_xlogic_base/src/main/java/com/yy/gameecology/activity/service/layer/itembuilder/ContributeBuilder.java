package com.yy.gameecology.activity.service.layer.itembuilder;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.common.consts.ActorInfoStatus;
import com.yy.gameecology.common.consts.LayerItemTypeKey;
import com.yy.gameecology.common.consts.RankMapType;
import com.yy.gameecology.common.db.model.gameecology.ActLayerViewDefine;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.hdztranking.ActorInfoItem;
import com.yy.thrift.hdztranking.ActorQueryItem;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * desc:贡献榜
 * <p>
 * 先实现主持对语音房的贡献，后续有其他需求，再完善代码
 *
 * <AUTHOR>
 * @date 2023-10-26 16:26
 **/
@Component
public class ContributeBuilder extends ActLayerInfoService implements LayerItemBuilder {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    @Override
    public Set<String> getItemKeys() {
        return Collections.singleton(LayerItemTypeKey.CONTRIBUTE);
    }

    @Override
    public List<String> getMemberIds(ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, OnlineChannelInfo onlineChannel) {

        String subChannelMemberId = onlineChannel.getSid() + "_" + onlineChannel.getSsid();
        return Lists.newArrayList(subChannelMemberId);
    }

    @Override
    public List<LayerMemberItem> build(Date now, ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, List<String> memberIds, Map<String, Object> ext) {
        if (memberIds == null) {
            return Lists.newArrayList();
        }
        long actId = actInfo.getActId();
        String subChannel = memberIds.get(0);
        String[] sidSsid = subChannel.split("_");
        long sid = Convert.toLong(sidSsid[0]);
        long ssid = Convert.toLong(sidSsid[1]);
        RoomInfo roomInfo = commonService.getRoomInfoBySsid(ssid);
        OnlineChannelInfo onlineChannelInfo = onlineChannelService.get(sid, ssid);
        if (onlineChannelInfo == null) {
            return Lists.newArrayList();
        }

        //解析主榜成员id
        String mainRankMember = null;
        int mainRankRoleType = viewDefine.resolveMainRankRoleType();
        if (RoleType.ROOM.getValue() == mainRankRoleType) {
            mainRankMember = Convert.toString(roomInfo.getRoomId());
        } else if (RoleType.FAMILY.getValue() == mainRankRoleType) {
            mainRankMember = Convert.toString(roomInfo.getFamilyId());
        } else if (RoleType.GUILD.getValue() == mainRankRoleType) {
            mainRankMember = sidSsid[0];
        } else if (RoleType.HALL.getValue() == mainRankRoleType) {
            mainRankMember = subChannel;
        }

        //被贡献对象为空
        if (StringUtil.isBlank(mainRankMember)) {
            return Lists.newArrayList();
        }

        //判断被贡献对象有无被淘汰，如果淘汰了直接返回空
        ActLayerViewDefine transViewDefine = new ActLayerViewDefine();
        transViewDefine.setActId(viewDefine.getActId());
        transViewDefine.setItemTypeKey(viewDefine.getItemTypeKey());
        transViewDefine.setRoleType(mainRankRoleType);
        transViewDefine.setExtJson(viewDefine.getExtJson());
        transViewDefine.setStatus(viewDefine.getStatus());
        transViewDefine.setRoleId(viewDefine.getRoleId());
        transViewDefine.setBusiIds(viewDefine.getBusiIds());
        transViewDefine.setStartShowTime(viewDefine.getStartShowTime());
        transViewDefine.setEndShowTime(viewDefine.getEndShowTime());

        ActorQueryItem actorQueryItem = genQueryActorInfoPara(actInfo, transViewDefine, busiId, mainRankMember, RankMapType.NORMAL, now);
        //主榜角色被淘汰了，角色切换了，对应角色没配置主赛场，actorQueryItem可以为null
        ActorInfoItem actorItem = null;
        if (actorQueryItem != null) {
            actorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, actorQueryItem);
        }

        //读取贡献成员信息
        List<LayerMemberItem> anchorInfos = genAnchorInfos(viewDefine, actInfo, busiId, viewDefine.getRoleType(), onlineChannelInfo.getEffectAnchorId(), now, 0L, mainRankMember);


        //TODO 定制代码 top title 替换房间名称
        if (CollectionUtils.isNotEmpty(anchorInfos)) {
            for (LayerMemberItem layerMemberItem : anchorInfos) {
                if (actorItem == null || ActorInfoStatus.ELIMINATE == actorItem.getStatus()) {
                    //给前端隐藏阶段榜
                    layerMemberItem.getExt().put("mainMemberStatus", ActorInfoStatus.ELIMINATE);
                }
                if (StringUtil.isNotBlank(layerMemberItem.getLastPhaseTopTitle())) {
                    String roomName = StringUtil.abbr(roomInfo.getTitle(), 7);
                    layerMemberItem.setLastPhaseTopTitle(layerMemberItem.getLastPhaseTopTitle().replace("{$roomName}", roomName));
                    String familyName = StringUtil.abbr(roomInfo.getFamilyName(), 7);
                    layerMemberItem.setLastPhaseTopTitle(layerMemberItem.getLastPhaseTopTitle().replace("{$familyName}", familyName));
                }
            }
        }
        return anchorInfos;

    }


    @Override
    public void fillLayerBroadcastInfo(ActLayerViewDefine viewDefine, LayerBroadcastInfo target, List<LayerMemberItem> source, List<String> memberIds, Map<String, Object> ext) {
        if (source == null) {
            return;
        }
        //TODO 放主播，如果后续有冲突，放扩展字段
        target.setAnchorInfo(source);
    }
}
