package com.yy.gameecology.activity.service.layer.itembuilder;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.common.consts.ActorInfoStatus;
import com.yy.gameecology.common.consts.LayerItemTypeKey;
import com.yy.gameecology.common.consts.LayerViewStatus;
import com.yy.gameecology.common.db.model.gameecology.ActLayerViewDefine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@Component
public class CpLatestBuilder extends ActLayerInfoService implements LayerItemBuilder{
    @Override
    public Set<String> getItemKeys() {
        return Set.of(LayerItemTypeKey.CP_LATEST);
    }

    @Override
    public List<String> getMemberIds(ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, OnlineChannelInfo onlineChannel) {
        if (CollectionUtils.isEmpty(onlineChannel.getEffectAnchorId())) {
            return null;
        }
        return onlineChannel.getEffectAnchorId().stream().map(x -> x + "").collect(Collectors.toList());
    }

    @Override
    public List<LayerMemberItem> build(Date now, ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, List<String> memberIds, Map<String, Object> ext) {
        //主播+用户列表
        List<LayerMemberItem> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(memberIds)) {
            return result;
        }

        for (int i = 0; i < memberIds.size(); i++) {
            String memberId = memberIds.get(i);
            LayerMemberItem item = new LayerMemberItem();
            item.setMemberId(memberId);
            item.setItemType(viewDefine.getItemTypeKey());
            item.setState(ActorInfoStatus.NORMAL);
            item.setViewStatus(LayerViewStatus.NORMAL_100);
            item.setSort(i);
            Map<String, Object> itemExtInfo = actLayerInfoExtService.extendLayerMemberItem(actInfo.getActId(), item);
            if (MapUtils.isNotEmpty(itemExtInfo)) {
                item.getExt().putAll(itemExtInfo);
            }
            result.add(item);
        }

        return result;
    }



    @Override
    public void fillLayerBroadcastInfo(ActLayerViewDefine viewDefine, LayerBroadcastInfo target, List<LayerMemberItem> source, List<String> memberIds, Map<String, Object> ext) {
        if (CollectionUtils.isEmpty(source)) {
            return;
        }
        target.getExtMemberItem().addAll(source);
    }


}
