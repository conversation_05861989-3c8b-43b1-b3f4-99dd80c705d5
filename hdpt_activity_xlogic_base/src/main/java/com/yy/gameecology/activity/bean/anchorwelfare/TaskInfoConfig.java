package com.yy.gameecology.activity.bean.anchorwelfare;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

@Data
public class TaskInfoConfig {
    @ComponentAttrField(labelText = "任务类型", remark = "1-普通签到 2-频道停留 3-上传相册 4-发布动态 5-上传语音条")
    private int taskType;

    @ComponentAttrField(labelText = "任务名称")
    private String taskName;

    @ComponentAttrField(labelText = "新用户奖励", remark = "单位厘")
    private int newUserAward;

    @ComponentAttrField(labelText = "老用户奖励", remark = "单位厘")
    private int oldUserAward;

    @ComponentAttrField(labelText = "过任务数值")
    private int passValue;

}
