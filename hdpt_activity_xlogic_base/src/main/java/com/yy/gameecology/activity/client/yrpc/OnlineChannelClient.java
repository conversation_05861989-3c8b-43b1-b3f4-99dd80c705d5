package com.yy.gameecology.activity.client.yrpc;

import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

@Component
public class OnlineChannelClient {

    @Reference(protocol = "yrpc", owner = "${online.channel.s2s}", registry = {"yrpc-reg"}, lazy = true,retries = 2, cluster = "failover")
    private OnlineChannelProvider proxy;

    public OnlineChannelProvider getProxy() {
        return proxy;
    }
}
