package com.yy.gameecology.activity.client.yrpc;
import com.yy.protocol.pb.currency.CurrencyProto;
import org.apache.dubbo.common.annotation.Yrpc;

public interface CurrencyProvider {
    @Yrpc(functionName="getMultiCurrency")
    public CurrencyProto.GetMultiCurrencyResult getMultiCurrency(CurrencyProto.GetMultiCurrencyRequest req);

    @Yrpc(functionName="consumeProduct")
    public CurrencyProto.ConsumeProductResult consumeProduct(CurrencyProto.ConsumeProductRequest req);

    @Yrpc(functionName="issueCurrency")
    public CurrencyProto.IssueMultiCurrencyResult issueCurrency(CurrencyProto.IssueMultiCurrencyRequest req);

    @Yrpc(functionName="rollback")
    public CurrencyProto.ConsumeProductResult rollback(CurrencyProto.RollbackRequest req);

    @Yrpc(functionName = "getCurrencyStat")
    CurrencyProto.CurrencyStatResponse getCurrencyStat(CurrencyProto.CurrencyStatRequest req);
}
