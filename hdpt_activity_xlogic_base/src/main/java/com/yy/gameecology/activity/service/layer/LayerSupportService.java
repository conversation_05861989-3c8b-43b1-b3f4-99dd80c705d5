package com.yy.gameecology.activity.service.layer;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.UserTaskService;
import com.yy.gameecology.common.consts.ActorInfoStatus;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.LayerViewStatus;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.component.attr.ActLayerConfigComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.LayerTimeViewStatusConfig;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.bridge.ICommand;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-04-16 9:48
 **/
@Service
public class LayerSupportService implements LayerSupport {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    private UserTaskService userTaskService;

    @Autowired
    private LayerConfigService layerConfigService;

    @Autowired
    private CommonService commonService;



    /**
     * 活动id为0代表 公共实现，先执行这个公共实现，再执行有特定活动id的实现
     */
    @Override
    public long getActId() {
        return 0;
    }

    @Override
    public LayerBroadcastInfo customBroadcastInTheEnd(LayerBroadcastInfo source) {
        //设置视图状态
        setViewStatus(source);
        return source;
    }


    @Override
    public Map<String, Object> buildItemMemberExtInfo(long actId, LayerMemberItem layerMemberItem, Map<String, Object> ext) {
        Map<String, Object> outPut = null;
        //---这里可放一些通用的扩展信息

        //静态配置输出
        if (actId != 0 && layerMemberItem != null && layerMemberItem.getCurRankId() != null && layerMemberItem.getCurPhaseId() != null) {
            outPut = Maps.newHashMap();
            Long rankId = layerMemberItem.getCurRankId();
            Long phaseId = layerMemberItem.getCurPhaseId();
            ActLayerConfigComponentAttr layerAttr = layerConfigService.getLayerAttrConfig(actId);
            if (layerAttr.getOutPutTabStaticConfig().containsKey(rankId)) {
                Map<Long, Map<String, String>> phaseConfig = layerAttr.getOutPutTabStaticConfig().get(rankId);
                if (phaseConfig.containsKey(phaseId)) {
                    outPut.putAll(phaseConfig.get(phaseId));
                }
            }
        }

        return outPut;
    }


    /**
     * 设置视图状态
     */
    private void setViewStatus(LayerBroadcastInfo source) {
        int globalStatus = getGlobalViewStatus(source);
        //频道
        int channelViewStatus = getViewStatus(source, source.getChannelInfo(), globalStatus);
        setViewStateValue(source.getChannelInfo(), channelViewStatus);

        //子频道
        int subChannelViewStatus = getViewStatus(source, source.getSubChannelInfo(), globalStatus);
        setViewStateValue(source.getSubChannelInfo(), subChannelViewStatus);

        //主播
        if (CollectionUtils.isNotEmpty(source.getAnchorInfo())) {
            for (LayerMemberItem memberItem : source.getAnchorInfo()) {
                int status = getViewStatus(source, memberItem, globalStatus);
                setViewStateValue(memberItem, status);
                //如果不用展示的主播，uid设置未0，龙龙好控制不展示主播列表头像 2021-09-19
                if (LayerViewStatus.containsNotShowStatus(status) && memberItem != null) {
                    memberItem.setMemberId("0");
                }
            }
        }

        //陪玩团
        if (CollectionUtils.isNotEmpty(source.getTopNGroupInfo())) {
            for (LayerMemberItem memberItem : source.getTopNGroupInfo()) {
                int status = getViewStatus(source, memberItem, globalStatus);
                setViewStateValue(memberItem, status);
            }
        }

        //扩展元素
        if (CollectionUtils.isNotEmpty(source.getExtMemberItem())) {
            for (LayerMemberItem memberItem : source.getExtMemberItem()) {
                int status = getViewStatus(source, memberItem, globalStatus);
                setViewStateValue(memberItem, status);
            }
        }
    }

    private int getGlobalViewStatus(LayerBroadcastInfo source) {
        return existAnyItemInSettle(source) ? LayerViewStatus.SETTLE_900 : 0;
    }

    private boolean canUseGlobalViewStatus(int viewStatus) {
        return true;
    }

    /**
     * 有1个元素在结算中，则全局结算中
     */
    private boolean existAnyItemInSettle(LayerBroadcastInfo source) {
        if (source == null) {
            return false;
        }
        if (source.getChannelInfo() != null && source.getChannelInfo().getSettleStatus() == 1) {
            return true;
        }
        if (source.getSubChannelInfo() != null && source.getSubChannelInfo().getSettleStatus() == 1) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(source.getAnchorInfo()) && source.getAnchorInfo().stream().anyMatch(x -> x.getSettleStatus() == 1)) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(source.getTopNGroupInfo()) && source.getTopNGroupInfo().stream().anyMatch(x -> x.getSettleStatus() == 1)) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(source.getExtMemberItem()) && source.getExtMemberItem().stream().anyMatch(x -> x.getSettleStatus() == 1)) {
            return true;
        }

        return false;

    }

    /**
     * 运算视图状态，先计算非正常状态
     */
    private int getViewStatus(LayerBroadcastInfo source, LayerMemberItem memberItem, int globalViewStatus) {
        if (memberItem == null) {
            return LayerViewStatus.ERROR;
        }

        ActivityInfoVo actInfo = new ActivityInfoVo();
        actInfo.setActId(source.getActId());
        actInfo.setBeginTime(source.getActBeginTime());
        actInfo.setBeginTimeShow(source.getActBeginShowTime());
        actInfo.setEndTime(source.getActEndTime());
        actInfo.setEndTimeShow(source.getActEndShowTime());
        actInfo.setCurrentTime(source.getCurrentTime());
        int viewStatus = getViewStatus(actInfo, memberItem);

        if (globalViewStatus != 0 && canUseGlobalViewStatus(viewStatus)) {
            return globalViewStatus;
        }
        return viewStatus;
    }

    /**
     * 获取界面状态
     */
    public int getViewStatus(ActivityInfoVo source, LayerMemberItem memberItem) {
        int normalViewStatus = getNormalViewStatus(source, memberItem);
        //不同活动，本质是同1种状态，但是客户端展示不一样，在这可以针对活动映射成不同状态
        return customerActState(memberItem.getItemType(), source.getActId(), memberItem.getCurRankId(), memberItem.getCurPhaseId(), normalViewStatus);
    }

    /**
     * 固化界面状态
     */
    private int getNormalViewStatus(ActivityInfoVo source, LayerMemberItem memberItem) {
        long now = source.getCurrentTime();

        if (memberItem == null) {
            return LayerViewStatus.ERROR;
        }

        //抽签中
        final boolean memberState = memberItem.getState() == ActorInfoStatus.NORMAL || memberItem.getState() == ActorInfoStatus.DANGEROUS;
        if (memberItem.isInDrawLots() && memberState) {
            return LayerViewStatus.IN_DOW_LOTS_109;
        }

        //是否结算中
        if (isSettle(memberItem)) {
            return LayerViewStatus.SETTLE_900;
        }

        //整体活动未开始
        if (now < source.getBeginTime()) {
            return LayerViewStatus.NOT_BEGIN_99;
        }

        //全局show top n
        if (memberItem.isShowHonorTitle()) {
            return LayerViewStatus.TOP_106;
        }

        //当前赛道活动未开始
        if (memberItem.getCurPhaseInfo() != null
                && now > memberItem.getCurPhaseInfo().getShowBeginTime()
                && now < memberItem.getCurPhaseInfo().getStartTime()) {
            return LayerViewStatus.NOT_BEGIN_991;
        }

        //交友帽子主持不展示 俊哥确认那边已经没有帽子主持逻辑
/*        if (memberItem.getRoleType() == RoleType.ANCHOR.getValue() ) {
            if (ftsBaseInfoBridgeClient.isHatCompere(Convert.toLong(memberItem.getMemberId(), 0))) {
                return LayerViewStatus.ERROR;
            }
        }*/

        //无主播不展示
        if (StringUtil.ZERO.equals(memberItem.getMemberId())) {
            return LayerViewStatus.ERROR;
        }

        //---活动是否已结束
        //阶段已结束，阶段展示时间未结束,top n 内
        if (inPhaseEndShow(now, memberItem) && inLastPhaseTopN(memberItem)) {
            return LayerViewStatus.TOP_106;
        }


        //阶段已结束，阶段展示时间未结束,top n 外;或者总活动时间已结束，因为有部分赛道可能会提前结束，所以要加上这个判断
        //主播还有主播任务的时候，不展示活动已结束--- 左左需求：这里不能显示活动已结束，因为整个大活动也还没结束，会让人误解的
        if ((inPhaseEndShow(now, memberItem) || now > source.getEndTime())) {
            if (!hasMission(memberItem)) {
                return LayerViewStatus.ACT_END_107;
            } else if (!userTaskService.isCompleteMission(memberItem.getMissions())) {
                return LayerViewStatus.MISSION_102;
            } else {
                return LayerViewStatus.MISSION_COMPLETE_103;
            }
        }


        //总活动已结束
        if (now > source.getEndTimeShow()) {
            return LayerViewStatus.ACT_END_107;
        }


        //以下为时间在赛程内判断
        //已淘汰，或者当前赛道赛程已结束  做任务
        final boolean match1 = (memberItem.getState() == ActorInfoStatus.ELIMINATE || memberItem.getState() == ActorInfoStatus.NOT_IN_PHASE)
                && hasMission(memberItem) && !userTaskService.isCompleteMission(memberItem.getMissions());
        if (match1) {
            return LayerViewStatus.MISSION_102;
        }
        //已淘汰，或者当前赛道赛程已结束 任务完成
        final boolean match2 = (memberItem.getState() == ActorInfoStatus.ELIMINATE || memberItem.getState() == ActorInfoStatus.NOT_IN_PHASE)
                && hasMission(memberItem) && userTaskService.isCompleteMission(memberItem.getMissions());
        if (match2) {
            return LayerViewStatus.MISSION_COMPLETE_103;
        }

        //未签约
        final boolean match3 = !Const.ZEROSTR.equals(memberItem.getMemberId())
                && memberItem.getRoleType() == RoleType.ANCHOR.getValue()
                && memberItem.getSignStatus() != 1;
        if (match3) {
            return LayerViewStatus.NOT_SIGN_105;
        }

        //未上榜
        if (memberItem.getState() == ActorInfoStatus.NOT_IN) {
            return LayerViewStatus.NOT_IN_104;
        }


        //pk
        final boolean pk = (memberItem.getState() == ActorInfoStatus.NORMAL || memberItem.getState() == ActorInfoStatus.DANGEROUS)
                && memberItem.getPkInfo() != null && CollectionUtils.isNotEmpty(memberItem.getPkInfo().getPkItems());
        if (pk) {
            return LayerViewStatus.PK_101;
        }

        if (memberItem.getState() == ActorInfoStatus.NORMAL) {
            int viewStatus = memberItem.getViewStatus();
            if (LayerViewStatus.RESERVED_STATUS.containsInteger(viewStatus)) {
                return viewStatus;
            }
        }

        //常规状态
        if (memberItem.getState() == ActorInfoStatus.NORMAL || memberItem.getState() == ActorInfoStatus.DANGEROUS || memberItem.getState() == ActorInfoStatus.NO_VICTORY) {
            return LayerViewStatus.NORMAL_100;
        }

        //已淘汰但是没任务
        if (memberItem.getState() == ActorInfoStatus.ELIMINATE) {
            return LayerViewStatus.ELIMINATE_NOT_MISSION_110;
        }

        return LayerViewStatus.ERROR;
    }

    /**
     * 自定义状态映射，
     */
    private int customerActState(String itemType, long actId, Long rankId, Long phaseId, int oldState) {

        ActLayerConfigComponentAttr config = layerConfigService.getLayerAttrConfig(actId);

        //按时间自定义状态
        if (config.getOverallTimeState().containsKey(itemType)) {
            List<LayerTimeViewStatusConfig> viewStatusConfigs = config.getOverallTimeState().get(itemType);
            long now = commonService.getNow(actId).getTime();
            Optional<LayerTimeViewStatusConfig> configOptional =
                    viewStatusConfigs.stream().filter(p -> now >= p.getBeginTime() && now <= p.getEndTime()).findFirst();
            if (configOptional.isPresent()) {
                oldState = configOptional.get().getViewStatus();
            }
        }


        //优先级 榜单>tab+时间>tab>全局

        //榜单级别映射
        if (rankId != null && phaseId != null) {

            if (config.getCustomerRankConvertState().containsKey(rankId)
                    && config.getCustomerRankConvertState().get(rankId).containsKey(phaseId)
                    && config.getCustomerRankConvertState().get(rankId).get(phaseId).containsKey(oldState)) {
                return config.getCustomerRankConvertState().get(rankId).get(phaseId).get(oldState);
            }
        }

        //tab+时间 级别映射
        if (config.getCustomerTabTimeConvertState().containsKey(itemType)
                && config.getCustomerTabTimeConvertState().get(itemType).containsKey(oldState)) {
            List<LayerTimeViewStatusConfig> viewStatusConfigs = config.getCustomerTabTimeConvertState().get(itemType).get(oldState);
            long now = commonService.getNow(actId).getTime();
            Optional<LayerTimeViewStatusConfig> configOptional =
                    viewStatusConfigs.stream().filter(p -> now >= p.getBeginTime() && now <= p.getEndTime()).findFirst();
            if (configOptional.isPresent()) {
                return configOptional.get().getViewStatus();
            }
        }

        //tab级别映射
        if (config.getCustomerTabConvertState().containsKey(itemType)
                && config.getCustomerTabConvertState().get(itemType).containsKey(oldState)) {
            return config.getCustomerTabConvertState().get(itemType).get(oldState);
        }


        //全局映射
        if (config.getCustomerOverallConvertState().containsKey(oldState)) {
            return config.getCustomerOverallConvertState().get(oldState);
        }



        return oldState;
    }

    /**
     * 是否结算中
     */
    private boolean isSettle( LayerMemberItem memberItem) {
        return memberItem != null && (memberItem.getState() == 500 || memberItem.getSettleStatus() == 1);
    }


    /**
     * 是否在活动结束后展示的期间
     */
    private boolean inPhaseEndShow(long now, LayerMemberItem memberItem) {
        return memberItem.getCurPhaseInfo() != null
                && now > memberItem.getCurPhaseInfo().getEndTime()
                && now < memberItem.getCurPhaseInfo().getShowEndTime();
    }

    /**
     * 是否在活动结束后top n 展示范围内
     */
    private boolean inLastPhaseTopN(LayerMemberItem memberItem) {
        return memberItem.getLastPhaseRank() <= memberItem.getLastPhaseTopN() && memberItem.getLastPhaseTopN() > 0 && memberItem.getLastPhaseRank() > 0;
    }

    /**
     * 是否有任务
     */
    private boolean hasMission(LayerMemberItem memberItem) {
        return CollectionUtils.isNotEmpty(memberItem.getMissions());
    }

    private void setViewStateValue(LayerMemberItem memberItem, int status) {
        if (memberItem != null) {
            memberItem.setViewStatus(status);
        }
    }

}
