package com.yy.gameecology.activity.client.http;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.xpush.*;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.IdGenerator;
import com.yy.gameecology.common.utils.TicketUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;

@Slf4j
@Service
public class XPushServiceHttpClient {

    @Value("${xpush.batch-push-by-uid.uri:https://push-openapi-test.yy.com/push/batchPushByUid}")
    private String pushUri;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 中台push接口
     * @param appId 要去申请，对应在配置表中配置appSecret
     * @param pushId 特定的推送需要约定，其余传null
     * @param title 标题
     * @param content 文案
     * @param link 跳转链接
     * @param icon 图标
     * @param platform 3：安卓，4：iOS
     * @param uids 用户uid
     * @return rsp.code == 0表示成功
     */
    public PushRsp push(String appId, Long pushId, String title, String content, String link, String icon, int[] platform, String... uids) {
        if (null == uids || uids.length < 1) {
            return new PushRsp().error(500, "uids cannot be empty!");
        }

        String appSecret = Const.GEPM.getParamValue(String.format("xpush_appSecret_%s", appId), StringUtils.EMPTY);
        if (StringUtils.isEmpty(appSecret)) {
            return new PushRsp().error(500, "appSecret not config");
        }

        long uid = Long.parseLong(uids[0]);
        long id = IdGenerator.generate(uid);
        if (pushId != null) {
            id = pushId;
        }

        WebPushPayload webPushPayload = new WebPushPayload();
        MulLangField titleField = new MulLangField();
        titleField.setFormat(title);
        MulLangField contentField = new MulLangField();
        contentField.setFormat(content);
        webPushPayload.setTitle(titleField);
        webPushPayload.setContent(contentField);
        webPushPayload.setLink(link);
        webPushPayload.setImg(icon);
        Model model = new Model();
        model.setAction(link);
        model.setIcon(icon);
        model.setTitle(title);
        model.setDesc(content);
        model.setPushId(id);
        webPushPayload.setModel(model);
        BatchData data = new BatchData(uids, webPushPayload);
        data.setPayload(webPushPayload);
        PushReq req = PushReq.builder()
                .appid(appId)
                .pushId(String.valueOf(id))
                .traceId(String.valueOf(id))
                .groupId(String.valueOf(id))
                .pushTime(System.currentTimeMillis())
                .msgType(MsgType.PAYLOAD.getValue())
                .uidToHdid(1)
                .data(new BatchData[] {data})
                .platforms(platform)
                .build();

        String templateUri = pushUri + "?sign={sign}";
        String body = JSON.toJSONString(req);
        String sign;
        try {
            sign = TicketUtils.generateSignature(body, appSecret);
        } catch (Exception e) {
            return new PushRsp().error(500, "sign generate fail" + e.getMessage());
        }
        RequestEntity<String> entity = RequestEntity.post(templateUri, sign)
                .contentType(MediaType.APPLICATION_JSON)
                .body(body);
        ResponseEntity<PushRsp> rsp = restTemplate.exchange(entity, PushRsp.class);
        log.info("push with appId:{}, pushId:{} uids:{},body:{}, httpCode:{}, rsp:{}", appId, pushId, Arrays.toString(uids), body, rsp.getStatusCode(), rsp.getBody());
        return rsp.getStatusCode() == HttpStatus.OK ? rsp.getBody() : PushRsp.FAIL;
    }
}
