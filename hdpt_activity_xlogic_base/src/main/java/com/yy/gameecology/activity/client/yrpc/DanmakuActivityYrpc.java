package com.yy.gameecology.activity.client.yrpc;

import com.yy.protocol.pb.danmu.Danmu;
import org.apache.dubbo.common.annotation.Yrpc;

public interface DanmakuActivityYrpc {

    @Yrpc(functionName="queryGameStatus")
    Danmu.QueryGameStatusResp queryGameStatus(Danmu.QueryGameStatusReq req);


    @Yrpc(functionName="queryAllGameChannel")
    Danmu.QueryAllGameChannelResp queryAllGameChannel(Danmu.QueryAllGameChannelReq req);

}
