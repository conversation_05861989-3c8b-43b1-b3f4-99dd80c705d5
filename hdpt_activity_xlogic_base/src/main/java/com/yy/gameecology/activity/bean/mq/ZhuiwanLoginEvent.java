package com.yy.gameecology.activity.bean.mq;

import lombok.Data;

import java.util.Date;

@Data
public class ZhuiwanLoginEvent {

    // {"app":"yomi","clientType":2,"firstLogin":false,
    // "hdid":"649b25045682b5c1f16d6f9b69371f3098a1da19","seq":"user_login_2740400470_1682413907",
    // "timestamp":1682413907,"uid":2740400470}
    public Date eventTime() {
        return new Date(timestamp * 1000);
    }

    private String seq;

    /**
     * 时间: 秒
     */
    private long timestamp;

    private long uid;

    private int clientType;

    private String app;
    private boolean firstLogin;
    private String hdid;

    private boolean riskPass;

    private String riskTips;
}
