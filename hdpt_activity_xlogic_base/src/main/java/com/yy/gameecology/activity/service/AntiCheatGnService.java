package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSONObject;
import com.yy.gameecology.common.client.AntiCheatGnThriftClient;
import com.yy.gameecology.common.utils.Clock;
import com.yy.thrift.ysec.anti_cheat_gn.Request;
import com.yy.thrift.ysec.anti_cheat_gn.RequestItem;
import com.yy.thrift.ysec.anti_cheat_gn.Response;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;

@Service
public class AntiCheatGnService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Value("${ysec.join-team.token:rP@kq4XnUg}")
    private String joinTeamToken;

    @Value("${ysec.luky.token:rP@kq4XnUg}")
    private String lucyToken;

    @Value("${ysec.exchange.token:TBx*1,xUU}")
    private String exchangeToken;

    @Autowired
    private AntiCheatGnThriftClient antiCheatGnThriftClient;

    /**
     * 功能说明：2021 520 活动 - 用户app端参团风控检查 - added by guoliping/2021-04-28
     * @return true：没有风险， false：有风险
     */
    public boolean check2021520ActJoinTeam(long uid, long inviteeUid, String ip, String device) {
        Clock clock = new Clock();
        String appId = "520_activity";
        String actionType = "joinTeam";

        // 1.组装扩展字段
        JSONObject json = new JSONObject(5);
        json.put("inviteeUid", String.valueOf(inviteeUid));
        json.put("actionType", actionType);
        json.put("time", String.valueOf(System.currentTimeMillis()));
        String extension = json.toJSONString();

        // 2.构造请求项
        RequestItem requestItem = new RequestItem();
        requestItem.setUid(String.valueOf(uid));
        requestItem.setIp(ip);
        requestItem.setDevice(device);
        requestItem.setPhone(StringUtils.EMPTY);
        requestItem.setEmail(StringUtils.EMPTY);
        requestItem.setAssocAcctId(StringUtils.EMPTY);
        requestItem.setExtension(extension);

        // 3.构造请求对象
        Request request = new Request();
        request.setAppId(appId);
        request.setToken(joinTeamToken);
        request.setDimension(0);
        request.setItems(Collections.singletonList(requestItem));

        try {
            Response response = antiCheatGnThriftClient.getProxy().riskLevelQueryGn(request);
            log.info("check2021520ActJoinTeam done@request:{}, response:{} {}", request, response, clock.tag());
            if (response != null && response.getRescode() == 0) {
                // 约定风控分小于80的检查通过返回true， 大于80的表示有风险返回false
                return response.getItems().get(0).getRiskLevel() <= 80;
            }
        } catch (Exception e) {
            log.error("check2021520ActJoinTeam exception@request:{}, err:{} {}", request, e.getMessage(), clock.tag(), e);
        }

        // 自身异常了，假设风控没问题
        return true;
    }

    /**
     * 功能说明：2021 520 活动 - 用户完成当日所有任务后抽奖 - added by guoliping/2021-04-28
     * @return true：没有风险， false：有风险
     */
    public boolean check2021520ActLuky(long uid, String ip, String device) {
        Clock clock = new Clock();
        String appId = "520_activity";
        String actionType = "luky";

        // 1.组装扩展字段
        JSONObject json = new JSONObject(5);
        json.put("actionType", actionType);
        json.put("time", String.valueOf(System.currentTimeMillis()));
        String extension = json.toJSONString();

        // 2.构造请求项
        RequestItem requestItem = new RequestItem();
        requestItem.setUid(String.valueOf(uid));
        requestItem.setIp(ip);
        requestItem.setDevice(device);
        requestItem.setPhone(StringUtils.EMPTY);
        requestItem.setEmail(StringUtils.EMPTY);
        requestItem.setAssocAcctId(StringUtils.EMPTY);
        requestItem.setExtension(extension);

        // 3.构造请求对象
        Request request = new Request();
        request.setAppId(appId);
        request.setToken(lucyToken);
        request.setDimension(0);
        request.setItems(Collections.singletonList(requestItem));

        try {
            Response response = antiCheatGnThriftClient.getProxy().riskLevelQueryGn(request);
            log.info("check2021520ActLuky done@request:{}, response:{} {}", request, response, clock.tag());
            if (response != null && response.getRescode() == 0) {
                // 约定风控分小于80的检查通过返回true， 大于80的表示有风险返回false
                return response.getItems().get(0).getRiskLevel() <= 80;
            }
        } catch (Exception e) {
            log.error("check2021520ActLuky exception@request:{}, err:{} {}", request, e.getMessage(), clock.tag(), e);
        }

        // 自身异常了，假设风控没问题
        return true;
    }


    public boolean check202309ActExchange(long uid, String ip) {
        Clock clock = new Clock();
        String appId = "yy_recharge_redeem";
        String actionType = "exhange";

        // 1.组装扩展字段
        JSONObject json = new JSONObject(4);
        json.put("actionType", actionType);
        json.put("time", String.valueOf(System.currentTimeMillis()));
        String extension = json.toJSONString();

        // 2.构造请求项
        RequestItem requestItem = new RequestItem();
        requestItem.setUid(String.valueOf(uid));
        requestItem.setIp(ip);
        requestItem.setDevice(StringUtils.EMPTY);
        requestItem.setPhone(StringUtils.EMPTY);
        requestItem.setEmail(StringUtils.EMPTY);
        requestItem.setAssocAcctId(StringUtils.EMPTY);
        requestItem.setExtension(extension);

        // 3.构造请求对象
        Request request = new Request();
        request.setAppId(appId);
        request.setToken(exchangeToken);
        request.setDimension(0);
        request.setItems(Collections.singletonList(requestItem));

        try {
            Response response = antiCheatGnThriftClient.getProxy().riskLevelQueryGn(request);
            log.info("check202309ActExchange done@request:{}, response:{} {}", request, response, clock.tag());
            if (response != null && response.getRescode() == 0) {
                // 约定风控分小于80的检查通过返回true， 大于80的表示有风险返回false
                return response.getItems().get(0).getRiskLevel() <= 80;
            }
        } catch (Exception e) {
            log.error("check202309ActExchange exception@request:{}, err:{} {}", request, e.getMessage(), clock.tag(), e);
        }

        // 自身异常了，假设风控没问题
        return true;
    }

}
