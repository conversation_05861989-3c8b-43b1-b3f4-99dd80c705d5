package com.yy.gameecology.activity.bean.rankroleinfo;


/**
 * @Author: CXZ
 * @Desciption: 主播信息
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
public class BabyRoleItem extends UserBaseItem implements ContractInfo {

    /**
     * 开播频道
     */
    private Long sid;
    private Long ssid;
    /**
     * 签约频道id
     */
    private Long contractSid;

    /**
     * 签约频道短号
     */
    private Long contractAsid;

    /**
     * 用户是否关注主播
     */
    private Boolean subscribe;

    /**
     * 1-交友 2-约战 3-宝贝
     */
    private long templateType;



    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getSsid() {
        return ssid;
    }

    public void setSsid(Long ssid) {
        this.ssid = ssid;
    }

    public Long getContractSid() {
        return contractSid;
    }

    public void setContractSid(Long contractSid) {
        this.contractSid = contractSid;
    }

    public Long getContractAsid() {
        return contractAsid;
    }

    public void setContractAsid(Long contractAsid) {
        this.contractAsid = contractAsid;
    }

    public Boolean getSubscribe() {
        return subscribe;
    }

    public void setSubscribe(Boolean subscribe) {
        this.subscribe = subscribe;
    }

    public long getTemplateType() {
        return templateType;
    }

    public void setTemplateType(long templateType) {
        this.templateType = templateType;
    }


    @Override
    public Long getConSid() {
        return contractSid;
    }

    @Override
    public Long getConAsid() {
        return contractAsid;
    }
}
