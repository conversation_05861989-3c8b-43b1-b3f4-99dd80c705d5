package com.yy.gameecology.activity.bean.xpush;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName
 * @description push消息结构
 * @date 2019/8/14 11:01
 */
@Data
@Builder
public class PushReq {
    /**
     * 请求唯一ID
     */
    private String traceId;
    /**
     * 中台appid
     */
    private String appid;
    /**
     * PushPlatform，3：安卓，4：iOS
     */
    private int[] platforms;
    /**
     * 安卓版本配置
     */
    private AppVersion androidVersion;
    /**
     * iOS版本配置
     */
    private AppVersion iosVersion;
    /**
     * 消息类型，同通道定义，200：透传，401：普通消息
     */
    private int msgType;
    /**
     * 推送ID，非71开头（71已被占用）
     */
    private String pushId;
    /**
     * 分组ID，不能超过16个字节
     */
    private String groupId;
    /**
     * 大区
     */
    private String area;
    /**
     * 国家s
     */
    private String country;
    /**
     * 用户数据
     */
    private BatchData[] data;
    /**
     * 推送时间戳，单位：毫秒
     */
    private long pushTime;
    /**
     * 推送过期时间戳，用户超时不连推送服务器，放弃推送，单位：毫秒
     */
    private long expiredTime;
    /**
     * push分类
     */
    private String catg;
    /**
     * 1-单语言  2-多语言
     */
    private int langCount;
    /**
     * 推送样式 0-默认 1-自定义
     */
    private int pushStyle;

    /**
     * 是否需要转hdid发送, 0：否，1：是, 默认0
     */
    private int uidToHdid = 1;

    public PushReq() {
        this.groupId = String.valueOf(System.currentTimeMillis() / 1000);
        this.traceId = this.groupId;
    }

    public PushReq(final String traceId, final String appid, final int[] platforms, final AppVersion androidVersion, final AppVersion iosVersion, final int msgType, final String pushId, final String groupId, final String area, final String country, final BatchData[] data, final long pushTime, final long expiredTime, final String catg, final int langCount, final int pushStyle, final int uidToHdid) {
        this.traceId = traceId;
        this.appid = appid;
        this.platforms = platforms;
        this.androidVersion = androidVersion;
        this.iosVersion = iosVersion;
        this.msgType = msgType;
        this.pushId = pushId;
        this.groupId = groupId;
        this.area = area;
        this.country = country;
        this.data = data;
        this.pushTime = pushTime;
        this.expiredTime = expiredTime;
        this.catg = catg;
        this.langCount = langCount;
        this.pushStyle = pushStyle;
        this.uidToHdid = uidToHdid;
    }
}
