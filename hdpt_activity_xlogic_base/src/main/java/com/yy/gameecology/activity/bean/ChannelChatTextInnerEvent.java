package com.yy.gameecology.activity.bean;
import lombok.Data;
@Data
public class ChannelChatTextInnerEvent {

    private long topsid;

    private long subsid;

    private long uid;

    private String chat;

    private long timestamp;

    private String seq;

    private  ChannelChatTextInnerEvent() {

    }

    public ChannelChatTextInnerEvent(long topsid, long subsid, long uid, String chat, long timestamp, String seq) {
        this.topsid = topsid;
        this.subsid = subsid;
        this.uid = uid;
        this.chat = chat;
        this.timestamp = timestamp;
        this.seq = seq;
    }
}
