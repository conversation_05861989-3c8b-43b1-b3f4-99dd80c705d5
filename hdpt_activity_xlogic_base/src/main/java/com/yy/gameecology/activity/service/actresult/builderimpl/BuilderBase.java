package com.yy.gameecology.activity.service.actresult.builderimpl;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.client.thrift.ZhuiwanRoomInfoClient;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.activity.service.impl.ActPwSupportService;
import com.yy.gameecology.activity.service.impl.ActSkillCardSupportService;
import com.yy.gameecology.common.client.WebdbServiceClient;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.thrift.hdztranking.BusiId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-11-17 16:50
 **/
@Component
public class BuilderBase {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    protected GameecologyDao gameecologyDao;

    @Autowired
    protected ActPwSupportService actPwSupportService;

    @Autowired
    protected CommonService commonService;

    @Autowired
    protected WebdbThriftClient webdbThriftClient;

    @Autowired
    protected WebdbServiceClient webdbServiceClient;

    @Autowired
    protected EnrollmentService enrollmentService;

    @Autowired
    protected OnMicService onMicService;

    @Autowired
    protected HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    protected ActInfoService actInfoService;
    @Autowired
    protected ActSkillCardSupportService actSkillCardSupportService;
    @Autowired
    protected ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;


    private static final Map<Integer, Integer> TYPE_2_BUSI_ID_MAP = Maps.newHashMap();

    static {
        TYPE_2_BUSI_ID_MAP.put(1, BusiId.MAKE_FRIEND.getValue());
    }


    /**
     * @param type 1-交友 2-约战 3-宝贝 4-互动全品类 5-陪玩
     **/
    protected ActSupportService getActSupportService(int type) {
        Integer busiId = TYPE_2_BUSI_ID_MAP.get(type);
        if (busiId != null) {
            return ActSupportService.getInstance(busiId);
        }

        return null;
    }

    /**
     * type_roleType
     */
    protected String builderKey(long type, long roleType) {
        return type + "_" + roleType;
    }


}
