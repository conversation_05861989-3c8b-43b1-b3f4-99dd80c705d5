package com.yy.gameecology.activity.client.thrift;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.actlayer.PhaseInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.RankPhasePair;
import com.yy.gameecology.activity.service.CacheService;
import com.yy.gameecology.activity.service.HdztRankService;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-17 16:31
 **/
@Component
public class HdztRankingThriftClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Lazy
    @Autowired
    private CacheService cacheService;

    /**
     * 直接调用自己的函数，不会触发注解，直接调用注解的函数要用自己的对象调用
     */
    @Lazy
    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Reference(protocol = "attach_nythrift", owner = "${hdztRanking_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"})
    private HdztRankingService.Iface proxy;

    @Reference(protocol = "attach_nythrift", owner = "${hdztRanking_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"}
            , retries = 2 , cluster = "failover")
    private HdztRankingService.Iface readProxy;

    public HdztRankingService.Iface getProxy() {
        return proxy;
    }

    public HdztRankingService.Iface getReadProxy() {
        return readProxy;
    }

    @Report
    public List<Rank> queryCurrentRanking(long actId, long rankId, long hour, long count, Map<String, String> ext) {
        long phaseId = queryCurrentPhaseId(actId, rankId);
        return queryRanking(actId, rankId, phaseId, "", count, ext);
    }


    /**
     * 查询当前阶段比拼榜
     *
     * @param actId   活动id
     * @param rankId  榜单id
     * @param phaseId 当前阶段id，如果不分阶段，则传0
     * @param dateStr 小时榜、日榜的时候才有值，yyyyMMdd或yyyyMMddHH,空的时候代表不分日期
     * @param count   成员数量
     * @param ext     扩展参数
     *                ext : rank_type_hover_src_id    hover展示的贡献榜的来源id（主播uid或公会id）
     * @return 榜单基础信息缓存
     */
    @Cached(timeToLiveMillis = CacheTimeout.HDZT_RANKING_INFO)
    @Report
    public List<Rank> queryRankingCache(long actId, long rankId, long phaseId, String dateStr, long count, String pointedMember, Map<String, String> ext) {
        return queryRanking(actId, rankId, phaseId, dateStr, count, pointedMember, ext);
    }

    @Report
    public List<Rank> queryRanking(long actId, long rankId, long phaseId, String dateStr, long count, Map<String, String> ext) {
        return queryRanking(actId, rankId, phaseId, dateStr, count, null, ext);
    }

    /**
     * 查询当前阶段比拼榜
     *
     * @param actId         活动id
     * @param rankId        榜单id
     * @param phaseId       当前阶段id，如果不分阶段，则传0
     * @param dateStr       小时榜、日榜的时候才有值，yyyyMMdd或yyyyMMddHH,空的时候代表不分日期
     * @param count         成员数量
     * @param pointedMember 指定要查询的成员， 可空
     * @param ext           扩展参数
     *                      ext : rank_type_hover_src_id    hover展示的贡献榜的来源id（主播uid或公会id）
     * @return 榜单基础信息
     */
    @Report
    public List<Rank> queryRanking(long actId, long rankId, long phaseId, String dateStr, long count, String pointedMember, Map<String, String> ext) {
        QueryRankingRequest request = new QueryRankingRequest();
        request.setActId(actId);
        request.setRankingId(rankId);
        request.setPhaseId(phaseId);
        request.setRankingCount(count);
        request.setDateStr(dateStr);
        request.setPointedMember(pointedMember);
        QuerySingleRankingResult rankingResult = null;

        //屏蔽中台复杂榜单概念，默认是当前比拼的榜单  HdztRankType.CONTEST
        String rankType = getRankQueryType(ext);
        request.setRankType(rankType);

        //贡献榜
        if (HdztRankType.SRC.equals(rankType)) {
            request.setFindSrcMember(ext.get(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID));
        }

        final String rank_sort_by_pkvalue = "rank_sort_by_pkvalue";
        if (ext != null && ext.containsKey(rank_sort_by_pkvalue)) {
            Map<String, String> extData = new HashMap<>(4);
            extData.put(rank_sort_by_pkvalue, "1");
            request.setExtData(extData);
        }

        try {
            rankingResult = getReadProxy().queryRanking(request);
        } catch (Exception e) {
            log.error("queryRanking error,actId:{},rankId:{},phaseId:{},count:{},e:{}", actId, rankId, phaseId, count, e.toString(), e);
        }
        if (rankingResult == null || rankingResult.getCode() != 0) {
            log.error("queryRanking error,actId:{},rankId:{}, phaseId:{},count:{},rankingResult:{}", actId, rankId, phaseId, count, JSON.toJSONString(rankingResult));
            return Lists.newArrayList();
        }

        List<Rank> ranks = rankingResult.getData();
        if (ranks == null) {
            log.error("queryRanking error,actId:{},rankId:{},phaseId:{},count:{},rankingResult:{}", actId, rankId, phaseId, count, JSON.toJSONString(rankingResult));
            ranks = Lists.newArrayList();
        }

        // 最后1个元素放置 指定成员的结果（这个办法很丑，为了和以前兼容，暂时只能这样做了！）， 主动传了 pointedMember 的地方要自己负责做数据分离等处理！！！
        if (HdztRankService.isValidPointedMember(pointedMember)) {
            ranks.add(rankingResult.getPointedRank());
        }

        return ranks;
    }

    @Report
    public Map<String, List<Rank>> queryAllHourRank(long actId, long rankId, long rankingCount) {
        try {
            QueryTimeRankingRequest request = new QueryTimeRankingRequest();
            request.setActId(actId);
            request.setRankingId(rankId);
            request.setRankingCount(rankingCount);

            QueryMulRankingResult rankingResult = getReadProxy().queryTimeRank(request);
            if (rankingResult == null || rankingResult.getCode() != 0) {
                log.error("queryRanking error,actId:{},rankId:{},result:{}", actId, rankId, JSON.toJSONString(rankingResult));
                return Maps.newHashMap();
            }
            return rankingResult.getData().get(rankId + "");
        } catch (Exception e) {
            log.error("queryRanking error,actId:{},rankId:{} ,e:{}", actId, rankId, e.toString(), e);
            return Maps.newHashMap();
        }

    }

    /**
     * 活动配置信息
     */
    @Report
    public ActivityInfoVo queryActivityCacheNoTime(long actId) {
        ActivityInfoVo activityInfo = hdztRankingThriftClient.queryActivityCache(actId);
        return activityInfo == null || !activityInfo.getActId().equals(actId) ? null : activityInfo;
    }

    /**
     * 中控无缓存
     */
    public ActivityInfoVo queryActivityNoCache(long actId) {
        ActivityInfoVo activityInfo = this.queryActivityCache(actId);
        return activityInfo == null || !activityInfo.getActId().equals(actId) ? null : activityInfo;
    }

    /**
     * 查找中台的活动信息-
     * <p>
     * 注意：找不到活动的时候会返回一个活动id为0的实体对象，外部调用要做判断，最好不要调用这个接口，推荐使用 queryActivityCacheNoTime
     * 不直接返回null，是为了不让缓存失效
     *
     * @param actId
     * @return
     */
    @Cached(timeToLiveMillis = 300 * 1000)
    @Report
    public ActivityInfoVo queryActivityCache(long actId) {
        try {
            ActivityInfoRequest request = new ActivityInfoRequest();
            request.setActId(actId);
            ActivityInfoResult result = getReadProxy().queryActInfo(request);
            if (result == null || result.getCode() != 0) {
                log.error("act not exist,actId:{},result:{}", actId, result);
            }
            if (result == null || result.getData() == null) {
                return null;
            }
            ActivityInfoVo activityInfo = new ActivityInfoVo();
            BeanUtils.copyProperties(result.getData(), activityInfo);

            return activityInfo;
        } catch (Exception e) {
            log.error("queryActivityInfo error,actId:{},e:{}", actId, e.toString(), e);
            return null;
        }
    }

    /**
     * 活动配置扩展信息
     */
    @Cached(timeToLiveMillis = 300 * 1000)
    @Report
    public Map<String, String> queryActivityAttr(long actId) {
        try {
            ActivityInfoRequest request = new ActivityInfoRequest();
            request.setActId(actId);
            ActivityAttrResult result = getReadProxy().queryActAttr(request);
            if (result == null || result.getCode() != 0) {
                log.error("act not exist,actId:{},result:{}", actId, result);
            }
            if (result == null || result.getData() == null) {
                return null;
            }

            return result.getData();
        } catch (Exception e) {
            log.error("queryActivityInfo error,actId:{},e:{}", actId, e.toString(), e);
            return null;
        }
    }

    /**
     * 活动技术负责人
     */
    public String queryActTechnical(long actId) {
        Map<String, String> attr = queryActivityAttr(actId);
        if (attr == null) {
            return StringUtil.EMPTY;
        }
        return attr.getOrDefault(HdztActAttrConst.FIELD_TECHNICAL, StringUtil.EMPTY);
    }

    @Report
    public ActivityInfoVo queryActivityInfo(long actId) {

        ActivityInfoVo vo = hdztRankingThriftClient.queryActivityCache(actId);
        if (vo == null || !vo.getActId().equals(actId)) {
            return null;
        }
        //时间不带缓存
        ActivityInfoVo activityInfo = new ActivityInfoVo();
        BeanUtils.copyProperties(vo, activityInfo);
        activityInfo.setCurrentTime(getNow(actId).getTime());
        return activityInfo;
    }

    /**
     * 获取所有有效（status = 1）活动
     */
    @Report
    @Cached(timeToLiveMillis = 300 * 1000)
    public List<ActivityInfoVo> queryEffectActInfos() {
        List<ActivityInfoVo> infos = Lists.newArrayList();
        try {
            ActivityInfosRequest request = new ActivityInfosRequest();
            ActivityInfosResult result = getReadProxy().queryActInfos(request);
            if (result == null || result.getCode() != 0) {
                log.error("queryEffectActInfos error,result:{}", result);
            }
            if (result == null || result.getData() == null) {
                //返回null，避免缓存
                return null;
            }
            for (ActivityInfo activityInfo : result.getData()) {
                //只判断活动状态，如果还要判断活动时间，预加载可能来不及
                if (activityInfo.getStatus() != 1) {
                    continue;
                }
                ActivityInfoVo vo = new ActivityInfoVo();
                BeanUtils.copyProperties(activityInfo, vo);
                infos.add(vo);
            }

            return infos;
        } catch (Exception e) {
            log.error("queryEffectActInfos error,e:{}", e.toString(), e);
            //返回null，避免缓存
            return null;
        }
    }

    public boolean existEffectActInfos() throws TException {
        ActivityInfosRequest request = new ActivityInfosRequest();
        ActivityInfosResult result = getReadProxy().queryActInfos(request);
        if (result == null || result.getCode() != 0) {
            log.error("existEffectActInfos error,result:{}", result);
        }
        if (result == null || result.getData() == null || CollectionUtils.isEmpty(result.getData())) {
            return false;
        }

        //离线状态，活动下线后，不会有状态为1的活动
        List<ActivityInfo> activityInfos = result.getData().stream().filter(p -> Const.ONE == p.getStatus()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(activityInfos)) {
            return false;
        }
        long timeBeginOffset = Const.GEPM.getParamValueToLong(GeParamName.ACT_ACTIVE_BEGIN_OFFSET, DateUtil.SIS_HOUR_MILL_SECONDS);
        long timeEndOffset = Const.GEPM.getParamValueToLong(GeParamName.ACT_ACTIVE_END_OFFSET, DateUtil.ONE_MONTH_MILL_SECONDS);
        for (ActivityInfo activityInfo : activityInfos) {
            long now = getNow(activityInfo.getActId()).getTime();
            //活动正常结束流程，会设置活动状态为归档状态，所以保险起见，为避免活动时间结束，还需要跑一些逻辑，有效期结束时间可以向后扩多1点
            if (now >= activityInfo.getBeginTimeShow() - timeBeginOffset
                    && now <= activityInfo.getEndTimeShow() + timeEndOffset) {
                log.info("activity is active:{}", activityInfo.getActId());
                return true;
            }
        }

        return false;
    }

    /**
     * 查询榜单阶段的mvp
     *
     * @param actId
     * @param rankId
     * @return
     */
    @Report
    @Cached(timeToLiveMillis = 10 * 1000)
    public List<Rank> phaseGroupMvp(long actId, long rankId) {
        Clock clock = new Clock();
        //查询版单的阶段
        RankingInfo rankingInfo = queryRankConfig(actId, rankId);
        if (rankingInfo == null) {
            log.error("phaseGroupMvp error can not find RankingInfo ,actId:{},rankId:{},clock:{}", actId, rankId, clock.tag());
            return Lists.newArrayList();
        }
        clock.tag();
        RankingPhaseInfo currentPhase = rankingInfo.getCurrentPhase();
        if (currentPhase == null) {
            return Lists.newArrayList();
        }
        Long currentPhaseId = currentPhase.getPhaseId();
        List<Long> phaseIds = rankingInfo.getPhasesMap().keySet().stream().sorted(Long::compareTo).collect(Collectors.toList());
        //根据阶段id生成请求数据
        List<QueryRankingRequest> rankingRequests = Lists.newArrayList();
        for (Long phaseId : phaseIds) {
            if (phaseId < currentPhaseId) {
                QueryRankingRequest rankingRequest = new QueryRankingRequest();
                rankingRequest.setActId(actId);
                rankingRequest.setRankingId(rankId);
                rankingRequest.setPhaseId(phaseId);
                rankingRequest.setRankingCount(1);
                rankingRequests.add(rankingRequest);
            } else {
                break;
            }

        }
        if (rankingRequests.isEmpty()) {
            return Lists.newArrayList();
        }
        //批量请求
        List<List<Rank>> rankLists = queryBatchRanking(rankingRequests, Maps.newHashMap());
        clock.tag();
        //组织返回数据
        List<Rank> mvpRanks = Lists.newArrayList();
        for (int i = 0; i < phaseIds.size() && !rankLists.isEmpty(); i++) {
            Long phaseId = phaseIds.get(i);
            if (phaseId < currentPhaseId) {
                List<Rank> ranks = rankLists.get(i);
                if (ranks.isEmpty()) {
                    log.error("phaseGroupMvp error rank is null,actId:{},rankId:{},i = {} ", actId, rankId, i);
                } else {
                    mvpRanks.add(ranks.get(0));
                }

            }
        }

        log.info("phaseGroupMvp info ,actId:{},rankId:{} ,clock:{}", actId, rankId, clock.tag());
        return mvpRanks;
    }

    @Report
    public Map<Long, RankingInfo> queryRankConfig(long actId, List<Long> rankIds) {
        Date currentDate = getNow(actId);
        return hdztRankingThriftClient.queryRankConfig(actId, Sets.newHashSet(rankIds), currentDate, 0L);
    }

    @Report
    public RankingInfo queryRankConfig(long actId, long rankId) {
        Date currentDate = getNow(actId);
        return queryRankConfig(actId, rankId, currentDate, 0L);
    }

    @Report
    public RankingInfo queryRankConfig(long actId, long rankId, long phaseId) {
        Date currentDate = getNow(actId);
        return queryRankConfig(actId, rankId, currentDate, phaseId);
    }

    @Report
    @Cached(timeToLiveMillis = CacheTimeout.HDZT_RANKING_CONFIG)
    public RankingInfo queryRankConfigByCache(long actId, long rankId, long phaseId) {
        Date currentDate = getNow(actId);
        return queryRankConfig(actId, rankId, currentDate, phaseId);
    }

    @Report
    public RankingInfo queryRankConfig(long actId, long rankId, Date currentDate, long phaseId) {
        Map<Long, RankingInfo> rankingInfoMap = hdztRankingThriftClient.queryRankConfig(actId, Sets.newHashSet(rankId), currentDate, phaseId);
        if (!MapUtils.isEmpty(rankingInfoMap)) {
            return rankingInfoMap.get(rankId);
        }
        return null;
    }

    /**
     * @param actId
     * @param rankIds     用set 是为了让缓存key有序
     * @param currentDate
     * @param phaseId
     * @return
     */
    //榜单配置信息
    @Report
    public Map<Long, RankingInfo> queryRankConfig(long actId, Set<Long> rankIds, Date currentDate, long phaseId) {
        RankingConfigRequest request = new RankingConfigRequest();
        request.setActId(actId);

        if (currentDate != null) {
            request.setCurrentDate(currentDate.getTime());
        }
        request.setCurPhaseId(phaseId);
        request.setRankingIds(Lists.newArrayList(rankIds));
        Clock clock = new Clock();
        try {
            RankingConfigResponse response = getReadProxy().queryRankingConfig(request);
            if (response == null || response.getCode() != 0 || response.getData() == null) {
                log.error("queryRankConfig error,actId:{},rankId:{},clock:{},response:{}", actId, rankIds, clock.tag(), JSON.toJSONString(response));
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("queryRankConfig error,actId:{},rankId:{},clock:{},e:{}", actId, rankIds, clock.tag(), e.toString(), e);
            return null;
        }
    }

    //查询榜单id
    @Report
    public Map<String, List<Long>> queryRankId(long actId, List<QueryRankIdItem> items) {
        Clock clock = new Clock();
        try {
            QueryRankIdRequest request = new QueryRankIdRequest();
            request.setActId(actId);
            request.setItems(items);
            QueryRankIdResponse response = getReadProxy().queryRankId(request);
            if (response == null || response.getRankIdMap() == null) {
                log.error("queryRankConfig error,actId:{},request:{},response:{},clock:{}", actId, request, response, clock.tag());
                return Maps.newHashMap();
            }
            return response.getRankIdMap();
        } catch (Exception e) {
            log.error("queryRankId error,actId:{},clock:{},e:{}", actId, clock.tag(), e.toString(), e);
            return Maps.newHashMap();
        }
    }

    @Report
    public Map<String, List<RankPhasePair>> queryRankPhasePair(long actId, List<QueryRankIdItem> items) {
        Clock clock = new Clock();
        Map<String, List<RankPhasePair>> result = Maps.newHashMap();
        try {
            QueryRankIdRequest request = new QueryRankIdRequest();
            request.setActId(actId);
            request.setItems(items);
            QueryRankPhasePairResponse response = getReadProxy().queryRankPhasePair(request);
            if (response == null || response.getRankIdMap() == null) {
                log.error("queryRankPhasePair error,actId:{},req:{},rsp:{},clock:{}", actId, request, response, clock);
                return result;
            }
            Map<String, List<com.yy.thrift.hdztranking.RankPhasePair>> map = response.getRankIdMap();
            for (String member : map.keySet()) {
                List<RankPhasePair> rankPhasePairs = Lists.newArrayList();
                map.get(member).forEach(x -> {
                    RankPhasePair pair = new RankPhasePair();
                    pair.setRankId(x.getRankId());
                    pair.setPhaseId(x.getPhaseId());
                    rankPhasePairs.add(pair);
                });
                result.put(member, rankPhasePairs);
            }
            return result;

        } catch (Exception e) {
            log.error("queryRankPhasePair error,actId:{},clock:{},e:{}", actId, clock.tag(), e.toString(), e);
            return result;
        }
    }


    //榜单配置信息
    @Report
    public Map<Long, RankingInfo> queryRankConfig(long actId, Date currentDate) {
        RankingConfigRequest request = new RankingConfigRequest();
        request.setActId(actId);
        request.setCurrentDate(currentDate.getTime());

        Clock clock = new Clock();
        try {
            RankingConfigResponse response = getReadProxy().queryRankingConfig(request);
            if (response == null || response.getCode() != 0 || response.getData() == null) {
                log.error("queryRankConfig error,actId:{},clock:{},response:{}", actId, clock.tag(), JSON.toJSONString(response));
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("queryRankConfig error,actId:{},rankId:{},clock:{},e:{}", actId, clock.tag(), e.toString(), e);
            return null;
        }
    }


    //阶段配置信息
    @Report
    @Cached(timeToLiveMillis = CacheTimeout.HDZT_RANKING_PHASE_INFOS)
    public List<RankingPhaseInfo> queryRankingPhaseInfo(long actId) {
        QueryRankingPhaseRequest request = new QueryRankingPhaseRequest();
        request.setActId(actId);

        Clock clock = new Clock();
        try {
            QueryRankingPhaseResponse response = getReadProxy().queryRankingPhase(request);
            if (response == null || response.getCode() != 0 || response.getData() == null) {
                log.error("queryRankConfig error,actId:{},clock:{},response:{}", actId, clock.tag(), JSON.toJSONString(response));
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("queryRankConfig error,actId:{},rankId:{},clock:{},e:{}", actId, clock.tag(), e.toString(), e);
            return null;
        }
    }

    //阶段配置信息
    @Report
    @Cached(timeToLiveMillis = CacheTimeout.HDZT_RANKING_PHASE_INFO)
    public PhaseInfo queryRankingPhaseInfo(long actId, long phaseId) {
        List<RankingPhaseInfo> phaseInfos = queryRankingPhaseInfo(actId);
        for (RankingPhaseInfo phaseInfo : phaseInfos) {
            if (phaseId == phaseInfo.getPhaseId()) {
                return PhaseInfo.toPhaseInfo(actId, phaseInfo);
            }
        }
        return null;
    }

    @Report
    @Cached(timeToLiveMillis = CacheTimeout.HDZT_RANKING_PHASE_INFO)
    public Map<Long, PhaseInfo> queryRankingPhaseMap(long actId) {
        //要保证顺序
        Map<Long, PhaseInfo> phaseInfoMap = Maps.newLinkedHashMap();

        List<RankingPhaseInfo> phaseInfos = queryRankingPhaseInfo(actId);
        phaseInfos = phaseInfos.stream().sorted(Comparator.comparing(RankingPhaseInfo::getEndTime)).collect(Collectors.toList());
        for (RankingPhaseInfo phaseInfo : phaseInfos) {
            PhaseInfo phaseInfoRes = new PhaseInfo();
            phaseInfoRes.setActId(actId);
            phaseInfoRes.setPhaseId(phaseInfo.getPhaseId());
            phaseInfoRes.setStartTime(phaseInfo.getBeginTime());
            phaseInfoRes.setEndTime(phaseInfo.getEndTime());
            phaseInfoRes.setShowBeginTime(phaseInfo.getShowBeginTime());
            phaseInfoRes.setShowEndTime(phaseInfo.getShowEndTime());
            phaseInfoRes.setName(phaseInfo.getPhaseName());
            phaseInfoRes.setNameShow(phaseInfo.getPhaseNameShow());
            phaseInfoRes.setRecycleCount(Convert.toInt(phaseInfo.getExtData().getOrDefault("recycleCount", "1")));
            phaseInfoMap.put(phaseInfo.getPhaseId(), phaseInfoRes);
        }
        return phaseInfoMap;
    }


    //查看成员排名信息
    @Report
    public Map<String, ActorInfoItem> queryActorRankingInfoMap(long actId, List<ActorQueryItem> actorStatus) {
        ActorRankingInfoRequest request = new ActorRankingInfoRequest();
        request.setActorStatus(actorStatus);
        request.setActId(actId);
        Clock clock = new Clock();
        try {
            ActorRankingInfoResponse response = getReadProxy().queryActorRankingInfo(request);
            if (response == null || response.getCode() != 0) {
                log.error("queryActorRankingInfoMap error,actId:{},request:{},rsp:{},clock:{}", actId, request, response, clock.tag());
            }
            if (response == null) {
                log.error("queryActorRankingInfoMap error,actId:{},clock:{}", actId, clock.tag());
                return Maps.newHashMap();
            }
            Map<String, ActorInfoItem> result = Maps.newHashMap();
            for (ActorInfoItem item : response.getActorStatus()) {
                result.put(item.getActorId(), item);
            }
            return result;
        } catch (Exception e) {
            log.error("queryActorRankingInfoMap error,actId:{},rankId:{},clock:{},e:{}", actId, clock.tag(), e.toString(), e);
            return Maps.newHashMap();
        }
    }

    //查看成员排名信息
    @Report
    public List<ActorInfoItem> queryActorRankingInfo(long actId, List<ActorQueryItem> actorStatus) {
        ActorRankingInfoRequest request = new ActorRankingInfoRequest();
        request.setActorStatus(actorStatus);
        request.setActId(actId);
        Clock clock = new Clock();
        try {
            ActorRankingInfoResponse response = getReadProxy().queryActorRankingInfo(request);
            if (response == null || response.getCode() != 0) {
                log.error("queryActorRankingInfo error,actId:{},request:{},rsp:{},clock:{}", actId, request, response, clock.tag());
            }
            if (response == null) {
                log.error("queryActorRankingInfo error,actId:{},clock:{}", actId, clock.tag());
                return Lists.newArrayList();
            }
            return response.getActorStatus();

        } catch (Exception e) {
            log.error("queryActorRankingInfo error,actId:{},rankId:{},clock:{},e:{}", actId, clock.tag(), e.toString(), e);
            return Lists.newArrayList();
        }
    }

    @Report
    public ActorInfoItem queryActorRankingInfo(long actId, ActorQueryItem actorStatus) {
        List<ActorQueryItem> para = Lists.newArrayList();
        para.add(actorStatus);
        List<ActorInfoItem> res = queryActorRankingInfo(actId, para);
        if (CollectionUtils.isEmpty(res)) {
            return null;
        }
        return res.get(0);
    }

    @Report
    public RankingPhaseInfo queryCurrentPhase(long actId, long rankId) {
        RankingInfo rankingInfo = queryRankConfig(actId, rankId);
        return rankingInfo.getCurrentPhase();
    }

    @Report
    public long queryCurrentPhaseId(long actId, long rankId) {
        RankingPhaseInfo rankingPhaseInfo = queryCurrentPhase(actId, rankId);
        return rankingPhaseInfo.getPhaseId();
    }

    /**
     * @param actId  活动ID
     * @param rankId 榜单ID
     * @param time   时间,单位:毫秒
     * @return
     */
    @Report
    public RankingPhaseInfo queryPhaseByTime(long actId, long rankId, long time) {
        RankingInfo rankingInfo = queryRankConfig(actId, rankId);
        Map<Long, RankingPhaseInfo> phasesMap = rankingInfo.getPhasesMap();
        for (Map.Entry<Long, RankingPhaseInfo> phaseInfo : phasesMap.entrySet()) {
            RankingPhaseInfo info = phaseInfo.getValue();
            if (info.getBeginTime() <= time && info.getEndTime() >= time) {
                return info;
            }
        }
        return null;
    }

    /**
     * @param actId  活动ID
     * @param rankId 榜单ID
     * @param time   时间,单位:毫秒
     * @return 若不存在阶段ID, 返回 -1 如小时榜无阶段ID
     */
    @Report
    public long queryPhaseIdByTime(long actId, long rankId, long time) {
        RankingPhaseInfo rankingPhaseInfo = queryPhaseByTime(actId, rankId, time);
        return rankingPhaseInfo == null ? 0 : rankingPhaseInfo.getPhaseId();
    }


    /**
     * 查询用户任务完成状态
     *
     * @param actId    活动id
     * @param rankId   榜单id
     * @param phaseId  阶段id，没有的话传0
     * @param dateStr  日期 yyyyMMdd
     * @param memberId 成员id(用户uid)
     * @return
     */
    @Report
    public QueryUserTaskResponse queryUserTaskInfo(Long actId, Long rankId, Long phaseId, String dateStr, String memberId) {
        QueryUserTaskRequest request = new QueryUserTaskRequest();
        request.setActId(actId);
        request.setRankId(rankId);
        request.setPhaseId(phaseId);
        request.setMemberId(memberId);
        request.setDateStr(dateStr);

        Clock clock = new Clock();
        try {
            QueryUserTaskResponse response = getReadProxy().queryUserTaskInfo(request);
            if (response == null || response.getCode() != 0) {
                log.error("queryUserTaskInfo error,actId:{},clock:{}", actId, clock.tag());
                return null;
            }
            return response;

        } catch (Exception e) {
            log.error("queryUserTaskInfo error actId:{}, clock:{}, exception:", actId, clock.tag(), e);
            return null;
        }

    }

    @Report
    public Date getTime() {
        try {
            return DateUtil.getDate(getReadProxy().currentTime());
        } catch (Exception e) {
            return new Date();
        }
    }

    @Report
    public String getTimeString() {
        try {
            return getReadProxy().currentTime();
        } catch (Exception e) {
            log.warn("get time error,e:{}", e.toString(), e);
            return DateUtil.format(new Date());
        }
    }

    @Report
    public String getTimeString(long actId) {
        try {
            return getReadProxy().getCurrentTime(actId);
        } catch (Exception e) {
            log.warn("get time error,e:{}", e.toString(), e);
            return DateUtil.format(new Date());
        }
    }

    @Report
    public List<EnrollmentInfo> queryEnrollmentInfo(Long actId) {
        List<EnrollmentInfo> result = Lists.newArrayList();
        Clock clock = new Clock();
        try {
            QueryEnrollmentInfoRequest request = new QueryEnrollmentInfoRequest();
            request.setActId(actId);
            int page = 1;
            int pageSize = 3000;
            request.setPageSize(pageSize);
            while (true) {
                request.setPage(page);
                QueryEnrollmentInfoResponse response = getReadProxy().queryEnrollment(request);
                if (response == null || response.getCode() != 0) {
                    log.error("queryEnrollmentInfo response:{}, request:{},clock:{}", response, request, clock.tag());
                }
                if (response == null) {
                    log.error("queryEnrollmentInfo response null, request:{} {}", request, clock.tag());
                    return null;
                }
                List<EnrollmentInfo> items = response.getItems();
                if (!CollectionUtils.isEmpty(items)) {
                    result.addAll(items);
                }
                if (items == null || items.size() < pageSize) {
                    break;
                }

                page++;
            }

            log.info("queryEnrollmentInfo done,actId:{},size:{},cost:{}", actId, result.size(), clock.tag());

            return result;
        } catch (Exception e) {
            log.error("queryEnrollmentInfo error,actId:{},e:{}", actId, e.toString(), e);
            return null;
        }
    }

    @Report
    public List<EnrollmentInfo> queryEnrollmentInfoNocache(Long actId, Long roleType, List<String> memberIds) {
        Clock clock = new Clock();
        try {
            QueryEnrollmentInfoRequest request = new QueryEnrollmentInfoRequest();
            request.setActId(actId);
            request.setNocache(true);
            request.setRoleType(roleType);
            request.setMemberIds(memberIds);

            QueryEnrollmentInfoResponse response = getReadProxy().queryEnrollment(request);
            if (response == null || response.getCode() != 0) {
                log.error("queryEnrollmentInfoNocache response:{}, request:{} {}", response, request, clock.tag());
            }
            if (response == null) {
                log.error("queryEnrollmentInfoNocache response null, request:{} {}", request, clock.tag());
                return null;
            }
            return response.getItems();

        } catch (Exception e) {
            log.error("queryEnrollmentInfoNocache error,actId:{},clock:{},e:{}", actId, clock.tag(), e.toString(), e);
            return null;
        }
    }

    /**
     * @param actId
     * @param rankingEnrollment
     * @param isUpdate
     * @param remark
     * @param seq
     * @return 成功返回0，重复更新返回1，频繁操作返回2
     */
    @Report
    public int saveEnrollment(long actId, EnrollmentInfo rankingEnrollment, boolean isUpdate, String remark, String seq) {
        Clock clock = new Clock();
        try {
            SaveEnrollmentInfoRequest request = new SaveEnrollmentInfoRequest();
            request.setEnrollmentInfo(rankingEnrollment);
            request.setActId(actId);
            request.setRemark(remark);
            request.setUpdate(isUpdate);
            request.setSeq(seq);
            SaveEnrollmentInfoResponse response = getProxy().saveEnrollment(request);
            if (response == null || response.getCode() != 0) {
                log.error("saveEnrollment error,actId:{},req:{},rsp:{}", actId, request, response);
            }
            if (response == null) {
                log.error("saveEnrollment response null,actId:{},seq:{},isUpdate:{},rankingEnrollment:{},{}", actId, seq, isUpdate, rankingEnrollment.toString(), clock.tag());
                return -999;
            }
            int code = response.getCode();
            //异常了
            if (code < 0) {
                log.error("saveEnrollment error,actId:{},seq:{},isUpdate:{},rankingEnrollment:{},message:{}", actId, seq, isUpdate, rankingEnrollment.toString(), response.getReason());
                return -999;
            }
            return code;
        } catch (Exception e) {
            log.error("saveEnrollment error,actId:{},seq:{},isUpdate:{},rankingEnrollment:{}", actId, seq, isUpdate, rankingEnrollment.toString(), e);
            return -999;
        }
    }


    /**
     * 只查询阶段pk （上次排名分数加成员ID）-做缓存加快查询效率
     *
     * @param pkConfigDateStr yyyyMMdd
     * @param srcRankId       来源榜单
     * @return
     */
    @Cached(timeToLiveMillis = 5 * 1000)
    @Report
    public PkInfo queryPhasePkgroup(Long actId, Long srcRankId, Long phaseId, String pkConfigDateStr) {
        return queryPhasePkgroup(actId, srcRankId, phaseId, pkConfigDateStr, "", true, false, Maps.newHashMap());
    }

    /**
     * 查询阶段pk 信息
     *
     * @param pkConfigDateStr yyyyMMdd
     * @param withPreScore    是否查询晋级时候的分数
     * @param withScore       是否查询当前阶段分数
     * @param srcRankId       来源榜单
     * @param rankDateStr     查询当前排名和分数指定的时间，yyyyMMdd，withScore = true 时选填
     * @return
     */
    @Report
    public PkInfo queryPhasePkgroup(Long actId, Long srcRankId, Long phaseId, String pkConfigDateStr, String rankDateStr, boolean withPreScore, boolean withScore, Map<String, String> ext) {
        QueryPkInfoRequest request = new QueryPkInfoRequest();
        Clock clock = new Clock();
        request.setActId(actId);
        request.setSrcRankId(srcRankId);
        request.setPhaseId(phaseId);
        request.setQueryDateStr(rankDateStr);
        request.setDateStr(pkConfigDateStr);
        if (withPreScore) {
            request.setReturnData(1);
        }
        if (withScore) {
            request.setReturnData(2);
        }
        request.setExtData(ext);
        try {
            QueryPkInfoResponse response = getReadProxy().queryPkInfo(request);
            if (response.getCode() == 0) {
                //修改过，先打印，后面删除
                //log.info("queryPhasePkgroup,actId:{},request:{},response:{}", actId, JSON.toJSONString(request), JSON.toJSONString(response));
                return response.getPkConfig();
            } else {
                log.error("queryPhasePkgroup,actId:{},request:{},response:{}", actId, JSON.toJSONString(request), JSON.toJSONString(response));
                return null;
            }
        } catch (Exception e) {
            log.error("queryPhasePkgroup,actId:{},rankId:{},request:{},clock:{},e:{}", actId, JSON.toJSONString(request), clock.tag(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 只查询晋级分组信息（上次排名分数加成员ID）-做缓存加快查询效率
     *
     * @param actId
     * @param rankId
     * @param phaseId
     * @return
     */
    @Cached(timeToLiveMillis = 60 * 1000)
    @Report
    public MemberGroup queryQualificationGroup(Long actId, Long rankId, Long phaseId) {
        return queryQualificationGroup(actId, rankId, phaseId, "", true, false, Maps.newHashMap());
    }

    /**
     * 查询晋级分组信息-可指定返回积分和排名
     *
     * @param withPreScore 是否查询晋级时候的分数
     * @param withScore    是否查询当前阶段分数
     * @param queryDateStr 查询当前排名和分数指定的时间，yyyyMMdd，withScore = true 时选填
     * @return
     */
    @Report
    public MemberGroup queryQualificationGroup(Long actId, Long rankId, Long phaseId, String queryDateStr, boolean withPreScore, boolean withScore, Map<String, String> ext) {
        QueryQualificationGroupRequest request = new QueryQualificationGroupRequest();
        Clock clock = new Clock();
        request.setActId(actId);
        request.setRankId(rankId);
        request.setPhaseId(phaseId);
        request.setQueryDateStr(queryDateStr);
        if (withPreScore) {
            request.setReturnData(1);
        }
        if (withScore) {
            request.setReturnData(2);
        }
        request.setExtData(ext);
        try {
            QueryQualificationGroupResponse response = getReadProxy().queryQualificationGroup(request);
            if (response.getCode() == 0) {
                //修改过，先打印，后面删除
                //log.info("queryQualificationGroup,actId:{},request:{},response:{}", actId, JSON.toJSONString(request), JSON.toJSONString(response));
                return response.getMemberGroup();
            } else {
                log.error("queryQualificationGroup,actId:{},request:{},response:{}", actId, JSON.toJSONString(request), JSON.toJSONString(response));
                return null;
            }
        } catch (Exception e) {
            log.error("queryPhasePkgroup,actId:{},rankId:{},request:{},clock:{},e:{}", actId, JSON.toJSONString(request), clock.tag(), e.getMessage(), e);
            return null;
        }
    }

    @Report
    public Map<Long, HdztActorInfo> queryHdztActorInfo() {
        Clock clock = new Clock();
        try {
            QueryHdztActorInfoResponse response = getReadProxy().queryHdztActorInfo(new QueryHdztActorInfoRequest());
            if (response.getCode() == 0) {
                return response.getRoles();
            } else {
                log.error("queryHdztActorInfo error,response:{}", JSON.toJSONString(response));
                return null;
            }
        } catch (Exception e) {
            log.error("queryHdztActorInfo,clock:{},e:{}", clock.tag(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查询指定用户的榜单分数
     *
     * @param actId
     * @param rankId
     * @param phaseId
     * @param dateStr
     * @param pointedMembers
     * @param ext
     * @return
     */
    @Report
    public Map<String, Rank> queryPointedMembersRanking(long actId, long rankId, long phaseId, String dateStr, List<String> pointedMembers, Map<String, String> ext) {
        List<QueryRankingRequest> rankingRequests = Lists.newArrayList();
        for (String pointedMember : pointedMembers) {
            QueryRankingRequest rankingRequest = new QueryRankingRequest();
            rankingRequest.setActId(actId);
            rankingRequest.setRankingId(rankId);
            rankingRequest.setPhaseId(phaseId);
            rankingRequest.setRankingCount(0);
            rankingRequest.setDateStr(dateStr);
            rankingRequest.setPointedMember(pointedMember);
            rankingRequest.setExtData(Maps.newHashMap(ext));
            rankingRequests.add(rankingRequest);
        }
        List<List<Rank>> ranksList = queryBatchRanking(rankingRequests, Maps.newHashMap());
        return ranksList.stream().flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toMap(Rank::getMember, Function.identity(), (k1, k2) -> k1));
    }

    /**
     * 查询指定用户的榜单分数
     *
     * @param actId
     * @param rankId
     * @param phaseId
     * @param dateStr
     * @param pointedMember
     * @param ext
     * @return
     */
    @Report
    public Rank queryPointedMembersRanking(long actId, long rankId, long phaseId, String dateStr, String pointedMember, Map<String, String> ext) {
        return queryPointedMembersRanking(actId, rankId, phaseId, dateStr, Lists.newArrayList(pointedMember), ext).get(pointedMember);
    }


    /**
     * 批量查询榜单
     *
     * @param batchQueryRequests
     * @param ext
     * @return
     */
    @Report
    public List<List<Rank>> queryBatchRanking(List<QueryRankingRequest> batchQueryRequests, Map<String, String> ext) {
        if (CollectionUtils.isEmpty(batchQueryRequests)) {
            return Lists.newArrayList();
        }

        Map<String, QueryRankingRequest> queryMap = IntStream.range(0, batchQueryRequests.size())
                .boxed().collect(Collectors.toMap(i -> i + "", i -> batchQueryRequests.get(i)));

        Map<String, BatchRankingItem> rankMap = queryBatchRanking(queryMap, ext);

        List<BatchRankingItem> batchRankingItems = IntStream.range(0, batchQueryRequests.size())
                .boxed()
                .map(i -> rankMap.get(i + "")).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(batchRankingItems)) {
            log.error("queryBatchRanking error,batchQueryRequests = {},rankingResult:{}", JSON.toJSONString(batchQueryRequests), JSON.toJSONString(batchRankingItems));
            return Lists.newArrayList();
        }
        List<List<Rank>> ranksLists = Lists.newArrayList();
        // 最后1个元素放置 指定成员的结果（这个办法很丑，为了和以前兼容，暂时只能这样做了！）， 主动传了 pointedMember 的地方要自己负责做数据分离等处理！！！
        for (BatchRankingItem batchRankingItem : batchRankingItems) {
            Rank pointedRank = batchRankingItem.getPointedRank();
            List<Rank> ranks = batchRankingItem.getData();
            if (pointedRank != null) {
                ranks.add(pointedRank);
            }
            if (ranks != null) {
                ranksLists.add(ranks);
            }

        }
        return ranksLists;
    }

    /**
     * 通过扩展参数确定榜单的查询类型，默认是 HdztRankType.CONTEST
     *
     * @param ext
     * @return
     */
    private String getRankQueryType(Map<String, String> ext) {
        if (!MapUtils.isEmpty(ext)) {
            if (ext.containsKey(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID)) {
                return HdztRankType.SRC;
            } else if (ext.containsKey(RankExtParaKey.RANK_SCORE_SOURCE)) {
                return ext.get(RankExtParaKey.RANK_SCORE_SOURCE);
            } else if (ext.containsKey(RankExtParaKey.RANK_PK_VALUE)) {
                return HdztRankType.CONTEST_PK;
            } else if (ext.containsKey(RankExtParaKey.QUERY_RANK_TYPE)) {
                return ext.get(RankExtParaKey.QUERY_RANK_TYPE);
            } else if (ext.containsKey(RankExtParaKey.RANK_PK_REVIVE)) {
                return HdztRankType.REVIVE;
            } else if (ext.containsKey(RankExtParaKey.RANK_TYPE_ALL)) {
                return HdztRankType.PHASE_ALL;
            } else if ((ext.containsKey(RankExtParaKey.RANK_TYPE_PROMOT))) {
                return HdztRankType.PROMOT;
            }
        }

        return HdztRankType.CONTEST;
    }

    /**
     * 批量查询榜单
     *
     * @param batchQueryRequestMap
     * @param ext
     * @return
     */
    @Report
    public Map<String, BatchRankingItem> queryBatchRanking(Map<String, QueryRankingRequest> batchQueryRequestMap, Map<String, String> ext) {
        if (MapUtils.isEmpty(batchQueryRequestMap)) {
            return Maps.newHashMap();
        }
        //屏蔽中台复杂榜单概念，默认是当前比拼的榜单
        batchQueryRequestMap.values().stream().forEach(query -> {
            Map<String, String> queryExtData = query.getExtData();
            String rankType = getRankQueryType(queryExtData);
            query.setRankType(rankType);
            //贡献榜
            if (HdztRankType.SRC.equals(rankType)) {
                query.setFindSrcMember(queryExtData.get(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID));
            }

        });

        QueryBatchRankingRequest request = new QueryBatchRankingRequest();
        request.setBatchQuestData(batchQueryRequestMap);
        request.setExtData(ext);

        QueryBatchRankingResult rankingResult = null;

        try {
            rankingResult = getReadProxy().queryBatchRanking(request);
        } catch (Exception e) {
            log.error("queryBatchRanking error,batchQueryRequests = {},e:{}", JSON.toJSONString(batchQueryRequestMap), e.toString(), e);
        }
        if (rankingResult == null || rankingResult.getCode() != 0) {
            log.error("queryBatchRanking error,batchQueryRequests = {},rankingResult:{}", JSON.toJSONString(batchQueryRequestMap), JSON.toJSONString(rankingResult));
            return Maps.newHashMap();
        }

        Map<String, BatchRankingItem> rankMap = rankingResult.getItems();
        if (MapUtils.isEmpty(rankMap)) {
            log.error("queryBatchRanking error,batchQueryRequests = {},rankingResult:{}", JSON.toJSONString(batchQueryRequestMap), JSON.toJSONString(rankingResult));
            return Maps.newHashMap();
        }
        return rankMap;
    }

    /**
     * 判断是否在白名单中，如果调用失败异常则返回不在白名单中禁止操作，最大限度防止错误发放
     * 只有在灰度的时候才允许调用这个接口
     */
    @Report
    public boolean checkWhiteList(long actId, RoleType roleType, String roleValue) {
        try {
            SimpleResult result = getReadProxy().checkWhiteList(actId, roleType, roleValue, null);
            return result.getCode() == 0;
        } catch (Exception e) {
            log.error("checkWhiteList error,actId:{},roleType:{},value:{},e:{}", actId, roleType, roleValue, e.toString(), e);
            return false;
        }
    }

    /**
     * 获取主赛程相关,成员所在的榜单id(淘汰赛制定位用)
     *
     * @param actId    活动id
     * @param memberId 成员id
     * @return 榜单id
     */
    @Report
    public long queryNotInRaceRankId(long actId, String memberId) {

        Clock clock = new Clock();
        try {
            QueryRankIdRequest request = new QueryRankIdRequest();
            request.setActId(actId);

            QueryRankIdItem queryRankIdItem = new QueryRankIdItem();
            queryRankIdItem.setMemberId(memberId);
            request.setItems(Collections.singletonList(queryRankIdItem));

            //未晋级榜单
            request.setQueryType(1);

            QueryRankIdResponse response = getReadProxy().queryRankId(request);
            if (response == null || response.getRankIdMap() == null) {
                log.error("queryNotInRaceRankId error,actId:{},memberId:{},clock:{}", actId, memberId, clock.tag());
                return -1;
            }
            List<Long> rankIds = response.getRankIdMap().get(memberId);
            if (CollectionUtils.isEmpty(rankIds)) {
                return -1;
            }

            return rankIds.get(0);
        } catch (Exception e) {
            log.error("queryNotInRaceRankId error,actId:{},clock:{},e:{}", actId, clock.tag(), e.toString(), e);
            return -1;
        }
    }

    @Report
    public Map<String, String> invokeRetry(long busiId, long type, Map<String, String> data, String sign, int retryCount) {
        int retry = retryCount;
        while (retry-- > 0) {
            try {
                Map<String, String> responseData = hdztRankingThriftClient.invoke(busiId, type, data, sign);
                if (MapUtils.isNotEmpty(responseData)) {
                    return responseData;
                }
                Thread.sleep(50);
            } catch (Exception e) {
                log.error("invoke error,busiId:{},type:{},data:{},sign:{}",
                        busiId, type, JSON.toJSONString(data), sign, e);
            }
        }
        return Maps.newHashMap();

    }
    //榜单配置信息

    @Report
    public Map<String, String> invoke(long busiId, long type, Map<String, String> data, String sign) {

        Clock clock = new Clock();
        try {
            SimpleResult response = getProxy().invoke(busiId, type, data, sign);
            if (response == null || response.getCode() != 0 || response.getData() == null) {
                log.error("invoke error,busiId:{},type:{},data:{},sign:{},clock:{},response:{}",
                        busiId, type, JSON.toJSONString(data), sign, clock.tag(), JSON.toJSONString(response));
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("invoke error,busiId:{},type:{},data:{},sign:{},clock:{}",
                    busiId, type, JSON.toJSONString(data), sign, clock.tag(), e);
            return null;
        }
    }


    /**
     * 查询榜单 key 数据,中台没有做缓存，直接查redis数据，要谨慎少量调用
     * ps：way = 1：按分值降序取排名在[from,to]者 from从0开始
     * way = 2：按分值升序取排名在[from,to]者
     * way = 3：按分值取 from~to之间成员，若from>to结果是升序，若from<to结果是降序   offset>0时count才有效
     *
     * @param actId
     * @param condition
     * @param extData
     * @return
     */
    @Report
    public List<ZsetMember> queryZsetRawData(long actId, ZsetQueryCondition condition, Map<String, String> extData) {
        if (condition == null) {
            return Lists.newArrayList();
        }
        List<List<ZsetMember>> zsetRawData = batchQueryZsetRawData(actId, Lists.newArrayList(condition), extData);
        return CollectionUtils.isEmpty(zsetRawData) ? null : zsetRawData.get(0);
    }

    /**
     * 批量查询榜单 key 数据,中台没有做缓存，直接查redis数据，要谨慎少量调用
     * ps：way = 1：按分值降序取排名在[from,to]者 from从0开始
     * way = 2：按分值升序取排名在[from,to]者
     * way = 3：按分值取 from~to之间成员，若from>to结果是升序，若from<to结果是降序   offset>0时count才有效
     *
     * @param actId
     * @param conditions
     * @param extData
     * @return
     */
    @Report
    public List<List<ZsetMember>> batchQueryZsetRawData(long actId, List<ZsetQueryCondition> conditions, Map<String, String> extData) {
        if (CollectionUtils.isEmpty(conditions)) {
            return Lists.newArrayList();
        }
        Clock clock = new Clock();
        try {
            QueryZsetRawDataRequest request = new QueryZsetRawDataRequest(actId, conditions, extData);
            QueryZsetRawDataResponse response = getReadProxy().queryZsetRawData(request);

            if (response == null || response.getCode() != 0) {
                log.error("queryZsetRawData error,request = {},response:{}", JSON.toJSONString(request), JSON.toJSONString(response));
                return Lists.newArrayList();
            }
            return response.getRawDatas();
        } catch (Exception e) {
            log.error("queryZsetRawData error,actId:{},clock:{},e:{}", actId, clock.tag(), e.toString(), e);
            return Lists.newArrayList();
        }
    }

    /**
     * 过任务项目配置
     */
    @Cached(timeToLiveMillis = CacheTimeout.COMMON_DB_STATIC_CONFIG)
    @Report
    public Map<String, List<RankingTaskItem>> queryRankingTaskItemMap(long actId, long rankId, long phaseId) {
        List<RankingTaskItem> items = queryRankingTaskItem(actId, rankId, phaseId);
        return items.stream().collect(Collectors.groupingBy(RankingTaskItem::getItemId));
    }

    @Report
    public List<RankingTaskItem> queryRankingTaskItem(long actId, long rankId, long phaseId) {
        Clock clock = new Clock();
        try {
            RankingTaskItemRequest request = new RankingTaskItemRequest();
            request.setActId(actId);
            request.setRankId(rankId);
            request.setPhaseId(phaseId);
            RankingTaskItemResponse response = getReadProxy().queryRankingTaskItem(request);

            if (response == null || response.getCode() != 0) {
                log.error("queryRankingTaskItem error,request = {},response:{}", JSON.toJSONString(request), JSON.toJSONString(response));
                return Lists.newArrayList();
            }
            return response.getItems();
        } catch (Exception e) {
            log.error("queryRankingTaskItem error,actId:{},rankId:{},clock:{},e:{}", actId, rankId, clock.tag(), e.toString(), e);
            return Lists.newArrayList();
        }
    }

    @Report
    public boolean notifySettleWithRetry(long actId, long rankId, int type, String tag, int retryTimes) {
        int retry = 0;
        boolean isSucceed = false;
        while (retry < retryTimes) {
            retry++;
            try {
                NotifySettleResponse result = notifySettle(actId, rankId, type, tag);
                if (result != null && result.getCode() == 0) {
                    isSucceed = true;
                    break;
                }
            } catch (Exception ex) {
                if (retry == retryTimes) {
                    log.error("notifySettleWithRetry error,actId={},rankId={},type={},tag={}", actId, rankId, type, tag, ex);
                }
            }
            SysEvHelper.waiting(50);
        }
        return isSucceed;
    }

    @Report
    public boolean setDynamicPkMembers(long actId, long rankId, long phaseId, String pkMembers, long opUid, String seq, int retryTimes) {
        int retry = 0;
        boolean isSucceed = false;
        while (retry < retryTimes) {
            retry++;
            try {
                SimpleResult simpleResult = getProxy().setDynamicPkMembers(actId, rankId, phaseId, pkMembers, opUid, seq);
                log.info("setDynamicPkMembers ,actId:{},rankId:{},phaseId:{},pkMembers:{},opUid:{},seq:{},e:{}", actId, rankId, phaseId, pkMembers, opUid, seq, JSON.toJSONString(simpleResult));
                //1001 代表已经设置成功了，不用重复设置
                if (simpleResult.getCode() == 0 || simpleResult.getCode() == 1001) {
                    isSucceed = true;
                    break;
                }else{
                    log.error("setDynamicPkMembers error,actId:{},rankId:{},rsp:{}", actId, rankId, simpleResult);
                }
            } catch (Exception e) {
                log.error("setDynamicPkMembers error,actId:{},rankId:{},phaseId:{},pkMembers:{},opUid:{},seq:{},e:{}", actId, rankId, phaseId, pkMembers, opUid, seq, e.getMessage(), e);
            }
        }

        return isSucceed;
    }

    /**
     * 发出可以结算的通知（在结算重试期内，可安全地重复调用）
     * 1. 实际结算处理器会拾取这个通知标记作为结算要满足的前提条件之一
     * 2. 即使返回通知成功，也不代表实际完成了结算（结算处理器会按自己的时序机制和条件判断去启动结算）
     * 3. 结算处理器会延迟启动（约5秒），并在指定时间内（约10分钟）一直尝试未完成的结算，超过后就不再处理，所以通知应及早发出
     *
     * @param actId
     * @param rankId
     * @param type   1：晋级结算，2：PK结算，其它值待定
     * @param tag    通知的结算标记，只能是数字和字母的组合（这个tag在中台榜单也会预配置好，匹配了才有效）
     * @return
     */
    @Report
    public NotifySettleResponse notifySettle(long actId, long rankId, int type, String tag) {
        Clock clock = new Clock();
        try {
            NotifySettleRequest request = new NotifySettleRequest();
            request.setActId(actId);
            request.setRankId(rankId);
            request.setType(type);
            request.setTag(tag);
            NotifySettleResponse response = getProxy().notifySettle(request);

            if (response == null || response.getCode() != 0) {
                log.error("notifySettle error,request = {},response:{}", JSON.toJSONString(request), JSON.toJSONString(response));
            }
            log.info("notifySettle,actId:{},rankId:{},type:{},tag:{},response:{}", actId, rankId, type, tag, JSON.toJSONString(response));
            return response;
        } catch (Exception e) {
            log.error("notifySettle error,actId:{},rankId:{},clock:{},e:{}", actId, rankId, clock.tag(), e.toString(), e);
            return null;
        }
    }

    @Report
    public boolean updateRankWithRetry(String seq, long actId, String itemId, String memberId, long rankRoleId, long score
            , Map<Long, Long> rankingScore, long time, int retryTimes, int busiId) {
        int retry = 0;
        boolean result = false;
        while (retry < retryTimes) {
            retry++;
            result = updateRank(seq, actId, itemId, memberId, rankRoleId, score, rankingScore, time, busiId);
            if (result) {
                break;
            }
            SysEvHelper.waiting(50);
        }
        return result;
    }

    @Report
    public boolean updateRankWithRetry(UpdateRankingRequest request, int retryTimes) {
        int retry = 0;
        boolean result = false;
        while (retry < retryTimes) {
            retry++;
            result = updateRank(request);
            if (result) {
                break;
            }
            SysEvHelper.waiting(50);
        }
        return result;
    }

    /**
     * 更新经验榜单
     **/
    @Report
    public boolean updateRank(String seq, long actId, String itemId, String memberId, long rankRoleId, long score
            , Map<Long, Long> rankingScore, long time, int busiId) {
        Map<Long, String> actors = new HashMap<>(Collections.singletonMap(rankRoleId, memberId));
        UpdateRankingRequest request = new UpdateRankingRequest();
        request.setBusiId(busiId);
        request.setActId(actId);
        request.setSeq(seq);
        request.setActors(actors);
        request.setItemId(itemId);
        request.setCount(1);
        request.setScore(score);
        request.setRankScores(rankingScore == null ? Maps.newHashMap() : rankingScore);
        request.setTimestamp(time);
        request.setExtData(Maps.newHashMap());
        try {
            UpdateRankingResult result = hdztRankingThriftClient.getProxy().updateRanking(request);
            log.info("updateExperienceRank success,request={},result={}", request, result);

            if (result == null || result.getCode() != 0) {
                log.error("updateRanking error,req:{},rsp:{}", request, result);
            }

            return true;
        } catch (Exception ex) {
            log.error("updateExperienceRank error,request={}", request, ex);
            return false;
        }
    }

    @Report
    public boolean updateRank(UpdateRankingRequest request) {
        try {
            UpdateRankingResult result = hdztRankingThriftClient.getProxy().updateRanking(request);
            log.info("updateRank success,request={},result={}", request, result);
            if (result != null && result.getCode() == 0) {
                log.info("updateRank request={},result={}", JSON.toJSONString(request), JSON.toJSONString(result));
                return true;
            } else {
                log.error("updateRanking error,req:{},rsp:{}", request, result);
            }
            return true;
        } catch (Exception ex) {
            log.error("updateExperienceRank error,request={}", request, ex);
            return false;
        }
    }

    @Report
    public boolean updateRankingWithRetry(UpdateRankingRequest request, int retryTimes) {
        int retry = 0;

        // 重试3次,总共会执行4次
        retryTimes = retryTimes + 1;
        UpdateRankingResult result = null;
        while (retry < retryTimes) {
            retry++;
            try {
                result = hdztRankingThriftClient.getProxy().updateRanking(request);
                if (result != null && result.getCode() == 0) {
                    log.info("updateRankingWithRetry request={},result={}", JSON.toJSONString(request), JSON.toJSONString(result));
                    return true;
                } else {
                    log.error("updateRanking error,req:{},rsp:{}", request, result);
                }
            } catch (Exception ex) {
                if (retry == retryTimes) {
                    log.error("updateRankingWithRetry error,request={},result={}", JSON.toJSONString(request), JSON.toJSONString(result), ex);
                }
            }

            SysEvHelper.waiting(50);
        }

        return false;
    }

    public Map<String, MemberRankingInfo> queryMemberRankingInfo(MemberRankingInfoRequest request) {
        int retry = 3;
        while (retry-- > 0) {
            try {
                MemberRankingInfoResponse response = hdztRankingThriftClient.getReadProxy().queryMemberRankingInfo(request);
                if (response != null && response.getCode() == 0) {
                    return response.getMemberRankingInfos();
                } else {
                    log.error("queryMemberRankingInfo fail with response:{}", response);
                }
            } catch (Exception e) {
                log.error("queryMemberRankingInfo error, request={}, exception:", request, e);
            }
        }

        return Collections.emptyMap();
    }


    public Date getNow(long actId) {
        Date now = new Date();
        if (!SysEvHelper.isDeploy() || cacheService.getActAttrValue(actId, GeActAttrConst.ACTIVITY_GREY_STATUS, Const.ONESTR).equals(Const.ONESTR)) {
            String actVirtTime = hdztRankingThriftClient.getTimeString(actId);
            if (StringUtils.hasLength(actVirtTime)) {
                LocalDateTime virt = LocalDateTime.parse(actVirtTime, DateUtil.YYYY_MM_DD_HH_MM_SS);
                now = DateUtil.toDate(virt);
            }
        }
        return now;
    }
}
