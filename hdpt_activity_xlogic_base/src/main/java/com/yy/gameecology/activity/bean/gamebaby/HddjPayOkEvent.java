/**
 * HddjPayOkEvent.java / 2017年12月21日 下午1:04:32
 * 
 * Copyright (c) 2017, YY Inc. All Rights Reserved.
 * 
 * 郭立平[<EMAIL>]
 */

package com.yy.gameecology.activity.bean.gamebaby;

import com.alibaba.fastjson.JSON;

/** 
 * 活动道具支付成功事件
 * <AUTHOR>
 * @date 2017年12月21日 下午1:04:32 
 */
public class HddjPayOkEvent extends YXBBEvent {

    private static final long serialVersionUID = 1L;

    private Transaction transaction;

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
