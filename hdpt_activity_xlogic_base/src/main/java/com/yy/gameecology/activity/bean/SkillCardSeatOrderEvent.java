package com.yy.gameecology.activity.bean;

import lombok.Data;

import java.util.List;

@Data
public class SkillCardSeatOrderEvent {

    private Long sid;

    private Long ssid;

    private Integer roomId;

    private Long familyId;

    private List<SimpleSkillCardSeatOrder> data;

    /**
     * 事件ID
     */
    private String seq;

    /**
     * 时间戳(秒)
     */
    private long timestamp;


    private String app;
}
