package com.yy.gameecology.activity.worker.subscriber;

import com.alibaba.fastjson.JSON;
import com.google.common.primitives.Ints;
import com.yy.gameecology.activity.bean.GiftSourceChannel;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.mq.FriendGift;
import com.yy.gameecology.activity.bean.mq.FriendGiftEvent;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.broadcast.Template;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;

/**
 * 交友道具消费事件
 *
 * <AUTHOR> 2021/8/26
 */
@Component
public class JiaoyouSendGiftConsumer {

    private static final Logger log = LoggerFactory.getLogger(JiaoyouSendGiftConsumer.class);


    @Autowired
    private SendGiftConsumer sendGiftConsumer;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    private static final String JIAOYOUGIFT_EVENT_SEQ_KEY = "jiaoyou_gift_event_seq";
    /**
     * 过期时间：2天，单位：秒
     */
    private static final long EXPIRE_SEC = 2 * 24 * 60 * 60;

    /**
     * 分数给主持人的玩法
     */
    public static final int[] COMPERE_TYPE = {0, 2, 7, 13, 17, 20};

    /**
     * 分数给收礼人的玩法
     */
    public static final int[] RECV_TYPE = {8, 801, 802, 803, 18, 1801, 1802,1803, 19, 1901, 1902,1903, 2304};


    public void handle(FriendGiftEvent giftEvent) {

        if (giftEvent == null || CollectionUtils.isEmpty(giftEvent.getGiftList()) || giftEvent.getExpand() == null) {
            //陈友煌说: 送礼列表为空的直接忽略 Expand一般不会为null, 因此直接忽略
            log.info("jiaoyouGiftEvent ignore.event:{}", JSON.toJSONString(giftEvent));
            return;
        }

        String seq = giftEvent.getSeqID();
        if (!actRedisDao.zSetNX(redisConfigManager.temp_act_group, JIAOYOUGIFT_EVENT_SEQ_KEY + StringUtil.UNDERSCORE + SysEvHelper.getGroup(), seq, EXPIRE_SEC)) {
            log.warn("jiaoyouGiftEvent ignore. seq double,seq:{}", seq);
            return;
        }

        //TODO: 玩法更新时需更新此处
        int grabLoveType = giftEvent.getExpand().getGrabLoveType();
        long recvUid = 0;
        if (Ints.contains(COMPERE_TYPE, grabLoveType)) {
            // TODO: 2020七夕, 都给收礼人, 算连送, 结束后需改回
            recvUid = giftEvent.getRecvUid();
        } else if (Ints.contains(RECV_TYPE, grabLoveType)) {
            recvUid = giftEvent.getRecvUid();
        }
        if (recvUid <= 0) {
            log.warn("jiaoyouGiftEvent ignore.not recvUid,seq:{}, grabLoveType:{}", seq, grabLoveType);
            return;
        }

        GiftSourceChannel giftSourceChannel = GiftSourceChannel.findByTurnover(giftEvent.getChannel());
        //追玩低版本app（3.6.0） 用sid放的asid这里做临时兼容-陈侠专（2021-11-18）
        long sid = giftEvent.getSid();
        if (giftSourceChannel == GiftSourceChannel.ZHUIWAN) {
            sid = commonService.getSid(sid);
            if (giftEvent.getSid() != sid) {
                log.info("jiaoyouGiftEvent adjust event sid@seq:{} old:{} newSid:{}", giftEvent.getSeqID(), giftEvent.getSid(), sid);
            }
        }

        for (int i = 0; i < giftEvent.getGiftList().size(); i++) {
            FriendGift giftList = giftEvent.getGiftList().get(i);
            SendGiftEvent sendGiftEvent = new SendGiftEvent();
            sendGiftEvent.setTemplate(Template.Jiaoyou);
            sendGiftEvent.setSeq(giftEvent.getSeqID() + "_" + i);
            sendGiftEvent.setSendUid(giftEvent.getSendUid());
            sendGiftEvent.setRecvUid(recvUid);
            sendGiftEvent.setGiftId(String.valueOf(giftList.getGiftId()));
            sendGiftEvent.setGiftNum(giftList.getGiftCount());
            sendGiftEvent.setGiftAmount(giftList.getGiftAmount());
            sendGiftEvent.setSid(sid);
            sendGiftEvent.setSsid(giftEvent.getSsid());
            sendGiftEvent.setSourceChannel(giftSourceChannel);
            sendGiftEvent.setEventTime(new Date(giftEvent.getTimestamp()));
            sendGiftEvent.addJsonData("GiftType", giftList.getGiftType());
            sendGiftEvent.addJsonData("GiftAmount", giftList.getGiftAmount());
            sendGiftEvent.addJsonData("GrabLoveType", grabLoveType);
            sendGiftEvent.addJsonData("isNewTemplate", giftEvent.getExpand().getIsNewTemplate());
            if (giftList.getOriginalPrice() > 0) {
                //盖章
                sendGiftEvent.addJsonData("OriginalPrice", giftList.getOriginalPrice());
            }
            sendGiftConsumer.onMessage(sendGiftEvent);
        }

        log.info("jiaoyouGiftEvent ok. seq:{}", seq);
    }

}
