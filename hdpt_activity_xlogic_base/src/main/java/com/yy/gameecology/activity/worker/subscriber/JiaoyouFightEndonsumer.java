package com.yy.gameecology.activity.worker.subscriber;

import com.yy.gameecology.activity.bean.mq.ChannelFightEndEvent;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.HdzjEventDispatcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 交友乱斗结束事件
 *
 * <AUTHOR> 2022/02/28
 */
@Component
public class JiaoyouFightEndonsumer {

    private static final Logger log = LoggerFactory.getLogger(JiaoyouFightEndonsumer.class);


    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;

    private static final String EVENT_SEQ_KEY = "jiaoyou_fight_end_%s:%s";
    /**
     * 过期时间：2天，单位：秒
     */
    private static final long EXPIRE_SEC = 2 * 24 * 60 * 60;


    public void handle(ChannelFightEndEvent fightEndEvent) {

        if (fightEndEvent == null) {
            log.info("jiaoyouFightEnd ignore.event is null");
            return;
        }

        String seq = fightEndEvent.getGameId();
        String seqKey = String.format(EVENT_SEQ_KEY, SysEvHelper.getGroup(), seq);
        if (!actRedisDao.setNX(redisConfigManager.temp_act_group, seqKey, StringUtil.ONE, EXPIRE_SEC)) {
            log.warn("jiaoyouFightEnd ignore. seq double,seq:{}", seq);
            return;
        }
        hdzjEventDispatcher.notify(fightEndEvent);
        log.info("jiaoyouFightEnd ok. seq:{}", seq);
    }

}
