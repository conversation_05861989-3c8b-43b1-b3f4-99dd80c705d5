package com.yy.gameecology.activity.service.impl;


import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.rankroleinfo.BabyRoleItem;
import com.yy.gameecology.activity.bean.rankroleinfo.UserBaseItem;
import com.yy.gameecology.activity.bean.rankroleinfo.UserRoleItem;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.activity.rolebuilder.RoleBuilder;
import com.yy.gameecology.activity.rolebuilder.impl.*;
import com.yy.gameecology.activity.service.ActSupportService;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.bean.UserInfo;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.Convert;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import com.yy.thrift.fts_base_info_bridge.CompereSign;
import com.yy.thrift.fts_base_info_bridge.TAppId;
import com.yy.thrift.fts_base_info_bridge.TTing;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
@Component
public class ActJySupportService implements ActSupportService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    private WebdbThriftClient webdbThriftClient;


    /**
     * 角色ID      memberId
     * Map<String, Map<String, MemberItemInfo>>
     *
     * @param actId
     * @param rankId
     * @param memberIdMap
     * @return
     */
    @Override
    public Map<String, Map<String, MemberItemInfo>> queryMember(long actId, long rankId, Map<String, List<String>> memberIdMap) {
        String tingRole = "50060";
        Map<String, Map<String, MemberItemInfo>> newTingMemberMap = null;
        List<String> newTingMember = memberIdMap.get(tingRole);
        if (CollectionUtils.isNotEmpty(newTingMember)) {
            memberIdMap.remove(tingRole);
            newTingMemberMap = queryTingMember(newTingMember, actId, rankId);
        }
        Map<String, Map<String, MemberItemInfo>> memberMap = ftsBaseInfoBridgeClient.queryMember(actId, rankId, memberIdMap);

        if (newTingMemberMap != null) {
            memberMap.putAll(newTingMemberMap);
        }

        return memberMap;
    }

    /**
     * 查询新厅信息
     *
     * @param newTingMember 新厅ID
     */
    private Map<String, Map<String, MemberItemInfo>> queryTingMember(List<String> newTingMember, long actId, long rankId) {
        Map<String, Map<String, MemberItemInfo>> memberMap = Maps.newHashMap();
        List<Long> ids = newTingMember.stream().map(Convert::toLong).collect(Collectors.toList());
        try {
            Map<Long, TTing> tTingMap = ftsBaseInfoBridgeClient.getReadProxy().batchQueryTingsByTids(TAppId.Dating, ids);
            List<Long> mgrUid = tTingMap.values().stream().map(TTing::getTingMgrUid).collect(Collectors.toList());

            Map<Long, UserInfo> userInfoMap = webdbThriftClient.batchGetUserInfo2(mgrUid);
            //查询交友用户信息
            /*List<String> mgrUidStr = mgrUid.stream().map(String::valueOf).collect(Collectors.toList());
            Map<String, List<String>> roleUid = new HashMap<>();
            roleUid.put("50003", mgrUidStr);
            Map<String, Map<String, MemberItemInfo>> member = ftsBaseInfoBridgeClient.queryMember(actId, rankId, roleUid);*/


            Map<String, MemberItemInfo> itemInfo = Maps.newHashMapWithExpectedSize(tTingMap.size());
            for (Map.Entry<Long, TTing> tingEntry : tTingMap.entrySet()) {
                MemberItemInfo memberItemInfo = new MemberItemInfo();
                memberItemInfo.setBaseFieldMemberId(String.valueOf(tingEntry.getKey()));

                long tingMgrUid = tingEntry.getValue().getTingMgrUid();
                UserInfo userInfo = userInfoMap.get(tingMgrUid);
                if (userInfo != null) {
                    memberItemInfo.setBaseFieldMemberUrl(userInfo.getAvatar());
                    memberItemInfo.setBaseFieldMemberName(userInfo.getNickName());
                } else {
                    memberItemInfo.setBaseFieldMemberUrl(Const.IMAGE.DEFAULT_CHANNEL_LOGO);
                }
                itemInfo.put(String.valueOf(tingEntry.getKey()), memberItemInfo);
            }
            memberMap.put("50060", itemInfo);
        } catch (TException e) {
            log.error("queryTingMember error, newTingMember: {}", newTingMember, e);
        }
        return memberMap;
    }


    @Override
    public RoleBuilder getUserRankBuilder() {
        return userRankBuilder;
    }

    @Override
    public RoleBuilder getBabyRankBuilder() {
        return babyRankBuilder;
    }

    @Override
    public RoleBuilder getHostRankBuilder() {
        return babyRankBuilder;
    }

    @Override
    public RoleBuilder getGuildRankBuilder() {
        return guildRankBuilder;
    }

    @Override
    public RoleBuilder getSubGuildRankBuilder() {
        return subGuidRankBuilder;
    }

    @Override
    public RoleBuilder getTeamRankBuilder() {
        return null;
    }

    @Override
    public RoleBuilder getTingRoleBuilder() {
        return newTingRankBuilder;
    }

    @Override
    public RoleBuilder getTingMgrRoleBuilder() {
        return tingMgrRoleBuilder;
    }

    private RoleBuilder userRankBuilder = new JyUserRoleBuilder();
    private RoleBuilder babyRankBuilder = new JyBabyRoleBuilder();
    private RoleBuilder guildRankBuilder = new GuildRoleBuilder();
    private RoleBuilder subGuidRankBuilder = new SubGuildRoleBuilder();
    private RoleBuilder newTingRankBuilder = new NewTingRoleBuilder();
    private RoleBuilder tingMgrRoleBuilder = new TingMgrRoleBuilder();


    class JyUserRoleBuilder extends UserRoleBuilder {

        @Override
        public UserRoleItem buildRankItemByEx(MemberItemInfo memberItemInfo, UserRoleItem roleItem) {
            if (memberItemInfo.getExt() != null) {
                Map<String, String> ext = memberItemInfo.getExt();
                roleItem.setNobleGrade(ext.get("nobleID"));
                roleItem.setGeNobleGrade(ext.get("ecologyNobleID"));
            }
            return roleItem;
        }
    }

    class JyBabyRoleBuilder extends BabyRoleBuilder {

        public JyBabyRoleBuilder() {
            super(Template.makefriend);
        }

        @Override
        public BabyRoleItem buildRankItemByEx(MemberItemInfo memberItemInfo, BabyRoleItem roleItem) {
            roleItem.setTemplateType(Template.makefriend.getCode());
            if (memberItemInfo.getExt() != null) {
                Map<String, String> ext = memberItemInfo.getExt();
                roleItem.setContractSid(Convert.toLong(ext.get("contractSid")));
                roleItem.setContractAsid(Convert.toLong(ext.get("contractAsid")));
                roleItem.setSid(Convert.toLong(ext.get("sid")));
                roleItem.setSsid(Convert.toLong(ext.get("ssid")));
            }
            return roleItem;
        }

        @Override
        public Map<String, BabyRoleItem> addExtraInfo(long actId, long roleId, long rankId, long uid, Map<String, BabyRoleItem> roleItemMap) {

            //优先使用父方法的填充
            roleItemMap = super.addExtraInfo(actId, roleId, rankId, uid, roleItemMap);


            List<Long> noSignBabyUids = roleItemMap.values().stream()
                    .filter(roleItem -> roleItem.getContractSid() == null)
                    .map(UserBaseItem::getUid).collect(Collectors.toList());

            //填充签约信息
            if (!noSignBabyUids.isEmpty()) {
                Map<Long, CompereSign> compereSignMap = ftsBaseInfoBridgeClient.getFtsSign(noSignBabyUids);
                for (Long babyUid : noSignBabyUids) {
                    CompereSign compereSign = compereSignMap.get(babyUid);
                    if (compereSign != null) {
                        BabyRoleItem babyRankRoleItem = roleItemMap.get(babyUid + "");
                        babyRankRoleItem.setContractSid(compereSign.getSid());
                        babyRankRoleItem.setContractAsid(compereSign.getAsid());
                    }
                }
            }

            //填充关注主播信息
            List<Long> noSubscribeUids = roleItemMap.values().stream()
                    .filter(roleItem -> roleItem.getSubscribe() == null)
                    .map(UserBaseItem::getUid).collect(Collectors.toList());

            if (!noSubscribeUids.isEmpty() && uid > 0) {
                Map<Long, Boolean> userSubscribeMap = ftsBaseInfoBridgeClient.getUserSubscribe(uid, noSubscribeUids);
                for (Long babyUid : noSubscribeUids) {
                    BabyRoleItem babyRankRoleItem = roleItemMap.get(babyUid + "");
                    babyRankRoleItem.setSubscribe(userSubscribeMap.getOrDefault(babyUid, false));
                }
            }

            return roleItemMap;
        }
    }

}
