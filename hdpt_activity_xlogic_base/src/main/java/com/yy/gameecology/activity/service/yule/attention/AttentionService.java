package com.yy.gameecology.activity.service.yule.attention;

import com.yy.gameecology.activity.service.yule.attention.domain.pb.attention.*;
import org.apache.dubbo.common.annotation.Yrpc;

/**
 * <AUTHOR>
 * @date 2022/4/18 15:02
 **/
public interface AttentionService {
    @Yrpc(functionName = "cancelAttention", reqUri = 9185, resUri = 9441)
    public CancelAttentionRsp cancelAttention(CancelAttentionReq req);

    @Yrpc(functionName = "queryUserAttentionList", reqUri = 9697, resUri = 9953)
    public QueryUserAttentionListRsp queryUserAttentionList(QueryUserAttentionListReq req);

    @Yrpc(functionName = "queryAttentionCount", reqUri = 10209, resUri = 10465)
    public QueryAttentionCountRsp queryAttentionCount(QueryAttentionCountReq req);

    @Yrpc(functionName = "attention", reqUri = 8673, resUri = 8929)
    public AttentionRsp attention(AttentionReq req);

    @Yrpc(functionName = "queryAttentionRelation", reqUri = 10721, resUri = 10977)
    public QueryAttentionRelationRsp queryAttentionRelation(QueryAttentionRelationReq req);

    @Yrpc(functionName = "queryUserAttentionedList", reqUri = 11233, resUri = 11489)
    public QueryUserAttentionedListRsp queryUserAttentionedList(QueryUserAttentionedListReq req);
}
