package com.yy.gameecology.activity.service.actresult.builderimpl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.service.actresult.ActResultMemberLoader;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.WebdbChannelInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-11-17 16:33
 **/
@Component
public class ChannelActResultBuilder extends BuilderBase  implements ActResultMemberLoader {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    public Map<String, Map<String, MemberInfo>> loadMemberInfo(long actId, long type, long roleType, List<String> memberIds) {
        Map<String, MemberInfo> memberInfoMap = Maps.newHashMap();

        List<Long> sids = Lists.newArrayList();
        memberIds.forEach(x -> {
            sids.add(Convert.toLong(x, 0));
        });
        Map<Long, WebdbChannelInfo> channelInfoMap = webdbThriftClient.batchGetChannelInfo(sids);
        for (long sid : channelInfoMap.keySet()) {
            WebdbChannelInfo channelInfo = channelInfoMap.get(sid);
            MemberInfo memberInfo = new MemberInfo();
            if (channelInfo != null) {
                memberInfo.setName(channelInfo.getName());
                memberInfo.setLogo(WebdbUtils.getLogo(channelInfo));
                memberInfo.setAsid(channelInfo.getAsid());
            } else {
                memberInfo.setName(StringUtils.EMPTY);
                memberInfo.setLogo(StringUtils.EMPTY);
                memberInfo.setAsid(StringUtils.EMPTY);
            }
            memberInfoMap.put(sid + "", memberInfo);
        }

        return ImmutableMap.of(builderKey(type,roleType),memberInfoMap);
    }
}
