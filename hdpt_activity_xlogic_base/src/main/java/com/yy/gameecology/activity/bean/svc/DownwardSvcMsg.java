package com.yy.gameecology.activity.bean.svc;

import com.google.protobuf.CodedInputStream;
import com.google.protobuf.CodedOutputStream;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.util.Assert;

import java.io.IOException;

@Getter
@Setter
@ToString
public class DownwardSvcMsg {

    protected SvcType svcType;

    protected long uid;

    protected long sid;

    protected long ssid;

    protected Template template;

    protected int excludeDanmaku;

    protected GameecologyActivity.GameEcologyMsg message;

    private byte[] newOutputBytes() {
        int size = message.getSerializedSize();
        int extraSize = switch (svcType) {
            case unicast -> CodedOutputStream.computeInt64SizeNoTag(uid);
            case broadcast_top -> CodedOutputStream.computeInt64SizeNoTag(sid);
            case broadcast_sub -> CodedOutputStream.computeInt64SizeNoTag(sid) + CodedOutputStream.computeInt64SizeNoTag(ssid);
            case broadcast_template -> CodedOutputStream.computeInt32SizeNoTag(template.getValue()) + CodedOutputStream.computeInt32SizeNoTag(excludeDanmaku);
            default -> throw new IllegalArgumentException("svc type error");
        };
        return new byte[size + extraSize + 1];
    }

    public byte[] pack() throws IOException {
        Assert.notNull(message, "message can not be null");
        Assert.notNull(svcType, "svcType can not be null");
        byte[] result = newOutputBytes();
        CodedOutputStream outputStream = CodedOutputStream.newInstance(result);
        outputStream.writeRawByte(svcType.typeCode);
        switch (svcType) {
            case unicast:
                outputStream.writeInt64NoTag(uid);
                break;
            case broadcast_top:
                outputStream.writeInt64NoTag(sid);
                break;
            case broadcast_sub:
                outputStream.writeInt64NoTag(sid);
                outputStream.writeInt64NoTag(ssid);
                break;
            case broadcast_template:
                outputStream.writeInt32NoTag(template.getValue());
                outputStream.writeInt32NoTag(excludeDanmaku);
                break;
            default:
                throw new IllegalArgumentException("svc type error");
        }

        message.writeTo(outputStream);
        outputStream.flush();

        return result;
    }

    public void unpack(byte[] bytes) throws IOException {
        CodedInputStream inputStream = CodedInputStream.newInstance(bytes);
        byte b = inputStream.readRawByte();
        this.svcType = SvcType.fromCode(b);
        switch (this.svcType) {
            case unicast:
                this.uid = inputStream.readInt64();
                break;
            case broadcast_top:
                this.sid = inputStream.readInt64();
                break;
            case broadcast_sub:
                this.sid = inputStream.readInt64();
                this.ssid = inputStream.readInt64();
                break;
            case broadcast_template:
                int templateId = inputStream.readInt32();
                this.template = Template.findByValue(templateId);
                this.excludeDanmaku = inputStream.readInt32();
                break;
            default:
                throw new IllegalArgumentException("svc type error");
        }
        this.message = GameecologyActivity.GameEcologyMsg.parseFrom(inputStream);
    }
}
