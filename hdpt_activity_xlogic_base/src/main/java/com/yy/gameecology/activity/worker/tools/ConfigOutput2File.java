package com.yy.gameecology.activity.worker.tools;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yy.gameecology.common.CommDao;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.consts.ComponentId;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @desc 活动配置收集
 *
 * 1）actId 和 types 参数必须出现，all代表所有类型，多个类型值间用 , 隔开， 合法的类型请参考常量：LEGAL_TYPE
 * 2）且cmptIds未指定时表示全部组件id, cmptIdNotFlag 表示取 cmptIds 的反集
 * 3）示例说明：
 * ------------------------------------------------------------------------
 * -DactId=2023021001 -Dtypes=all  <-- 全部类型、全部组件
 * -DactId=2023021001 -Dtypes=ranking,award <-- 仅 ranking/award 类型
 * -DactId=2023021001 -Dtypes=all -DcmptIds=1006,1008  <-- 全部类型、 1006+1008 组件
 *
 * -DactId=2023021001 -Dtypes=ranking -DcmptIds=1006,1008 <-- 仅 ranking 类型，忽略 cmptIds
 * -DactId=2023021001 -Dtypes=xlgcmpt -DcmptIds=1006,1008 <-- 仅 组件类型，且只含 1006 + 1008
 * -DactId=2023021001 -Dtypes=xlgcmpt -DcmptIds=all  <-- 组件类型，包括所有组件
 *
 * -DactId=2023021001 -Dtypes=all -DcmptIdNotFlag=1  <-- 不含组件外的全部类型
 * -DactId=2023021001 -Dtypes=all -DcmptIds=1006,1008 -DcmptIdNotFlag=1 <-- 不含 1006+1008 组件外的全部类型
 *
 * -DactId=2023021001 -Dtypes=ranking -DcmptIds=1006,1008 -DcmptIdNotFlag=1 <-- 仅 ranking 类型，cmptIds、DcmptIdNotFlag 被忽略
 * -DactId=2023021001 -Dtypes=xlgcmpt -DcmptIds=1006,1008 -DcmptIdNotFlag=1 <-- 仅 组件类型，但不包括 1006,1008 组件
 * ------------------------------------------------------------------------
 *
 * <AUTHOR>
 */
public class ConfigOutput2File {

    private static final String TYPE_ALL = "all";
    private static final String TYPE_XLGOTHER = "xlgother";
    private static final String TYPE_XLGCMPT = "xlgcmpt";
    private static final String TYPE_RANKING = "ranking";
    private static final String TYPE_AWARD = "award";
    private static final String TYPE_STREAM = "stream";

    private static final String NOW = DateUtil.today();

    private static final String TPL = "SELECT * FROM %s WHERE %s=%s";

    private static final String TPL_CMPT = "SELECT * FROM %s WHERE %s=%s AND %s=%s";

    private static final Set<String> LEGAL_AUTHOR = Sets.newHashSet("yulianzhu", "wangdonghong", "guoliping", "zengwenzhi", "liqingyang", "guanqihua", "zengyuan", "zhuguosheng");

    private static final Set<String> LEGAL_TYPE = Sets.newHashSet(TYPE_ALL, TYPE_XLGOTHER, TYPE_XLGCMPT, TYPE_RANKING, TYPE_AWARD, TYPE_STREAM);

    private final static String[][] MYSQL_HDZKS = {
            {"jdbc:mysql://*************:6323/hdzk?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"},
            {"jdbc:mysql://*************:6323/hdzk2_west?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"},
            {"*********************************************************************************************************************", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"},
            {"jdbc:mysql://*************:6323/hdzk4_north?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"},
            {"jdbc:mysql://*************:6323/hdzk5_middle?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"},
            {"jdbc:mysql://*************:6323/hdzk6_up?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"},
            {"jdbc:mysql://*************:6323/hdzk7_down?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"}
    };

    private final static String[][] MYSQL_HDZTS = {
            {"jdbc:mysql://*************:6323/hdzt?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"},
            {"jdbc:mysql://*************:6323/hdzt2_west?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"},
            {"jdbc:mysql://*************:6323/hdzt3_south?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"},
            {"jdbc:mysql://*************:6323/hdzt4_north?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"},
            {"jdbc:mysql://*************:6323/hdzt5_middle?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"},
            {"jdbc:mysql://*************:6323/hdzt6_up?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"},
            {"jdbc:mysql://*************:6323/hdzt7_down?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"}
    };

    private final static String[] MYSQL_STREAM = {"*********************************************************************************************************************", "udb_data", "z9tnj5kiev52bH8FoVOVwJLA"};


    public static void main(String[] args) throws Exception {
        String author = System.getProperty("author");
        author = StringUtil.isBlank(author) ? "unknown" : author;
        if(!LEGAL_AUTHOR.contains(author)) {
            // throw new Exception("invlaid author:" + author);
        }

        String types = StringUtil.trim(System.getProperty("types"));
        if(!TYPE_ALL.equals(types)) {
            for (String type : types.split(",")) {
                type = StringUtil.trim(type);
                if (!type.isEmpty() && !LEGAL_TYPE.contains(type)) {
                    throw new Exception("invlaid type:" + type);
                }
            }
        }

        long actId = Long.parseLong(System.getProperty("actId"));
        for(String type : types.split(",")) {
            if(TYPE_ALL.equals(type) || TYPE_XLGOTHER.equals(type)) {
                outputHdzkOther(actId, author);
            }

            if(TYPE_ALL.equals(type) || TYPE_XLGCMPT.equals(type)) {
                List<Long> cmptIds = getComponentIds(actId);
                if(!cmptIds.isEmpty()) {
                    outputHdzkComponent(actId, cmptIds, author);
                }
            }

            if(TYPE_ALL.equals(type) || TYPE_RANKING.equals(type)) {
                outputRanking(actId, author);
            }

            if(TYPE_ALL.equals(type) || TYPE_AWARD.equals(type)) {
                outputAward(actId, author);
            }

            if(TYPE_ALL.equals(type) || TYPE_STREAM.equals(type)) {
                outputStream(actId, author);
            }
        }
    }

    public static void outputHdzkOther(long actId, String author) throws Exception {
        String[][] tableNameField = {
                {"ge_act_attr", "act_id"},
                {"ge_act_info", "act_id"},
                {"hdzj_activity", "act_id"}
        };

        String name = TYPE_XLGOTHER + "-" + actId;
        int group = getActGroup(actId);
        String[] mysqlParams = MYSQL_HDZKS[group - 1];

        process(actId, 0, author, tableNameField, name, mysqlParams);
    }


    public static void outputHdzkComponent(long actId, List<Long> cmptIds, String author) throws Exception {
        int group = getActGroup(actId);
        String[] mysqlParams = MYSQL_HDZKS[group - 1];

        for(Long cmptId : cmptIds) {
            String[][] tableNameField = {
                    {"hdzj_component", "act_id", "cmpt_id"},
                    {"hdzj_component_attr", "act_id", "cmpt_id"},
                    {"hdzj_component_storage_policy", "act_id", "cmpt_id"},
                    {"hdzj_component_ui", "act_id", "cmpt_id"},
            };

            String name = TYPE_XLGCMPT + "-" + actId + "-" + cmptId;
            process(actId, cmptId, author, tableNameField, name, mysqlParams);
        }

        // {"ge_broadcast_timer_config", "act_id"},
        if(cmptIds.contains(ComponentId.MATCH_POINT_NOTIFY)) {
            String[][] tableNameField = {
                    {"ge_broadcast_timer_config", "act_id"},
            };
            String name = TYPE_XLGCMPT + "-" + actId + "-" + ComponentId.MATCH_POINT_NOTIFY + "-mpn";
            process(actId, ComponentId.MATCH_POINT_NOTIFY, author, tableNameField, name, mysqlParams);
        }

        // {"ge_broadcast_rank_result_config", "act_id"},
        if(cmptIds.contains(ComponentId.MATCH_RESULT_NOTIFY)) {
            String[][] tableNameField = {
                    {"ge_broadcast_rank_result_config", "act_id"},
            };
            String name = TYPE_XLGCMPT + "-" + actId + "-" + ComponentId.MATCH_RESULT_NOTIFY + "-mrn";
            process(actId, ComponentId.MATCH_RESULT_NOTIFY, author, tableNameField, name, mysqlParams);
        }

        // {"act_result", "act_id"},
        // {"act_result_group", "act_id"},
        if(cmptIds.contains(ComponentId.HONOR_HALL_RANK_V2)) {
            String[][] tableNameField = {
                    {"act_result", "act_id"},
                    {"act_result_group", "act_id"}
            };
            String name = TYPE_XLGCMPT + "-" + actId + "-" + ComponentId.HONOR_HALL_RANK_V2 + "-rydt";
            process(actId, ComponentId.HONOR_HALL_RANK_V2, author, tableNameField, name, mysqlParams);
        }

        // {"act_layer_view_attr", "act_id"}, - 咨询文职说已废弃
        // {"act_layer_view_define", "act_id"},
        // {"act_role_rank_map", "act_id"},
        if(cmptIds.contains(ComponentId.ACT_LAYER_CONFIG)) {
            String[][] tableNameField = {
                    {"act_layer_view_define", "act_id"},
                    {"act_role_rank_map", "act_id"}
            };
            String name = TYPE_XLGCMPT + "-" + actId + "-" + ComponentId.ACT_LAYER_CONFIG + "-layer";
            process(actId, ComponentId.ACT_LAYER_CONFIG, author, tableNameField, name, mysqlParams);
        }

        // {"act_show_rank_config", "act_id"},
        if(cmptIds.contains(ComponentId.RANKING_SHOW)) {
            String[][] tableNameField = {
                    {"act_show_rank_config", "act_id"},
            };
            String name = TYPE_XLGCMPT + "-" + actId + "-" + ComponentId.RANKING_SHOW + "-jyycgp";
            process(actId, ComponentId.RANKING_SHOW, author, tableNameField, name, mysqlParams);
        }
    }

    public static void outputRanking(long actId, String author) throws Exception {
        String[][] tableNameField = {
                {"hdzt_activity", "act_id"},
                {"hdzt_activity_attr", "act_id"},
                {"hdzt_enroll_policy", "act_id"},
                {"hdzt_role_group", "act_id"},
                {"ranking_config", "act_id"},
                {"ranking_config_attr", "act_id"},
                {"ranking_item", "act_id"},
                {"ranking_item_transform", "act_id"},
                {"ranking_member", "act_id"},
                {"ranking_phase", "act_id"},
                {"ranking_phase_group", "act_id"},
                {"ranking_phase_pk", "dest_act_id"},
                {"ranking_phase_pkgroup", "dest_act_id"},
                {"ranking_phase_qualification", "dest_act_id"},
                {"ranking_role_info_hint", "act_id"},
                {"ranking_task", "act_id"},
                {"ranking_task_award_config", "act_id"},
                {"ranking_task_item", "act_id"}
        };

        String name = TYPE_RANKING + "-" + actId;
        int group = getActGroup(actId);
        String[] mysqlParams = MYSQL_HDZTS[group - 1];

        process(actId, 0, author, tableNameField, name, mysqlParams);
    }

    public static void outputAward(long actId, String author) throws Exception {
        // 奖池
        String sqlAawardTask = "SELECT * FROM award_task where act_id=%s";
        // 抽发奖模型
        String sqlAwardModel = "SELECT * FROM award_model where task_id in (SELECT task_id FROM award_task where act_id=%s)";
        // 奖包
        String sqlAwardPackage = "SELECT * FROM award_package where package_id in (SELECT distinct package_id FROM award_model where task_id in (SELECT task_id FROM award_task where act_id=%s))";
        // 奖项
        String sqlAwardPackageItem = "SELECT * FROM award_package_item where package_id in (SELECT distinct package_id FROM award_model where task_id in (SELECT task_id FROM award_task where act_id=%s))";

        String[][] sqls = {
                {"award_task", String.format(sqlAawardTask, actId)},
                {"award_model", String.format(sqlAwardModel, actId)},
                {"award_package", String.format(sqlAwardPackage, actId)},
                {"award_package_item", String.format(sqlAwardPackageItem, actId)}
        };

        String name = TYPE_AWARD + "-" + actId;
        int group = getActGroup(actId);
        String[] mysqlParams = MYSQL_HDZTS[group - 1];

        process(actId, author, sqls, name, mysqlParams);
    }

    public static void outputStream(long actId, String author) throws Exception {
        List<Long> ruleIds = getRuleIds(actId);
        for(Long ruleId : ruleIds) {
            // "0!" 取巧的办法，确保形成的sql中 0!=${actId} 为true
            String[][] tableNameField = {
                    {"re_rule_script", "0!", "rule_id"},
            };

            String name = TYPE_STREAM + "-" + actId + "-" + ruleId;
            process(actId, ruleId, author, tableNameField, name, MYSQL_STREAM);
        }
    }

    /**
     * 获取组件ID，要考察组件ID否定标记
     */
    private static List<Long> getComponentIds(long actId) throws Exception {
        String types = StringUtil.trim(System.getProperty("types"));
        String cmptIds = StringUtil.trim(System.getProperty("cmptIds"));
        cmptIds = (TYPE_ALL.equals(types) && cmptIds.isEmpty()) ? TYPE_ALL : cmptIds;

        // 组件ID否定标记： 1-不包括cmptIds属性上出现的组件ID， 空或非1-包括（默认值）
        boolean cmptIdNotFlag = "1".equals(System.getProperty("cmptIdNotFlag"));

        List<Long> allCmptIds = getAllComponentIds(actId);
        if (TYPE_ALL.equals(cmptIds)) {
            return cmptIdNotFlag ? Lists.newArrayList() : allCmptIds;
        }

        List<Long> result = Lists.newArrayList();
        for (String str : cmptIds.split(",")) {
            if (StringUtil.isPureNumber(str)) {
                result.add(Long.parseLong(str));
            }
        }

        return cmptIdNotFlag
                ? allCmptIds.stream().filter(cmptId -> !result.contains(cmptId)).collect(Collectors.toList())
                : result;
    }

    private static List<Long> getAllComponentIds(long actId) throws Exception {
        List<Long> result = Lists.newArrayList();
        String sql = "SELECT distinct cmpt_id FROM hdzj_component where act_id=" + actId;
        int group = getActGroup(actId);
        String[] mysqlParams = MYSQL_HDZKS[group - 1];
        Connection conn = getConnection(mysqlParams);
        List<Map<String, Object>> list = CommDao.queryForList(conn, sql);
        for (Map<String, Object> map : list) {
            result.add(Convert.toLong(map.get("cmpt_id")));
        }
        return result;
    }

    private static List<Long> getRuleIds(long actId) throws Exception {
        List<Long> result = Lists.newArrayList();
        String sql = "SELECT distinct rule_id FROM re_rule_param where event_act_id=" + actId;
        Connection conn = getConnection(MYSQL_STREAM);
        List<Map<String, Object>> list = CommDao.queryForList(conn, sql);
        for (Map<String, Object> map : list) {
            result.add(Convert.toLong(map.get("rule_id")));
        }
        return result;
    }


    public static String getOutputPath(long actId) {
        String path = ConfigOutput2File.class.getProtectionDomain().getCodeSource().getLocation().getPath();
        int inx = path.indexOf("hdpt_activity_xlogic_base");
        path = path.substring(0, inx);
        path += "hdpt_activity_xlogic_app";
        path = path + "/src/main/java/com/yy/gameecology/y" + String.valueOf(actId).substring(0, 4) + "/act" + actId + "/actcfg";
        File file = new File(path);
        if(!file.exists()) {
            file.mkdirs();
        }
        return path;
    }

    public static int getActGroup(long actId) {
        // 2023023001 -> 3001 -> 3
        int length = String.valueOf(actId).length();
        if(10 != length) {
            throw new RuntimeException("actId length must equal 10, your length:" + length);
        }

        char c = String.valueOf(actId).charAt(6);
        char zero = '0';
        if(c == zero) {
            throw new RuntimeException("actId group can't be 0");
        }

        String s = String.valueOf(c);
        return Integer.parseInt(s);
    }



    private static void process(long actId, long id, String author, String[][] tableNameField, String name, String[] mysqlParams) throws Exception {
        Connection conn = getConnection(mysqlParams);
        String outputPath = getOutputPath(actId);
        File file = new File(outputPath + "/" + name + ".cfg");

        List<String> all = Lists.newArrayList();
        for(String[] nameField : tableNameField) {
            // 无条件先删除，防止堆积空文件
            file.delete();

            // 查出所有数据, 空内容的跳过
            String sql = nameField.length == 2 ?
                    String.format(TPL, nameField[0], nameField[1], actId):
                    String.format(TPL_CMPT, nameField[0], nameField[1], actId, nameField[2], id);
            List<Map<String, Object>> list = CommDao.queryForList(conn, sql);
            if(list.size() != 0) {
                all.add("[" + nameField[0] + ", " + nameField[1] + ", " + actId + "," + id + "]");
                all.addAll(convert(list));
                all.add("");
            }
        }

        // 若有实际内容, 写文件
        if(!all.isEmpty()) {
            all.add(0, "created time " + NOW + "\n");
            FileUtils.writeLines(file, all);
        }
    }

    private static void process(long actId, String author, String[][] sqls, String name, String[] mysqlParams) throws Exception {
        Connection conn = getConnection(mysqlParams);
        String outputPath = getOutputPath(actId);
        File file = new File(outputPath + "/" + name + ".cfg");

        // 无条件先删除，防止堆积空文件
        file.delete();

        List<String> all = Lists.newArrayList();
        for(String[] sql : sqls) {
            all.add("\n----------------------------[ " + sql[0] + " ]--------------------------------\n");
            List<Map<String, Object>> list = CommDao.queryForList(conn, sql[1]);
            if(list.isEmpty()) {
                all.add("nothing!");
                all.add("");
            } else {
                all.addAll(convert(list));
                all.add("");
            }
        }

        // 若有实际内容, 写文件
        if(!all.isEmpty()) {
            all.add(0, "created time " + NOW + "\n");
            FileUtils.writeLines(file, all);
        }
    }


    public static List<String> list(Connection connection, String sql) {
        List<Map<String, Object>> list = CommDao.queryForList(connection, sql);
        List<String> result = Lists.newArrayList();
        for (Map<String, Object> map : list) {
            result.add(JSON.toJSONString(map));
        }
        return result;
    }

    public static Connection getConnection(String[] params) throws Exception {
        return getConnection(params[0], params[1], params[2]);
    }

    public static Connection getConnection(String url, String user, String password) throws Exception {
        Class.forName("com.mysql.jdbc.Driver");
        return DriverManager.getConnection(url, user, password);
    }

    /**
     * 假设只有 hdzt_stream.re_rule_script 表含有 rule_script 字段， 对 rule_script 不做 json 转换，以原有格式呈现数据
     * @param list
     * @return
     */
    public static List<String> convert(List<Map<String, Object>> list) {
        // 先去掉 rule_script 头
        String fnRuleScript = "rule_script";
        List<String> result = Lists.newArrayList();
        List<String> keys = list.get(0).keySet().stream().filter(item -> !item.equals(fnRuleScript)).collect(Collectors.toList());
        result.add(JSON.toJSONString(keys));

        // 已json格式输出不含 rule_script 的记录
        for (Map<String, Object> map : list) {
            List<Object> objects = Lists.newArrayList();
            for(String key: keys) {
                objects.add(map.get(key));
            }
            result.add(JSON.toJSONString(objects));
        }

        // 已原生格式输出 rule_script、rule_id
        for (Map<String, Object> map : list) {
            if(map.containsKey(fnRuleScript)) {
                result.add("\n---------- " + map.get("rule_id") + " -----------\n");
                result.add(String.valueOf(map.get(fnRuleScript)));
            }
        }

        return result;
    }
}
