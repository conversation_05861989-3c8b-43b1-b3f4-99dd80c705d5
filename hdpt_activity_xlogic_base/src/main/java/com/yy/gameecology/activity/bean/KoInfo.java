package com.yy.gameecology.activity.bean;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/11/15 19:02
 * @Modified:
 */
public class KoInfo {

    private String memberKey;

    private long diff;

    private long startTime = 0L;
    private long endTime = 0L;

    private String winnerMemberId;
    private long winnerScore;
    private Integer winnerRank;

    private String loserMemberId;
    private long loserScore;
    private Integer loserRank;

    public String getMemberKey() {
        return memberKey;
    }

    public void setMemberKey(String memberKey) {
        this.memberKey = memberKey;
    }

    public long getDiff() {
        return diff;
    }

    public void setDiff(long diff) {
        this.diff = diff;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public String getWinnerMemberId() {
        return winnerMemberId;
    }

    public void setWinnerMemberId(String winnerMemberId) {
        this.winnerMemberId = winnerMemberId;
    }

    public long getWinnerScore() {
        return winnerScore;
    }

    public void setWinnerScore(long winnerScore) {
        this.winnerScore = winnerScore;
    }

    public Integer getWinnerRank() {
        return winnerRank;
    }

    public void setWinnerRank(Integer winnerRank) {
        this.winnerRank = winnerRank;
    }

    public String getLoserMemberId() {
        return loserMemberId;
    }

    public void setLoserMemberId(String loserMemberId) {
        this.loserMemberId = loserMemberId;
    }

    public long getLoserScore() {
        return loserScore;
    }

    public void setLoserScore(long loserScore) {
        this.loserScore = loserScore;
    }

    public Integer getLoserRank() {
        return loserRank;
    }

    public void setLoserRank(Integer loserRank) {
        this.loserRank = loserRank;
    }
}
