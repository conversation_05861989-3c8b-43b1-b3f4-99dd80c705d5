package com.yy.gameecology.activity.bean.event;

import lombok.Data;

/**
 * desc:追玩APP通用弹窗
 * 临时方案：最终方案看活动和APP Service通道直接打通！！！
 *
 * <AUTHOR>
 * @date 2023-02-24 16:00
 **/
@Data
public class AppPopUpEvent {
    private String seq;
    /**
     * mq 生产时间
     */
    private long productTime;

    /**
     * 弹窗url
     */
    private String popUrl;

    /**
     * 弹窗uid
     */
    private Long uid;

    /**
     * 透传给客户端的上线文信息
     */
    private String context;

    /**
     * 扩展字段
     */
    private String ext;
}
