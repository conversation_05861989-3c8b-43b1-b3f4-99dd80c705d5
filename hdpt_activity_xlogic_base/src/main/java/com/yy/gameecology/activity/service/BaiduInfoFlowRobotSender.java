package com.yy.gameecology.activity.service;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.ruliu.BaiduInfoFlowAtMsg;
import com.yy.gameecology.activity.bean.ruliu.BaiduInfoFlowMarkdownMsg;
import com.yy.gameecology.activity.bean.ruliu.BaiduInfoFlowMsg;
import com.yy.gameecology.activity.bean.ruliu.BaiduInfoFlowTextMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-10-08 16:55
 **/
@Service
public class BaiduInfoFlowRobotSender {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


   private static final String BAIDU_IM_API_PROXY_DOMAIN = "baidu-im-api-proxy.yy.com";

   /**
    * 替换代理域名（百度云专区才可使用如流原始域名）
    *
    * @param url url
    * @return 返回代理域名
    */
   private String replaceProxyDomain(String url) {
      return url.replace("apiin.im.baidu.com", BAIDU_IM_API_PROXY_DOMAIN);
   }

   /**
    * 发送文本类型如流消息
    *
    * @param robotWebhook 如流机器人 Webhook
    * @param msg          文本消息内容
    */
   public void sendTextMsg(String robotWebhook, String msg) {
      sendTextMsg(robotWebhook, msg, false, null);
   }

   /**
    * 发送文本类型如流消息
    *
    * @param robotWebhook 如流机器人 Webhook
    * @param msg          文本消息内容
    * @param atall        @全体成员
    * @param atUserIds    要 @谁，使用百度邮箱前缀
    */
   public void sendTextMsg(String robotWebhook, String msg, boolean atall, List<String> atUserIds) {
      List<BaiduInfoFlowMsg> msgList;
      if (!atall && CollectionUtils.isEmpty(atUserIds)) {
         msgList = Lists.newArrayList(new BaiduInfoFlowTextMsg(msg));
      } else {
         msgList = Lists.newArrayList(new BaiduInfoFlowTextMsg(msg), new BaiduInfoFlowAtMsg(atall, atUserIds));
      }
      send(robotWebhook, msgList);
   }

   /**
    * 发送 Markdown 类型如流消息
    *
    * @param robotWebhook 如流机器人 Webhook
    * @param msg          文本消息内容
    */
   public void sendMarkdownMsg(String robotWebhook, String msg) {
      sendMarkdownMsg(robotWebhook, msg, false, null);
   }

   /**
    * 发送 Markdown 类型如流消息
    *
    * @param robotWebhook 如流机器人 Webhook
    * @param msg          文本消息内容
    * @param atall        @全体成员
    * @param atUserIds    要 @谁，使用百度邮箱前缀
    */
   public void sendMarkdownMsg(String robotWebhook, String msg, boolean atall, List<String> atUserIds) {
      List<BaiduInfoFlowMsg> msgList;
      if (!atall && CollectionUtils.isEmpty(atUserIds)) {
         msgList = Lists.newArrayList(new BaiduInfoFlowMarkdownMsg(msg));
      } else {
         msgList = Lists.newArrayList(new BaiduInfoFlowMarkdownMsg(msg), new BaiduInfoFlowAtMsg(atall, atUserIds));
      }
      send(robotWebhook, msgList);
   }

   /**
    * 发送消息
    *
    * @param robotWebhook 如流机器人 Webhook
    * @param msgList      消息列表
    */
   public void send(String robotWebhook, List<BaiduInfoFlowMsg> msgList) {
      Assert.isTrue(!CollectionUtils.isEmpty(msgList), "msgList is required");
      robotWebhook = replaceProxyDomain(robotWebhook);

      if (msgList.size() == 1) {
         doSend(robotWebhook, msgList);
         return;
      }

      // 注意，Markdown 消息，不能和其他消息一起发送，因此需要拆开多次发送
      boolean containMarkdownMsg = false;
      for (BaiduInfoFlowMsg msg : msgList) {
         if (msg instanceof BaiduInfoFlowMarkdownMsg) {
            containMarkdownMsg = true;
            break;
         }
      }
      if (!containMarkdownMsg) {
         doSend(robotWebhook, msgList);
         return;
      }

      // 拆分多个列表
      List<List<BaiduInfoFlowMsg>> msgListList = Lists.newArrayListWithExpectedSize(2);
      List<BaiduInfoFlowMsg> tempList = Lists.newArrayList();
      for (BaiduInfoFlowMsg msg : msgList) {
         if (msg instanceof BaiduInfoFlowMarkdownMsg) {
            if (!tempList.isEmpty()) {
               msgListList.add(tempList);
            }
            msgListList.add(Collections.singletonList(msg));
            tempList = Lists.newArrayList();
         } else {
            tempList.add(msg);
         }
      }
      if (!tempList.isEmpty()) {
         msgListList.add(tempList);
      }
      for (List<BaiduInfoFlowMsg> list : msgListList) {
         doSend(robotWebhook, list);
      }
   }

   public void doSend(String robotWebhook, List<BaiduInfoFlowMsg> msgList) {
      Map<String, Object> message = Collections.singletonMap("message", Collections.singletonMap("body", msgList));
      Map<String, String> headers = Maps.newHashMapWithExpectedSize(1);
      headers.put("Content-Type", "application/json;charset=UTF-8");
      String rspBody = HttpUtil.createPost(robotWebhook).body(JSON.toJSONString(message))
              .addHeaders(headers)
              .execute().body();
      log.info("sendBaiduMsg rsp:{}", rspBody);
      JSONObject object = JSON.parseObject(rspBody);
      Assert.isTrue(Objects.equals(0, object.getIntValue("errcode")), object.getString("errmsg"));
   }
}
