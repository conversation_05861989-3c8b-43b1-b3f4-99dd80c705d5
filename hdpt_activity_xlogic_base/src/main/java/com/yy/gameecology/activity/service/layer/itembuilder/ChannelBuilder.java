package com.yy.gameecology.activity.service.layer.itembuilder;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.*;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.db.model.gameecology.ActLayerViewDefine;
import com.yy.gameecology.common.db.model.gameecology.ActResult;
import com.yy.gameecology.common.db.model.gameecology.ActRoleRankMap;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.element.component.attr.ActLayerConfigComponentAttr;
import com.yy.thrift.hdztranking.*;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-07-14 11:20
 **/
@Component
public class ChannelBuilder extends ActLayerInfoService implements LayerItemBuilder {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 组装频道、子频道
     */
    @Override
    public Set<String> getItemKeys() {
        return Set.of(LayerItemTypeKey.CHANNEL, LayerItemTypeKey.CHANNEL_2, LayerItemTypeKey.CHANNEL_3
                , LayerItemTypeKey.HALL, LayerItemTypeKey.PEIWAN_TUAN, LayerItemTypeKey.ROOM, LayerItemTypeKey.ROOM2, LayerItemTypeKey.HALL_2);

    }

    @Override
    public List<String> getMemberIds(ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, OnlineChannelInfo onlineChannel) {
        //频道
        if (RoleType.GUILD.getValue() == viewDefine.getRoleType()) {
            return Lists.newArrayList(onlineChannel.getSid() + "");
        }
        //厅
        else if (RoleType.HALL.getValue() == viewDefine.getRoleType()) {
            Integer topN = viewDefine.getShowTopN();
            Date now = commonService.getNow(actInfo.getActId());
            if (topN == null || topN <= 1) {
                String subChannelMemberId = onlineChannel.getSid() + "_" + onlineChannel.getSsid();
                return Lists.newArrayList(subChannelMemberId);
            } else {
                return getSubChannelTopNMemberId(actInfo.getActId(), viewDefine, busiId, onlineChannel.getSid(), now, topN);
            }
        }
        //陪玩-top N 团
        else if (RoleType.PWTUAN.getValue() == viewDefine.getRoleType()) {
            int topN = viewDefine.getShowTopN();
            Date now = commonService.getNow(actInfo.getActId());
            return getGroupTopNMemberId(actInfo.getActId(), viewDefine, busiId, onlineChannel.getSid(), now, topN);
        } else if (RoleType.ROOM.getValue() == viewDefine.getRoleType()) {
            RoomInfo roomInfo = commonService.getRoomInfoBySsid(Convert.toLong(onlineChannel.getSsid()));
            return Lists.newArrayList(roomInfo.getRoomId() + "");
        }

        return null;
    }

    @Override
    public List<LayerMemberItem> build(Date now, ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, List<String> memberIds, Map<String, Object> ext) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return null;
        }
        List<LayerMemberItem> result = Lists.newArrayList();
        for (int i = 0; i < memberIds.size(); i++) {
            LayerMemberItem member = getChannelItemInfo(actInfo, viewDefine, busiId, memberIds.get(i), now);
            if (member != null) {
                member.setItemType(viewDefine.getItemTypeKey());
                member.setIndex(i);
                member.setSort(i);
                //扩展信息
                Map<String, Object> itemExtInfo = actLayerInfoExtService.extendLayerMemberItem(actInfo.getActId(), member);
                if (MapUtils.isNotEmpty(itemExtInfo)) {
                    member.getExt().putAll(itemExtInfo);
                }
                result.add(member);
            }
        }
        return result;
    }

    @Override
    public void fillLayerBroadcastInfo(ActLayerViewDefine viewDefine, LayerBroadcastInfo target, List<LayerMemberItem> source, List<String> memberIds, Map<String, Object> ext) {
        if (CollectionUtils.isEmpty(source)) {
            return;
        }
        switch (viewDefine.getItemTypeKey()) {
            case LayerItemTypeKey.CHANNEL:
                target.setChannelInfo(source.get(0));
                break;
            case LayerItemTypeKey.HALL:
                //房间代替厅
            case LayerItemTypeKey.ROOM:
                target.setSubChannelInfo(source.get(0));
                break;
            case LayerItemTypeKey.PEIWAN_TUAN:
                target.setTopNGroupInfo(source);
                break;
            default:
                target.getExtMemberItem().addAll(source);
        }

    }

    @Override
    public List<LayerMemberItem> buildSimpleInfo(Date now, ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine,

                                                 List<String> memberIds) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return null;
        }
        List<LayerMemberItem> result = Lists.newArrayList();
        for (int i = 0; i < memberIds.size(); i++) {
            String memberId = memberIds.get(i);

            //---构造查询成员积分参数
            ActorQueryItem actorStatusPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.NORMAL, now);

            //如果为空,尝试获取总榜
            if (actorStatusPara == null) {
                actorStatusPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.TOTAL, now);
            }
            if (actorStatusPara == null) {
                continue;
            }

            //-----查出成员积分
            ActorInfoItem actorItem = hdztRankingThriftClient.queryActorRankingInfo(actInfo.getActId(), actorStatusPara);
            if (actorItem == null) {
                continue;
            }

            LayerMemberItem itemRes = new LayerMemberItem();
            itemRes.setMemberId(memberId);
            itemRes.setCurRankId(actorItem.getRankingId());
            itemRes.setCurPhaseId(actorItem.getPhaseId());

            result.add(itemRes);
        }
        return result;
    }

    @Override
    public void fillSimpleInfo(ActLayerViewDefine viewDefine, LayerBroadcastInfo target, List<LayerMemberItem> source) {
        if (CollectionUtils.isEmpty(source)) {
            return;
        }
        switch (viewDefine.getItemTypeKey()) {
            case LayerItemTypeKey.CHANNEL:
                target.setChannelInfo(source.get(0));
                break;
            case LayerItemTypeKey.ROOM:
                target.setChannelInfo(source.get(0));
                break;
            case LayerItemTypeKey.HALL:
                target.setSubChannelInfo(source.get(0));
                break;
            default:
                break;
        }
    }

    //频道/厅/团
    public LayerMemberItem getChannelItemInfo(ActivityInfoVo actInfo,
                                              ActLayerViewDefine viewDefine,
                                              Long busiId,
                                              String memberId,
                                              Date now) {
        Clock clock = new Clock();

        if (StringUtil.isEmpty(memberId)) {
            return null;
        }

        Long actId = actInfo.getActId();

        LayerMemberItem itemRes = new LayerMemberItem();
        itemRes.setMemberId(memberId);
        itemRes.setItemType(viewDefine.getItemTypeKey());
        Integer roleType = viewDefine.getRoleType();
        itemRes.setRoleType(roleType);
        //成员基本信息
        MemberInfo memberInfo = memberInfoService.getMemberInfo(busiId, RoleType.findByValue(roleType), memberId);
        if (memberInfo != null) {
            itemRes.setNickName(Base64Utils.encodeToString(Convert.toString(memberInfo.getName()).getBytes()));
            itemRes.setLogo(memberInfo.getLogo() == null ? "" : memberInfo.getLogo());
            String roleName = enrollmentService.getFirstEnrolDestRoleName(actInfo.getActId(), busiId, roleType, memberId);
            itemRes.setRoleName(roleName);
        }

        clock.tag();
        EnrollmentInfo enrollmentInfo = enrollmentService.tryGetFirstEnrolMemberCache(actId, busiId, roleType, memberId);
        if (enrollmentInfo != null) {
            itemRes.setAsid(commonService.getAsid(enrollmentInfo.getSignSid()) + "");
            itemRes.setRoleId(enrollmentInfo.getDestRoleId());
        }

        clock.tag();

        ActResult actResult = getNotLastPhaseTitle(now.getTime(), viewDefine.getItemTypeKey(), actId, roleType, memberId);
        if (actResult != null) {
            itemRes.setShowHonorTitle(true);
            itemRes.setLastPhaseTopTitle(actResult.getTitle());
        }

        clock.tag();

        //结算状态
        if (inSettle(actId, now)) {
            itemRes.setSettleStatus(1);
            return itemRes;
        }


        //---构造查询成员积分参数
        ActorQueryItem actorStatusPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.NORMAL, now);

        //如果为空,尝试获取总榜
        if (actorStatusPara == null) {
            actorStatusPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.TOTAL, now);
        }
        if (actorStatusPara == null) {
            if (enrollmentInfo == null) {
                itemRes.setState(ActorInfoStatus.NOT_IN);
            } else {
                //不在赛程内
                itemRes.setState(ActorInfoStatus.NOT_IN_PHASE);
            }
            return itemRes;
        }

        clock.tag();

        //-----查出成员积分
        ActorInfoItem actorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, actorStatusPara);

        clock.tag();

        ActLayerConfigComponentAttr layerAttr = layerConfigService.getLayerAttrConfig(actId);
        //日榜，展示每日top n
        String prePhaseTopNTitle = getPrePhaseTopNTitle(now, actId, roleType, actorItem, layerAttr);
        if (StringUtil.isNotBlank(prePhaseTopNTitle)) {
            itemRes.setShowHonorTitle(true);
            itemRes.setLastPhaseTopTitle(prePhaseTopNTitle);
        }

        if (actorItem != null) {
            Long rankId = actorItem.getRankingId();
            itemRes.setCurRankId(rankId);
            itemRes.setCurPhaseId(actorItem.getPhaseId());

            PhaseInfo curPhaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, actorItem.getPhaseId());
            itemRes.setCurPhaseInfo(curPhaseInfo);

            itemRes.setScore(actorItem.getScore());
            //对象状态 -1非参赛角色（没在分组名单） 0-正常状态（在晋级线以上） 1-代表有危险（在晋级线以下） 2-被淘汰
            itemRes.setState(actorItem.getStatus());
            //距离上一名
            itemRes.setOffsetScore(actorItem.getRank() != 1 ? actorItem.getPreScore() - actorItem.getScore() : 0);
            itemRes.setSort(0);
            itemRes.setRank(Convert.toInt(actorItem.getRank(), 0));
            RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, rankId, now, actorItem.getPhaseId());
            itemRes.setCurRankName(rankingInfo.getRankingName());
            itemRes.setCurRankNameShow(rankingInfo.getRankingNameShow());
            itemRes.setCurRankExtJson(rankingInfo.getRankingExtjson());
            //N进X
            String passDesc = getPassDescContent(rankingInfo);
            itemRes.setPassDesc(passDesc);
            long leftSeconds = getPhaseLeftSeconds(rankingInfo, curPhaseInfo, now);
            itemRes.setLeftSeconds(leftSeconds);

            if (!itemRes.isShowHonorTitle()) {
                String topTitle = getLastPhaseTopTitle(rankingInfo, actorItem.getPhaseId(), actorItem.getRank());
                itemRes.setLastPhaseTopTitle(topTitle);
            }

            boolean inDrawLots = hdztPhaseConfigService.isInDrawLots(now.getTime(), curPhaseInfo);
            itemRes.setInDrawLots(inDrawLots);

            /*
            ！！！ 活动产品 左嘉伟 要求抽签状态只由 静态配置的时间段来控制，不看  PK对阵中台提交状态 - modified by guo liping / 2022-11-02
                  以后若需要了， 可打开下面这段代码，或者有活动定制服务类中定制实现也行。

            // 对在抽签时间内的，需要判断是否已经完成抽签，若已完成也设置抽签状态为 false - added by guoliping / 2022-10-31
            if(inDrawLots) {
                // PkInfo.settleStatus 结算状态: 0是不需要结算 ,-1待结算 1结算完成, 只获取pk结算状态时不需要 pkConfigDateStr 的值，所以这里可固定的给 "" 串
                PkInfo pkInfo = hdztRankingThriftClient.queryPhasePkgroup(actId, rankId, actorItem.getPhaseId(), "");
                if (pkInfo != null && pkInfo.getSettleStatus() == 1L) {
                    itemRes.setInDrawLots(false);
                }
            }
            */

            //公会小时榜(和前端协商,0的时候不展示)
            itemRes.setHourRank(0);
            ActorQueryItem dayRankQuery = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.DAY, now, false);
            if (dayRankQuery != null) {
                RankingInfo dayRankingInfo = hdztRankingThriftClient.queryRankConfig(actId, dayRankQuery.getRankingId());
                //在采集榜单事件内
                if (inHourRank(dayRankingInfo, now)) {
                    ActorInfoItem hourActor = hdztRankingThriftClient.queryActorRankingInfo(actId, dayRankQuery);
                    itemRes.setHourRank(Convert.toInt(hourActor.getRank()));
                }
            }

            //总榜荣耀值和排名
            ActorQueryItem queryTotalActorPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.TOTAL, now);
            if (queryTotalActorPara != null) {
                ActorInfoItem totalActorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, queryTotalActorPara);
                if (totalActorItem != null) {
                    itemRes.setTotalRank(Convert.toInt(totalActorItem.getRank(), 0));
                    itemRes.setTotalScore(totalActorItem.getScore());
                }

                if (StringUtil.isEmpty(itemRes.getLastPhaseTopTitle())) {
                    RankingInfo totalRankingInfo = hdztRankingThriftClient.queryRankConfig(actId, queryTotalActorPara.getRankingId()
                            , now, queryTotalActorPara.getPhaseId());
                    String topTitle = getLastPhaseTopTitle(totalRankingInfo, queryTotalActorPara.getPhaseId(), totalActorItem.getRank());
                    itemRes.setLastPhaseTopTitle(topTitle);
                }
            }

            // 日排行日荣耀值
            ActorQueryItem queryDayActorPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.DAY, now);
            if (queryDayActorPara != null) {
                ActorInfoItem dayActorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, queryDayActorPara);
                if (dayActorItem != null) {
                    itemRes.setDayRank(Convert.toInt(dayActorItem.getRank(), 0));
                    itemRes.setDayScore(dayActorItem.getScore());
                }
            }

            //获取日排名奖励积分
            ActorQueryItem queryAwardPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.AWARD, now);
            if (queryAwardPara != null) {
                int addScore = getDayRankAwardScore(actId, queryAwardPara.getRankingId(), queryAwardPara.getPhaseId(), itemRes.getDayRank());
                itemRes.setAddScore(addScore);
            }

            clock.tag();


            //pk信息
            PKInfo pkInfo = getPkInfo(actId, viewDefine, busiId, memberId, now, null);
            //结算中(抽签中优先级大于结算中)
            if (pkInfo != null && pkInfo.isInSettle() && !inDrawLots) {
                itemRes.setSettleStatus(1);
                return itemRes;
            }
            if (pkInfo != null) {
                itemRes.setPkInfo(pkInfo);
            }

            clock.tag();

            //分组排名 GroupInfo
            ActorQueryItem queryGroupPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.GROUP, now);
            if (queryGroupPara != null) {
                GroupInfo groupInfo = getGroupInfo(actId, queryGroupPara.getRankingId(), queryGroupPara.getPhaseId(), memberId, queryGroupPara.getDateStr(), queryAwardPara.getExtData());
                if (groupInfo != null) {
                    itemRes.setGroupName(groupInfo.getGroupName());
                    itemRes.setGroupRank(groupInfo.getRank());
                    itemRes.setGroupOffsetScore(groupInfo.getGroupOffsetScore());
                    itemRes.setPassDesc(groupInfo.getPassDesc());
                    itemRes.setRank(groupInfo.getRank());
                    //1、 晋级结算到新的阶段，当榜单第一名分数为0，所有人都是正常状态，排名为-- ;
                    //2、榜单第一名出现分数后，按照实际排名(有的有可能是0分,但是继承了上一阶段的排名)展示排名和危险状态
                    if (groupInfo.isAllScoreIsZero()) {
                        itemRes.setGroupRank(-1);
                        if (itemRes.getState() == 1) {
                            itemRes.setState(0);
                        }
                    }
                    //前端展示需要，榜单名称加上分组名称
                    //itemRes.setCurRankName(itemRes.getCurRankName() + groupInfo.getGroupName());
                    itemRes.setCurRankName(groupInfo.getGroupName());
                    itemRes.setCurRankNameShow(rankingInfo.getRankingNameShow() + groupInfo.getGroupName());
                }
            }

            //复活赛需要查询上一阶段信息
            if (curPhaseInfo != null && isResurrection(curPhaseInfo.getExtJson())) {
                ActorQueryItem prePara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.NORMAL, now, true);
                ActorInfoItem preActorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, prePara);
                if (preActorItem != null) {
                    itemRes.setPrePhaseState(preActorItem.getStatus());
                }
            }

            //最后一个阶段晋级结算展示top n (和最后一阶段n进x的n是同一个标识)
            int lastPhaseTopN = curPhaseInfo != null ? getLastPhaseTopN(rankingInfo, curPhaseInfo.getPhaseId()) : 0;
            itemRes.setLastPhaseTopN(lastPhaseTopN);

        }


        return itemRes;
    }


    /**
     * 取top n 团id,即公会下面的团对应的主榜的top n （挂件需要展示top10的团）
     */
    protected List<String> getGroupTopNMemberId(Long actId, ActLayerViewDefine viewDefine, Long busiId, Long sid, Date now, int topN) {
        ActRoleRankMap actRoleRankMap = actRoleRankMapService.getActRoleRankMap(actId, viewDefine.getItemTypeKey(), "", HdztRoleId.PW_TUAN, RankMapType.TOP_1_SUPPORT, now);
        if (actRoleRankMap == null) {
            return null;
        }
        Map<String, String> ext = Maps.newHashMap();

        List<Rank> ranks = new ArrayList<>();
        String dateStr = "";
        if (StringUtil.isNotBlank(actRoleRankMap.getDateKey())) {
            dateStr = DateUtil.format(now, actRoleRankMap.getDateKey());
        }

        //通常用于有晋级关系的非海选赛
        if (actRoleRankMap.getScoreSource() == RoleRankMapScoreSource.PROMOT.code) {
            ranks = hdztRankingThriftClient.queryRanking(actId, actRoleRankMap.getRankId(), actRoleRankMap.getPhaseId(), dateStr, 5000, ext);
        } else if (actRoleRankMap.getScoreSource() == RoleRankMapScoreSource.PK.code) {
            ext.put(RankExtParaKey.RANK_SCORE_SOURCE, HdztRankType.CONTEST_PK);
            ranks = hdztRankingThriftClient.queryRanking(actId, actRoleRankMap.getRankId(), actRoleRankMap.getPhaseId(), dateStr, 5000, ext);
        }
        //无晋级关系-贡献榜,通常用于海选赛
        else if (actRoleRankMap.getScoreSource() == RoleRankMapScoreSource.CONTRIBUTE.code) {
            ext.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, sid.toString());
            ranks = hdztRankingThriftClient.queryRanking(actId, actRoleRankMap.getRankId(), actRoleRankMap.getPhaseId(), dateStr, topN, ext);
        }

        Set<String> guildTeams = enrollmentService.getChannelTuan(actId, sid);
        List<String> members = new ArrayList<>();
        for (Rank rank : ranks) {
            if (guildTeams.contains(rank.getMember())) {
                members.add(rank.getMember());
            }
        }
        if (logLayerInfo(actId, sid)) {
            log.info("guildTeams :{}, ranks:{}", guildTeams, ranks.stream().map(Rank::getMember).collect(Collectors.joining()));
        }

        return MyListUtils.subList(members, 0, topN);
    }

    /**
     * 获取top n 子频道,先根据公会id获取对应贡献榜
     **/
    private List<String> getSubChannelTopNMemberId(Long actId, ActLayerViewDefine viewDefine, Long busiId
            , Long sid, Date now, int topN) {
        ActRoleRankMap actRoleRankMap = actRoleRankMapService.getActRoleRankMap(actId, viewDefine.getItemTypeKey()
                , "", HdztRoleId.PW_SUB_CHANNEL, RankMapType.TOP_1_SUPPORT, now);
        if (actRoleRankMap == null) {
            return null;
        }
        Map<String, String> ext = Maps.newHashMap();

        List<Rank> ranks = new ArrayList<>();
        String dateStr = "";
        if (StringUtil.isNotBlank(actRoleRankMap.getDateKey())) {
            dateStr = DateUtil.format(now, actRoleRankMap.getDateKey());
        }

        //通常用于有晋级关系的非海选赛
        if (actRoleRankMap.getScoreSource() == RoleRankMapScoreSource.PROMOT.code) {
            ranks = hdztRankingThriftClient.queryRanking(actId, actRoleRankMap.getRankId(), actRoleRankMap.getPhaseId(), dateStr, 5000, ext);
        } else if (actRoleRankMap.getScoreSource() == RoleRankMapScoreSource.PK.code) {
            ext.put(RankExtParaKey.RANK_SCORE_SOURCE, HdztRankType.CONTEST_PK);
            ranks = hdztRankingThriftClient.queryRanking(actId, actRoleRankMap.getRankId(), actRoleRankMap.getPhaseId(), dateStr, 5000, ext);
        }
        // 无晋级关系-贡献榜,通常用于海选赛
        // 配置榜单的时候需要一个辅助榜：当前公会的子频道贡献榜
        // 然后在 act_role_rank_map 表中将当前阶段的数据来源,配置成贡献榜
        // 如果赛程的第一阶段没有限制白名单,则参赛的用户范围会比较多
        // 而挂件是在频道内展示的,已经限定了公会id,此时可以通过公会id来进行第一步筛选,减少数据量
        // 基于此思路,需要额外的贡献榜,以便圈出改公会下的子频道
        // 对于陪玩团/陪玩也是这个思路
        else if (actRoleRankMap.getScoreSource() == RoleRankMapScoreSource.CONTRIBUTE.code) {
            ext.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, sid.toString());
            ranks = hdztRankingThriftClient.queryRanking(actId, actRoleRankMap.getRankId(), actRoleRankMap.getPhaseId(), dateStr, topN, ext);
        }

        List<String> members = ranks.stream().map(Rank::getMember).collect(Collectors.toList());

        return MyListUtils.subList(members, 0, topN);
    }
}
