package com.yy.gameecology.activity.client.thrift;

import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.turnover_contract.TAppId;
import com.yy.thrift.turnover_contract.TContractService;
import com.yy.thrift.turnover_contract.TRoomContract;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class TurnoverContractServiceThriftClient {


    @Reference(protocol = "nythrift_compact", owner = "${turnoverContract_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"})
    private TContractService.Iface proxy = null;

    @Reference(protocol = "nythrift_compact", owner = "${turnoverContract_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"}
            , retries = 2 , cluster = "failover")
    private TContractService.Iface readProxy = null;

    public TContractService.Iface getProxy() {
        return proxy;
    }

    public TContractService.Iface getReadProxy() {
        return readProxy;
    }

    private static TAppId getAppId(BusiId busiId) {
        return switch (busiId) {
            case MAKE_FRIEND -> TAppId.Dating;
            case GAME_BABY -> TAppId.Baby;
            case SKILL_CARD -> TAppId.PeopleGame;
            case YUE_ZHAN -> TAppId.VipPk;
            default -> throw new IllegalArgumentException("Unsupported busiId " + busiId.getValue());
        };
    }

    public TRoomContract queryRoomContractByUid(BusiId busiId, long uid) {
        Map<Long, TRoomContract> res = null;
        try {
            res = getReadProxy().queryRoomContractByUid(getAppId(busiId), Collections.singletonList(uid));
        } catch (Exception e) {
            log.error("queryRoomContractByUid fail:", e);
        }

        if (res != null && res.containsKey(uid)) {
            return res.get(uid);
        }

        return null;
    }

    public List<TRoomContract> queryRoomContractByOw(BusiId busiId, long uid) {
        try {
            return getReadProxy().queryRoomContractByOw(getAppId(busiId), uid);
        } catch (Exception e) {
            log.error("queryRoomContractByOw fail:", e);
            return Collections.emptyList();
        }
    }

    /**
     * 查询uid下的交友房管厅，包括：1、uid为房管的房管厅；2、uid为ow的房管厅
     * @param uid
     * @return
     */
    @Cached(timeToLiveMillis = 10 * 60 * 1000)
    public Set<String> queryFtsChannel(long uid) {
        List<TRoomContract> contracts = queryRoomContractByOw(BusiId.MAKE_FRIEND, uid);
        TRoomContract contract = queryRoomContractByUid(BusiId.MAKE_FRIEND, uid);
        Set<String> result = new HashSet<>(contracts.size() + 1);
        if (contract != null) {
            result.add(contract.getSid() + StringUtil.UNDERSCORE + contract.getSsid());
        }

        for (TRoomContract c : contracts) {
            result.add(c.getSid() + StringUtil.UNDERSCORE + c.getSsid());
        }

        return result;
    }

    /**
     * 获取全量房管厅
     */
    public Map<Long, TRoomContract> queryRoomContract() {
        try {
            Map<Long, TRoomContract> roomContractMap =
                getReadProxy().queryRoomContractBySsid(TAppId.Dating, new ArrayList<Long>());
            log.info("queryContractByLiveUid resp={}", roomContractMap);
            return roomContractMap;
        } catch (Exception e) {
            log.error("queryContractByLiveUid error", e);
        }
        return null;
    }

    public long queryJyRoomContract(long sid, long ssid) {
        Map<Long, TRoomContract> roomContractMap = queryRoomContract();
        if(roomContractMap == null || roomContractMap.isEmpty()) {
            return sid;
        }
        long roomContractSid = 0;
        if(roomContractMap.containsKey(ssid)) {
            TRoomContract tRoomContract = roomContractMap.get(ssid);
            if(tRoomContract.getSid() == 0) {
                roomContractSid = sid;
            } else {
                roomContractSid = tRoomContract.getSid();
            }
            return roomContractSid;
        }
        return sid;
    }

}
