package com.yy.gameecology.activity.bean.actlayer;


import com.yy.thrift.hdztranking.RankingPhaseInfo;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-09-12 15:00
 **/
public class PhaseInfo {
    private String name;
    /**
     * 展示名称
     */
    private String nameShow;

    private long actId;

    //阶段id
    private long phaseId;

    //开始时间
    private long startTime;

    //结束时间
    private long endTime;

    //展示开始时间
    private long showBeginTime;

    //展示结束时间
    private long showEndTime;

    private String extJson;

    /**
     * 任务完成次数限制
     **/
    private Integer recycleCount;

    public Integer getRecycleCount() {
        return recycleCount;
    }

    public void setRecycleCount(Integer recycleCount) {
        this.recycleCount = recycleCount;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameShow() {
        return nameShow;
    }

    public void setNameShow(String nameShow) {
        this.nameShow = nameShow;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getShowBeginTime() {
        return showBeginTime;
    }

    public void setShowBeginTime(long showBeginTime) {
        this.showBeginTime = showBeginTime;
    }

    public long getShowEndTime() {
        return showEndTime;
    }

    public void setShowEndTime(long showEndTime) {
        this.showEndTime = showEndTime;
    }

    public long getActId() {
        return actId;
    }

    public void setActId(long actId) {
        this.actId = actId;
    }

    public String getExtJson() {
        return extJson;
    }

    public void setExtJson(String extJson) {
        this.extJson = extJson;
    }


    public static PhaseInfo toPhaseInfo(long actId, RankingPhaseInfo rankingPhaseInfo) {
        if (rankingPhaseInfo == null) {
            return null;
        }
        PhaseInfo phaseInfoRes = new PhaseInfo();
        phaseInfoRes.setActId(actId);
        phaseInfoRes.setPhaseId(rankingPhaseInfo.getPhaseId());
        phaseInfoRes.setStartTime(rankingPhaseInfo.getBeginTime());
        phaseInfoRes.setEndTime(rankingPhaseInfo.getEndTime());
        phaseInfoRes.setShowBeginTime(rankingPhaseInfo.getShowBeginTime());
        phaseInfoRes.setShowEndTime(rankingPhaseInfo.getShowEndTime());
        phaseInfoRes.setName(rankingPhaseInfo.getPhaseName());
        phaseInfoRes.setNameShow(rankingPhaseInfo.getPhaseNameShow());
        phaseInfoRes.setExtJson(rankingPhaseInfo.getExtJson());
        return phaseInfoRes;
    }
}
