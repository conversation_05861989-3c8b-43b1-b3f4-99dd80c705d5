package com.yy.gameecology.activity.client.yrpc;

import com.yy.protocol.pb.shop.ZhuiyaShopServer;
import org.apache.dubbo.common.annotation.Yrpc;

/**
 * <AUTHOR> 2024/3/6
 */
public interface ZhuiyaShopServerYrpc {
    
    @Yrpc(functionName = "shop/listShopItem")
    ZhuiyaShopServer.ListShopItemResp listShopItem(ZhuiyaShopServer.ListShopItemReq req);
    
    @Yrpc(functionName = "shop/buyShopItem")
    ZhuiyaShopServer.BuyShopItemResp buyShopItem(ZhuiyaShopServer.BuyShopItemReq req);
    
    @Yrpc(functionName = "shop/buyHistory")
    ZhuiyaShopServer.BuyHistoryResp buyHistory(ZhuiyaShopServer.BuyHistoryReq req);
    
    static ZhuiyaShopServer.App getApp(String app) {
        if ("zhuiwan".equalsIgnoreCase(app)) {
            return ZhuiyaShopServer.App.ZHUIWAN;
        }
        if ("yomi".equalsIgnoreCase(app)) {
            return ZhuiyaShopServer.App.YOMI;
        }
        if ("pcyy".equalsIgnoreCase(app)) {
            return ZhuiyaShopServer.App.PCYY;
        }
        if ("yymobile".equalsIgnoreCase(app)) {
            return ZhuiyaShopServer.App.YOMI;
        }
        return ZhuiyaShopServer.App.UNKNOWN_APP;
    }
    
    static ZhuiyaShopServer.Platform getPlatform(String platform) {
        if ("1".equals(platform)) {
            return ZhuiyaShopServer.Platform.PC;
        }
        if ("2".equals(platform)) {
            return ZhuiyaShopServer.Platform.WEB;
        }
        if ("3".equals(platform)) {
            return ZhuiyaShopServer.Platform.ANDROID;
        }
        if ("4".equals(platform)) {
            return ZhuiyaShopServer.Platform.IOS;
        }
        return ZhuiyaShopServer.Platform.UNKNOWN_PLATFORM;
    }
    
}
