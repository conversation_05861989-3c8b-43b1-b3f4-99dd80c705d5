package com.yy.gameecology.activity.bean.acttask;

/**
 * desc:子任务
 *
 * @createBy 曾文帜
 * @create 2020-10-26 14:45
 **/
public class MemberTaskItemVo {

    /**
     *子任务名称
     */
    private String name;

    /**
     *任务类型  过关类型 1-榜单分值 2-赛程榜单排名
     */
    private int type;

    /**
     *当前累计荣耀值
     */
    private long curScore;

    /**
     *	完成任务需要的荣耀值
     */
    private long taskScore;

    /**
     *任务完成状态 1===已达成
     */
    private int state;

    /**
     * 任务等级
     */
    private int level;

    /**
     *	说明规则
     */
    private String desc;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getCurScore() {
        return curScore;
    }

    public void setCurScore(long curScore) {
        this.curScore = curScore;
    }

    public long getTaskScore() {
        return taskScore;
    }

    public void setTaskScore(long taskScore) {
        this.taskScore = taskScore;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }
}
