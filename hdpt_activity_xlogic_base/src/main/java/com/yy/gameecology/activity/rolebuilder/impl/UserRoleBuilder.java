package com.yy.gameecology.activity.rolebuilder.impl;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.rankroleinfo.UserBaseItem;
import com.yy.gameecology.activity.bean.rankroleinfo.UserRoleItem;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SpringBeanAwareFactory;
import com.yy.thrift.act_ext_support.MemberItemInfo;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
public class UserRoleBuilder extends UserRoleBaseBuilder<UserRoleItem> {
//    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient = SpringBeanAwareFactory.getBean(FtsBaseInfoBridgeClient.class);

    private static final UserRoleItem DEFAULT_OBJECT= new UserRoleItem();
    {
        DEFAULT_OBJECT.setKey("0");
        DEFAULT_OBJECT.setName("某大看官");
        DEFAULT_OBJECT.setAvatarInfo(Const.IMAGE.DEFAULT_USER_LOGO);

    }
    @Override
    public UserRoleItem getDefaultObject() {
        return DEFAULT_OBJECT;
    }

    @Override
    public UserRoleItem createBankObject() {
        return new UserRoleItem();
    }

    @Override
    public Map<String, UserRoleItem> addExtraInfo(long actId, long roleId, long rankId, long uid, Map<String, UserRoleItem> roleItemMap) {
        //填充用户的贵族信息--贵族信息都保存在交友
        List<String> noNodeGradeUids = roleItemMap.values().stream()
                .filter(roleItem -> roleItem.getGeNobleGrade() == null)
                .map(UserBaseItem::getUid).map(String::valueOf).collect(Collectors.toList());

        if (!noNodeGradeUids.isEmpty()) {
            Map<String, List<String>> memberIdMap = Maps.newHashMap();
            //交友用户角色ID
            String userRoleId = "50003";
            memberIdMap.put(userRoleId, noNodeGradeUids);
            FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient = SpringBeanAwareFactory.getBean(FtsBaseInfoBridgeClient.class);
            Map<String, Map<String, MemberItemInfo>> memberMaps = ftsBaseInfoBridgeClient.queryMember(actId, rankId, memberIdMap);
            Map<String, MemberItemInfo> memberMap = memberMaps.getOrDefault(userRoleId, Collections.EMPTY_MAP);
            for (String userId : noNodeGradeUids) {
                MemberItemInfo memberItemInfo = memberMap.get(userId);
                if (memberItemInfo != null && memberItemInfo.getExt() != null) {
                    Map<String, String> ext = memberItemInfo.getExt();
                    UserRoleItem userRoleItem = roleItemMap.get(userId);
                    userRoleItem.setNobleGrade(ext.get("nobleID"));
                    userRoleItem.setGeNobleGrade(ext.get("ecologyNobleID"));
                }
            }
        }

        return roleItemMap;
    }


}
