package com.yy.gameecology.activity.bean.hdzt;

import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-23 12:17
 **/
public class RankingConfigVo {
    private Long rankingId;
    private String rankingName;

    private String timeKey;

    private String calBeginTime;

    private String calEndTime;

    private String timeKeyBegin;

    private String timeKeyEnd;

    /**
     * 当前阶段信息
     */
    private RankingPhaseVo currentPhase;

    /**
     * 榜单所有阶段
     */
    private List<RankingPhaseVo> phases;

    /**
     * 榜单类型， 1-普通单成员榜单， 2-情侣榜， 3-pk榜，4-全部小时榜的分榜+pk
     */
	private long rankType;
    /**
     * 成员类型， 1-用户， 2-主播， 3-公会，4-天团
     */
    private long memberType;
    /**
     * 模板类型，1-交友, 2-约战, 3-宝贝, 4-全部, 5-陪玩
     */
    private long templateType;


    public Long getRankingId() {
        return rankingId;
    }

    public void setRankingId(Long rankingId) {
        this.rankingId = rankingId;
    }

    public String getRankingName() {
        return rankingName;
    }

    public void setRankingName(String rankingName) {
        this.rankingName = rankingName;
    }

    public RankingPhaseVo getCurrentPhase() {
        return currentPhase;
    }

    public void setCurrentPhase(RankingPhaseVo currentPhase) {
        this.currentPhase = currentPhase;
    }

    public List<RankingPhaseVo> getPhases() {
        return phases;
    }

    public void setPhases(List<RankingPhaseVo> phases) {
        this.phases = phases;
    }

    public String getTimeKey() {
        return timeKey;
    }

    public void setTimeKey(String timeKey) {
        this.timeKey = timeKey;
    }

    public String getCalBeginTime() {
        return calBeginTime;
    }

    public void setCalBeginTime(String calBeginTime) {
        this.calBeginTime = calBeginTime;
    }

    public String getCalEndTime() {
        return calEndTime;
    }

    public void setCalEndTime(String calEndTime) {
        this.calEndTime = calEndTime;
    }

    public String getTimeKeyBegin() {
        return timeKeyBegin;
    }

    public void setTimeKeyBegin(String timeKeyBegin) {
        this.timeKeyBegin = timeKeyBegin;
    }

    public String getTimeKeyEnd() {
        return timeKeyEnd;
    }

    public void setTimeKeyEnd(String timeKeyEnd) {
        this.timeKeyEnd = timeKeyEnd;
    }

    public long getRankType() {
        return rankType;
    }

    public void setRankType(long rankType) {
        this.rankType = rankType;
    }

    public long getMemberType() {
        return memberType;
    }

    public void setMemberType(long memberType) {
        this.memberType = memberType;
    }

    public long getTemplateType() {
        return templateType;
    }

    public void setTemplateType(long templateType) {
        this.templateType = templateType;
    }
}
