package com.yy.gameecology.activity.worker.timer;


import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.service.datatransfer.RedisDataTransferService;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * desc:数据归档
 *
 * <AUTHOR>
 * @date 2022-08-24 17:40
 **/
@ConditionalOnExpression(Const.EXPRESSION_NOT_HISTORY)
@Component
public class DataArchiveTimer {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RedisDataTransferService redisDataTransferService;

    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "0/10 * * * * ? ")
    @Report
    public void runCopyRedisData() {
        String actId = redisDataTransferService.popNeedCopyActId();
        if (StringUtil.isEmpty(actId)) {
            return;
        }
        Clock clock = new Clock();
        log.info("DataArchiveTimer-runCopyRedisData start  actId:{}", actId);
        redisDataTransferService.copyRedisData(Convert.toLong(actId));
        log.info("DataArchiveTimer-runCopyRedisData done@{} actId:{}", clock.tag(), actId);
    }

    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "0/10 * * * * ? ")
    @Report
    public void runDelRedisData() {
        String actId = redisDataTransferService.popNeedDelActId();
        if (StringUtil.isEmpty(actId)) {
            return;
        }

        Clock clock = new Clock();
        redisDataTransferService.delRedisData(Convert.toLong(actId));
        log.info("DataArchiveTimer-runDelRedisData done@{}", clock.tag());
    }
}
