package com.yy.gameecology.activity.processor.ranking.impl;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.RankItem;
import com.yy.gameecology.activity.bean.rank.UserRankItem;
import com.yy.gameecology.activity.processor.ranking.BaseRankingProcessor;
import com.yy.gameecology.activity.service.HdztRankService;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import com.yy.thrift.hdztranking.RankingType;
import com.yy.thrift.hdztranking.RoleDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Component
public class RankingOneUserProcessor extends BaseRankingProcessor {

    @Autowired
    private HdztRankService hdztRankService;

    @Override
    protected int getProcessorType() {
        return RankingType.ONE_USER.getValue();
    }

    @Override
    public List<Object> getRankInfo(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, Map<String, String> ext) {
        List<Object> list = Lists.newArrayList();
        List<RankItem> rankItems = hdztRankService.genRankBaseInfo(rankReq.getActId(), rankReq.getRankId(), ranks, (int) rankingInfo.getRankingType(), rankReq.getShowZeroItem());
        if(!CollectionUtils.isEmpty(rankItems)) {
            List<List<RoleDetail>> roles = rankingInfo.getRoleItemConfig().getRoles();
            Map<Long, RoleDetail> map = RankingAnyProcessor.toRoleDetailMap(roles);
            String groupId = rankItems.get(0).getGroupId();
            RoleDetail roleDetail = StringUtil.isBlank(groupId) ? roles.get(0).get(0) : map.get(Long.parseLong(groupId));
            Template template = this.getTemplateByBusiId(roleDetail.getRoleBusiId());
            List<UserRankItem> userRankItems = hdztRankService.genUserRankItems(rankItems, template.getCode());
            list.addAll(userRankItems);
        }
        return list;
    }
}
