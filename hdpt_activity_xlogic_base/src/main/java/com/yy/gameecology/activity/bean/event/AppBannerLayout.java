package com.yy.gameecology.activity.bean.event;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-06-05 11:26
 **/
@Data
public class AppBannerLayout {
   /**
    * 动画播放位置（左右充满，垂直对齐类型） 0：全屏播放；1：居中对齐播放；2：顶部对齐播放；3：底部对齐播放
    */
   private int type;

   /**
    *相对父布局的间距 2个元素的数组，分别对应顶部和底部的间距；对应位置为[top, bottom]。通常top、bottom为0
    * android 表示android遵循的间距规范；android数值单位dp
    * ios表示iOS遵循的间距规范
    */
   private Map<String, List<Integer>> margin;
}
