package com.yy.gameecology.activity.config.proto;

import com.yy.gameecology.activity.annotation.ProtoHandler;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.util.ReflectionUtils;

/**
 * <AUTHOR> 2020/7/22
 */
public class ProtoHandlerBeanPostProcessor implements BeanPostProcessor {

    private ProtoHandlerMapping protoHandlerMapping;

    public ProtoHandlerBeanPostProcessor(ProtoHandlerMapping protoHandlerMapping) {
        this.protoHandlerMapping = protoHandlerMapping;
    }

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Class<?> targetClass = AopUtils.getTargetClass(bean);
        ReflectionUtils.doWithMethods(targetClass, method -> {
            ProtoHandler protoHandler= AnnotationUtils.findAnnotation(method, ProtoHandler.class);
            if (protoHandler != null) {
                protoHandlerMapping.addHandler(protoHandler, bean, beanName, method);
            }
        }, ReflectionUtils.USER_DECLARED_METHODS);
        return bean;
    }

}
