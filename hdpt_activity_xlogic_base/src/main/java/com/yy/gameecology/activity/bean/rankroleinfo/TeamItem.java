package com.yy.gameecology.activity.bean.rankroleinfo;

import com.yy.gameecology.common.utils.Convert;

/**
 * desc: 团角色信息
 *
 * @createBy 曾文帜
 * @create 2020-07-21 16:14
 **/
public class TeamItem extends RoleItem implements ContractInfo {
    private Long id;
    private Long asid;
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public void setKey(String key) {
        setId(Convert.toLong(key));
    }

    @Override
    public String getKey() {
        return getId()+"";
    }

    public Long getAsid() {
        return asid;
    }

    public void setAsid(Long asid) {
        this.asid = asid;
    }

    @Override
    public Long getConSid() {
        return asid;
    }

    @Override
    public Long getConAsid() {
        return asid;
    }
}
