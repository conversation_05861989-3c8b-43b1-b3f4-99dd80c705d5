package com.yy.gameecology.activity.service.pepc;

import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.client.yrpc.PubgGameGatewayClient;
import com.yy.gameecology.activity.dao.mysql.PepcDao;
import com.yy.gameecology.common.consts.PepcConst;
import com.yy.gameecology.common.db.mapper.pepc.PepcGameMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcPhaseSubscribeMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamScheduleMapper;
import com.yy.gameecology.common.db.model.gameecology.pepc.PepcGame;
import com.yy.gameecology.common.db.model.gameecology.pepc.PepcGameMember;
import com.yy.gameecology.common.db.model.gameecology.pepc.PepcGameTeam;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.component.attr.PepcPhaseComponentAttr;
import com.yy.zhuiya.game.gateway.gen.pb.GameGateway;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;

/**
 * 测试辅助工具
 *
 * <AUTHOR>
 * @date 2025-04-14 11:30
 **/
@Service
public class PepcGameTestService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PepcDao pepcDao;

    @Autowired
    private PubgGameGatewayClient pubgGameGatewayClient;

    @Autowired
    private PepcGameMapper pepcGameMapper;


    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private PepcPhaseSubscribeMapper pepcPhaseSubscribeMapper;


    public Response<String> replaceGame4Test(PepcPhaseComponentAttr attr, long gameId, Date signUpStartTime, Date gameBeginTime) {
        log.info("replaceGame4Test begin,gameId:{},signupStartTime:{},gameBeginTime:{}", gameId, DateUtil.format(signUpStartTime), DateUtil.format(gameBeginTime));
        PepcGame game = pepcDao.getPepcGameById(attr.getActId(), gameId);
        if (game == null) {
            return Response.fail(-1, "pepc_game 表，游戏不存在,id:" + gameId);
        }

        Date signUpEndTime = DateUtil.addMinutes(gameBeginTime, -attr.getSignUpEndMin());
        Date gameEndTime = DateUtil.addMinutes(gameBeginTime, attr.getGameTimeMin());

        GameGateway.CreatePubgMatchVO result = pubgGameGatewayClient.createPubgGame(game.getId(), game.getGameName(),
                signUpStartTime, signUpEndTime, gameBeginTime, gameEndTime,
                0, game.getModuleId(), game.getJoinFunc());
        if (result == null) {
            return Response.fail(-1, "创建腾讯游戏失败");
        }
        pepcGameMapper.updateGameState(game.getActId(), game.getId(), PepcConst.GameState.GAME_CREATED, game.getState(), result.getSiteId(), result.getParentId(), result.getMatchId());


        PepcGameTeam where = new PepcGameTeam();
        where.setActId(attr.getActId());
        where.setGameId(gameId);
        PepcGameTeam pepcGameTeamUpdate = new PepcGameTeam();
        pepcGameTeamUpdate.setMatchId(0L);
        pepcGameTeamUpdate.setTxTeamId("");
        pepcGameTeamUpdate.setState(PepcConst.GameTeamState.INIT);
        int gameTeamRes = pepcDao.getGameecologyDao().update(PepcGameTeam.class, where, pepcGameTeamUpdate);

        PepcGameMember gameMemberWhere = new PepcGameMember();
        gameMemberWhere.setActId(attr.getActId());
        gameMemberWhere.setGameId(gameId);
        PepcGameMember pepcGameMemberUpdate = new PepcGameMember();
        pepcGameMemberUpdate.setState(PepcConst.GameMemberState.INIT);
        int gameMemberRes = pepcDao.getGameecologyDao().update(PepcGameMember.class, gameMemberWhere, pepcGameMemberUpdate);

        log.info("replaceGame4Test,actId:{},gameId:{},signupStartTime:{},gameBeginTime:{},txGameId:{},gameTeamRes:{},gameMemberRes:{}"
                , attr.getActId(), gameId, DateUtil.format(signUpStartTime), DateUtil.format(gameBeginTime), result.getMatchId(), gameTeamRes, gameMemberRes);


        return Response.ok("创建测试赛事成功,等候队伍和队员初始化,txGameId:" + result.getMatchId());

    }

    public void transTest() {
        transactionTemplate.execute(status -> {
//            int res1 = pepcDao.updatePepcGameLive(2L, "test", "test2", 1);
//            int res2 = pepcPhaseSubscribeMapper.updateSubscribeState(3L, 0, 1);
//            log.info("transTest======,res1:{},res2:{}", res1, res2);
//            if (false) {
//                throw new RuntimeException("roll back test");
//            }
            return null;
        });
    }
}
