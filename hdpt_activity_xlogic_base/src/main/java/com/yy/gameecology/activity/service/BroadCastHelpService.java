package com.yy.gameecology.activity.service;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.BroadcastType;
import com.yy.gameecology.common.consts.RankDataSource;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @Author: CXZ
 * @Desciption: 广播协助服务--一些通用方法
 * @Date: 2021/4/15 20:42
 * @Modified:
 */
@Service
public class BroadCastHelpService {

    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CommonService commonService;
    @Autowired
    private SvcSDKService svcSDKService;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private EnrollmentService enrollmentService;
    @Autowired
    private SignedService signedService;

    @Autowired
    private DanmakuActivityClient danmakuActivityClient;

    private static List<Integer> BRO_BUSI_IDS = ImmutableList.of(RankDataSource.YUEZHAN, RankDataSource.BAOBEI, RankDataSource.JIAOYOU, RankDataSource.PEIWAN);

    public UserBaseInfo getUserBaseInfo(long userUid, BusiId busiId) {

        UserBaseInfo userInfo = new UserBaseInfo();
        UserInfoVo userInfoVo = userInfoService.getUserInfo(Lists.newArrayList(userUid), changeBusiId2Template(busiId)).get(userUid);
        if (userInfoVo != null) {
            userInfo.setUid(userInfoVo.getUid());
            userInfo.setNick(userInfoVo.getNick());
            userInfo.setLogo(userInfoVo.getAvatarUrl());
            userInfo.setYyno(Long.parseLong(userInfoVo.getYyno()));
        } else {
            Optional.ofNullable(commonService.getUserInfo(userUid, false)).ifPresent(info -> {
                userInfo.setUid(userUid);
                userInfo.setNick(info.getNick());
                userInfo.setNick(Base64.encodeBase64String(userInfo.getNick().getBytes()));
                userInfo.setLogo(info.getLogo());
                userInfo.setYyno(Convert.toLong(info.getYyno(), 0));
            });
        }
        return userInfo;
    }

    public GameecologyActivity.UserInfo.Builder getUserInfo(long userUid, BusiId busiId) {
        UserBaseInfo userInfo = getUserBaseInfo(userUid, busiId);
        return GameecologyActivity.UserInfo.newBuilder()
                .setUid(userUid)
                .setLogo(userInfo.getLogo())
                .setNick(userInfo.getNick());
    }

    public GameecologyActivity.GiftInfo.Builder getGiftInfo(String giftId, long giftNum, String giftName, String giftUrl) {
        return GameecologyActivity.GiftInfo.newBuilder()
                .setId(giftId)
                .setName(giftName)
                .setCount(giftNum)
                .setUrl(giftUrl);
    }

    public String adjustName(String name, int length) {
        return name.length() > length ? name.substring(0, length) + "..." : name;
    }

    public String adjustString(String name) {
        return name.replace("<", "&lt;").replace(">", "&gt;");
    }

    /**
     * 只从报名表获取信息
     *
     * @param actId
     * @param anchorUid
     * @param channel
     * @param busiId
     * @return
     */
    public GameecologyActivity.AnchorInfo.Builder getAnchorInfo(long actId, long anchorUid, boolean channel, BusiId busiId) {
        UserBaseInfo userInfo = getUserBaseInfo(anchorUid, busiId);
        GameecologyActivity.AnchorInfo.Builder anchor = GameecologyActivity.AnchorInfo.newBuilder()
                .setUid(anchorUid)
                .setLogo(userInfo.getLogo())
                .setNick(userInfo.getNick());
        if (channel) {
            List<EnrollmentInfo> entryConfigInfo = enrollmentService.getEntryConfigInfo(actId, anchorUid + "");
            if (!CollectionUtils.isEmpty(entryConfigInfo)) {
                // 因为报名成员标识（sid / uid等）有可能相同，只需要“主播”角色即可
                List<EnrollmentInfo> anthorEnrollList = entryConfigInfo.stream().filter(i -> i.getRoleType() == RoleType.ANCHOR.getValue()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(anthorEnrollList)) {
                    EnrollmentInfo enrollmentInfo = anthorEnrollList.get(0);
                    anchor.setAsid(enrollmentInfo.getSignAsid());
                }
            }
            if (anchor.getAsid() <= 0) {
                long signedSid = signedService.getSignedSid(anchorUid, changeBusiId2BroTemplate(busiId));
                anchor.setAsid(commonService.getAsid(signedSid));
            }

            UserCurrentChannel userCurrentChannel = commonService.getUserCurrentChannel(anchorUid);
            if (userCurrentChannel != null) {
                anchor.setSid(userCurrentChannel.getTopsid()).setSsid(userCurrentChannel.getSubsid());
            }
            //缓存读不到，尝试实时查
            if (!anchor.hasSid() || anchor.getSid() == 0L) {
                userCurrentChannel = commonService.getNoCacheUserCurrentChannel(anchorUid, 3);
                if (userCurrentChannel != null) {
                    anchor.setSid(userCurrentChannel.getTopsid()).setSsid(userCurrentChannel.getSubsid());
                } else {
                    log.warn("getAnchorInfo not find channel@actId:{} anchorUid:{}", actId, anchorUid);
                }
            }
        }
        return anchor;
    }

    public void broadcastAll(Long actId, GameecologyActivity.GameEcologyMsg message) {

        List<Integer> broBusiIds = Lists.newArrayList();
        String allBroBusiIds = commonService.getActAttr(actId, "ALL_BRO_BUSI_ID");
        if (StringUtils.isNotBlank(allBroBusiIds)) {
            broBusiIds = Stream.of(allBroBusiIds.split(","))
                    .filter(StringUtils::isNumeric)
                    .map(Integer::parseInt).collect(Collectors.toList());
        }

        Assert.isTrue(!CollectionUtils.isEmpty(broBusiIds), "指定业务广播,broBusiIds 必须不为空");
        broadcast(actId, broBusiIds, message);
    }

    /**
     * 广播
     *
     * @param actId
     * @param busiIds
     * @param message
     */
    public void broadcast(Long actId, List<Integer> busiIds, GameecologyActivity.GameEcologyMsg message) {
        for (Integer busiId : busiIds) {
            if (busiId == BusiId.PEI_WAN.getValue()) {
                svcSDKService.broadcastAllChanelsInPW(actId, message);
            } else {
                Template tmp = changeBusiId2BroTemplate(busiId + 0L);
                Assert.notNull(tmp, "busiId cat change bro template.busiId=" + busiId);
                svcSDKService.broadcastTemplate(tmp, message);
            }
        }
    }

    public void broadcast(Long actId, BusiId busiId, int broadcastType, long sid, long ssid, GameecologyActivity.GameEcologyMsg message) {
        broadcast("", actId, busiId, broadcastType, sid, ssid, message);
    }

    public void broadcastExcludeDanmakuChannel(GameecologyActivity.GameEcologyMsg message) {
        List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
        Set<String> exclude = danmakuChannel.stream().map(channel -> channel.getSid() + StringUtil.UNDERSCORE + channel.getSsid()).collect(Collectors.toSet());
        svcSDKService.broadcastTemplateExclude(Template.Jiaoyou, message, exclude);
        log.info("broadcastExcludeDanmakuChannel success exclude channel:{}", exclude);
    }

    /**
     * @param actId
     * @param busiId
     * @param broadcastType 2 = 子频道广播，3=顶级频道广播，4=本业务模板内广播，5=活动业务广播
     * @param sid           6的时候传家族id
     * @param ssid
     * @param message
     */
    public void broadcast(String seq, Long actId, BusiId busiId, int broadcastType, long sid, long ssid, GameecologyActivity.GameEcologyMsg message) {
        if (broadcastType == BroadcastType.FAMILY) {
            //请使用另外1个带有familyId参数的重载
            throw new RuntimeException("use broadcast with familyId parameter");
        }
        broadcast(seq, actId, busiId, broadcastType, 0, sid, ssid, message);
    }

    /**
     * @param actId
     * @param busiId
     * @param broadcastType 2 = 子频道广播，3=顶级频道广播，4=本业务模板内广播，5=活动业务广播，6=本家族广播
     * @param familyId   broadcastType==6的时候要传家族id
     * @param sid
     * @param ssid
     * @param message
     */
    public void broadcast(String seq, Long actId, BusiId busiId, int broadcastType, long familyId, long sid, long ssid, GameecologyActivity.GameEcologyMsg message) {
        switch (broadcastType) {
            case 2:
                Assert.isTrue(sid > 0 && ssid > 0, "子频道广播sid和ssid必须不为0. seq=" + seq + " sid=" + sid + ",ssid=" + ssid);
                svcSDKService.broadcastSub(sid, ssid, message);
                break;
            case 3:
                Assert.isTrue(sid > 0, "顶级频道广播sid必须不为0.seq=" + seq + " sid=" + sid);
                svcSDKService.broadcastTop(sid, message);
                break;
            case 4:
                Assert.notNull(busiId, "模板内广播bussId必须不为null,seq=" + seq);
                broadcast(actId, Lists.newArrayList(busiId.getValue()), message);
                break;
            case 5:
                broadcastAll(actId, message);
                break;
            case 6:
                Assert.isTrue(familyId > 0, "familyId 必须大于0");
                broadcastFamily(familyId, message);
            default:
                Assert.isTrue(true, "广播类型错误,seq=" + seq + " broadcastType=" + broadcastType);
                break;
        }
    }

    public void broadcastExcludeDanmaku(String seq, Long actId, BusiId busiId, int broadcastType, long sid, long ssid, GameecologyActivity.GameEcologyMsg message,
                                         int exludeDanmaku) {
        switch (broadcastType) {
            case 4:
                Assert.notNull(busiId, "模板内广播bussId必须不为null,seq=" + seq);
                if(exludeDanmaku != 0) {
                    broadcastExcludeDanmakuChannel(message);
                } else {
                    broadcast(actId, Lists.newArrayList(busiId.getValue()), message);
                }
                break;
            default:
                broadcast(seq, actId, busiId, broadcastType, sid, ssid, message);
        }
    }

    public void broadcastFamily(long familyId, GameecologyActivity.GameEcologyMsg message) {
        Assert.isTrue(familyId > 0, "家族广播familyId必须不为0.familyId=" + familyId);
        svcSDKService.broadcastFamilyInSkillCard(familyId, message);

    }



    public BusiId changeBroTemplate2BusiId(Template broTemplate) {
        switch (broTemplate) {
            case Gamebaby:
                return BusiId.GAME_BABY;
            case Yuezhan:
                return BusiId.YUE_ZHAN;
            case Jiaoyou:
                return BusiId.MAKE_FRIEND;
            case SkillCard:
                return BusiId.SKILL_CARD;
            case ZhuiWan:
                return BusiId.ZHUI_WAN;
            default:
                return null;
        }
    }
    public static Template changeBusiId2BroTemplate(int busiId) {
        return changeBusiId2BroTemplate(BusiId.findByValue(busiId));
    }
    public static Template changeBusiId2BroTemplate(Long busiId) {
        return changeBusiId2BroTemplate(BusiId.findByValue(busiId.intValue()));
    }

    public static Template changeBusiId2BroTemplate(BusiId busiId) {
        switch (busiId) {
            case GAME_BABY:
                return Template.Gamebaby;
            case YUE_ZHAN:
                return Template.Yuezhan;
            case MAKE_FRIEND:
                return Template.Jiaoyou;
            case SKILL_CARD:
                return Template.SkillCard;
            default:
                return null;
        }
    }
    public static int toAppBroBusiness(int busiId) {
        return toAppBroBusiness(BusiId.findByValue(busiId));
    }
    /**
     * @param busiId 810-语音房，500-交友，400-宝贝
     * @return 需要推送的业务类型 1 -语音房 2 -交友房 4 -其他 8 -宝贝
     */
    public static int toAppBroBusiness(BusiId busiId) {
        return switch (busiId) {
            case BusiId.SKILL_CARD -> 1;
            case BusiId.MAKE_FRIEND -> 2;
            case BusiId.GAME_BABY -> 8;
            default -> 4;
        };
    }


    public com.yy.gameecology.common.bean.Template changeBroTemplate2Template(Template broTemplate) {
        BusiId busiId = changeBroTemplate2BusiId(broTemplate);
        return changeBusiId2Template(busiId);

    }

    public Template changeTemplate2BroTemplate(com.yy.gameecology.common.bean.Template template) {
        BusiId busiId = changeTemplate2BusiId(template);
        return changeBusiId2BroTemplate(busiId);
    }

    public BusiId changeTemplate2BusiId(com.yy.gameecology.common.bean.Template template) {
        switch (template) {
            case gamebaby:
                return BusiId.GAME_BABY;
            case yuezhan:
                return BusiId.YUE_ZHAN;
            case makefriend:
                return BusiId.MAKE_FRIEND;
            default:
                return null;
        }
    }

    public com.yy.gameecology.common.bean.Template changeBusiId2Template(BusiId busiId) {
        if (busiId == null) {
            return null;
        }
        switch (busiId) {
            case GAME_BABY:
                return com.yy.gameecology.common.bean.Template.gamebaby;
            case YUE_ZHAN:
                return com.yy.gameecology.common.bean.Template.yuezhan;
            case MAKE_FRIEND:
                return com.yy.gameecology.common.bean.Template.makefriend;
            case PEI_WAN:
                return com.yy.gameecology.common.bean.Template.peiwan;
            default:
                return null;
        }
    }
}
