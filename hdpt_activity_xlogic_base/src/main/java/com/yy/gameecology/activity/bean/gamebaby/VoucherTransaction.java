/*
 * @(#)VoucherTransaction.java 2017-12-23
 * 
 * Copy Right@ 欢聚时代
 *
 * 代码生成: voucher_transaction 表的数据模型类  VoucherTransaction
 */
package com.yy.gameecology.activity.bean.gamebaby;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @功能说明
 * <pre>
 * voucher_transaction 表的数据模型类  VoucherTransaction
 * </pre>
 * 
 * @版本更新
 * <pre>
 * 修改版本: 1.0.0 / 2017-12-23 / cgc
 * 修改说明：形成初始版本
 * 复审人：
 * </pre>
 */
@SuppressWarnings("serial") 
public class VoucherTransaction implements java.io.Serializable {
	private static final Logger log = LoggerFactory.getLogger(VoucherTransaction.class);
	
	private Long id;
	private String sid;
	private Long payer;
	private Long recver;
	private BigDecimal amount;
	private String status;
	private String subject;
	private BigDecimal count;
	private String summary;
	private Date createtime;
	private Date modifytime;

	public void setId(Long id){
		this.id = id;
	}

	public Long getId(){
		return id;
	}

	public void setSid(String sid){
		this.sid = sid;
	}

	public String getSid(){
		return sid;
	}

	public void setPayer(Long payer){
		this.payer = payer;
	}

	public Long getPayer(){
		return payer;
	}

	public void setRecver(Long recver){
		this.recver = recver;
	}

	public Long getRecver(){
		return recver;
	}

	public void setAmount(BigDecimal amount){
		this.amount = amount;
	}

	public BigDecimal getAmount(){
		return amount;
	}

	public void setStatus(String status){
		this.status = status;
	}

	public String getStatus(){
		return status;
	}

	public void setSubject(String subject){
		this.subject = subject;
	}

	public String getSubject(){
		return subject;
	}

	public void setCount(BigDecimal count){
		this.count = count;
	}

	public BigDecimal getCount(){
		return count;
	}

	public void setSummary(String summary){
		this.summary = summary;
	}

	public String getSummary(){
		return summary;
	}

	public void setCreatetime(Date createtime){
		this.createtime = createtime;
	}

	public Date getCreatetime(){
		return createtime;
	}

	public void setModifytime(Date modifytime){
		this.modifytime = modifytime;
	}

	public Date getModifytime(){
		return modifytime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}