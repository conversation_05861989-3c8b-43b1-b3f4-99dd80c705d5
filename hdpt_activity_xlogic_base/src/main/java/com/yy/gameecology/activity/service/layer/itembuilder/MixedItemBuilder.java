package com.yy.gameecology.activity.service.layer.itembuilder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yy.gameecology.activity.bean.actlayer.*;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.ZhuiwanRoomInfoClient;
import com.yy.gameecology.activity.service.EnrollmentService;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.common.consts.ActorInfoStatus;
import com.yy.gameecology.common.consts.LayerItemTypeKey;
import com.yy.gameecology.common.consts.RankMapType;
import com.yy.gameecology.common.db.model.gameecology.ActLayerViewDefine;
import com.yy.gameecology.common.db.model.gameecology.ActResult;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.component.RankBuilderComponent;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.ActLayerConfigComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.RankBuilderComponentAttr;
import com.yy.thrift.hdztranking.*;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class MixedItemBuilder extends ActLayerInfoService implements LayerItemBuilder {

    private static final String ROLE_NAME_PLACEHOLDER = "{roleName}";

    @Autowired
    private EnrollmentService enrollmentService;

    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;

    @Autowired
    private RankBuilderComponent rankBuilderComponent;

    @Autowired
    private WhitelistComponent whitelistComponent;

    @Override
    public Set<String> getItemKeys() {
        return Collections.singleton(LayerItemTypeKey.MIXED);
    }

    @Override
    public List<String> getMemberIds(ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, OnlineChannelInfo onlineChannel) {
        String extJson = viewDefine.getExtJson();
        if (!StringUtils.startsWith(extJson, StringUtil.OPEN_BRACE)) {
            return Collections.emptyList();
        }

        Map<Long, List<MixedRoleConfig>> mixedConfig = JSON.parseObject(extJson, new TypeReference<Map<Long, List<MixedRoleConfig>>>() {
        });
        List<MixedRoleConfig> roleConfigs = mixedConfig.get(busiId);
        if (CollectionUtils.isEmpty(roleConfigs)) {
            return Collections.emptyList();
        }

        for (MixedRoleConfig roleConfig : roleConfigs) {
            List<String> memberIds = getMember(onlineChannel, roleConfig);
            if (CollectionUtils.isEmpty(memberIds)) {
                continue;
            }

            Map<String, EnrollmentInfo> enrollmentInfoMap = enrollmentService.getFirstEnrollmentInfoMap(actInfo.getActId(), memberIds, RoleType.findByValue(roleConfig.roleType));
            for (Map.Entry<String, EnrollmentInfo> entry : enrollmentInfoMap.entrySet()) {
                EnrollmentInfo enrollmentInfo = entry.getValue();
                if (enrollmentInfo == null || enrollmentInfo.roleBusiId != busiId) {
                    continue;
                }

                if (roleConfig.roleId != null && roleConfig.roleId > 0) {
                    if (roleConfig.roleId != enrollmentInfo.destRoleId) {
                        continue;
                    }
                }

                // 只需返回一个满足的角色
                return Collections.singletonList(roleConfig.roleType + StringUtil.VERTICAL_BAR + entry.getKey());
            }
        }

        return Collections.emptyList();
    }

    @Override
    public List<LayerMemberItem> build(Date now, ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, List<String> memberIds, Map<String, Object> ext) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return Collections.emptyList();
        }

        List<LayerMemberItem> result = new ArrayList<>(memberIds.size());
        for (String member : memberIds) {
            String[] splitters = StringUtils.split(member, StringUtil.VERTICAL_BAR);
            final int roleType = Integer.parseInt(splitters[0]);
            final String memberId = splitters[1];

            ActLayerViewDefine copy = new ActLayerViewDefine();
            BeanUtils.copyProperties(viewDefine, copy);
            copy.setRoleType(roleType);
            LayerMemberItem memberItem = getLayerMemberItem(actInfo, copy, busiId, memberId, now);
            result.add(memberItem);
        }
        return result;
    }

    public LayerMemberItem getLayerMemberItem(ActivityInfoVo actInfo,
                                              ActLayerViewDefine viewDefine,
                                              Long busiId,
                                              String memberId,
                                              Date now) {

        final long actId = actInfo.getActId();
        final int roleType = viewDefine.getRoleType();
        LayerMemberItem itemRes = new LayerMemberItem();
        itemRes.setMemberId(memberId);
        itemRes.setItemType(viewDefine.getItemTypeKey());
        itemRes.setRoleType(roleType);
        MemberInfo memberInfo = memberInfoService.getMemberInfo(busiId, RoleType.findByValue(roleType), memberId);
        if (memberInfo != null) {
            itemRes.setLogo(memberInfo.getLogo() == null ? StringUtils.EMPTY : memberInfo.getLogo());
            String roleName = enrollmentService.getFirstEnrolDestRoleName(actInfo.getActId(), busiId, roleType, memberId);
            itemRes.setRoleName(roleName);

            String replateName = replaceWhiteListNick(actId, memberId, memberInfo.getName());
            itemRes.setNickName(Base64Utils.encodeToString(Convert.toString(replateName).getBytes()));
        }

        EnrollmentInfo enrollmentInfo = enrollmentService.getFirstEnrolMember(actId, busiId, roleType, memberId);
        if (enrollmentInfo != null) {
            itemRes.setAsid(commonService.getAsid(enrollmentInfo.getSignSid()) + "");
            itemRes.setRoleId(enrollmentInfo.getDestRoleId());
        }

        ActResult actResult = getNotLastPhaseTitle(now.getTime(), viewDefine.getItemTypeKey(), actId, roleType, memberId);
        if (actResult != null) {
            itemRes.setShowHonorTitle(true);
            itemRes.setLastPhaseTopTitle(actResult.getTitle());
        }

        //结算状态
        if (inSettle(actId, now)) {
            itemRes.setSettleStatus(1);
            return itemRes;
        }

        if (roleType == RoleType.ANCHOR.getValue()) {
            itemRes.setSignStatus(1);
        }

        //---构造查询成员积分参数
        ActorQueryItem actorStatusPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.NORMAL, now);

        //如果为空,尝试获取总榜
        if (actorStatusPara == null) {
            actorStatusPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.TOTAL, now);
        }
        if (actorStatusPara == null) {
            if (enrollmentInfo == null) {
                itemRes.setState(ActorInfoStatus.NOT_IN);
            } else {
                //不在赛程内
                itemRes.setState(ActorInfoStatus.NOT_IN_PHASE);
            }
            return itemRes;
        }

        //-----查出成员积分
        ActorInfoItem actorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, actorStatusPara);


        ActLayerConfigComponentAttr layerAttr = layerConfigService.getLayerAttrConfig(actId);
        //日榜，展示每日top n
        String prePhaseTopNTitle = getPrePhaseTopNTitle(now, actId, roleType, actorItem, layerAttr);
        if (StringUtil.isNotBlank(prePhaseTopNTitle)) {
            itemRes.setShowHonorTitle(true);
            itemRes.setLastPhaseTopTitle(prePhaseTopNTitle);
        }

        if (actorItem != null) {
            Long rankId = actorItem.getRankingId();
            itemRes.setCurRankId(rankId);
            itemRes.setCurPhaseId(actorItem.getPhaseId());

            PhaseInfo curPhaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, actorItem.getPhaseId());
            itemRes.setCurPhaseInfo(curPhaseInfo);

            itemRes.setScore(actorItem.getScore());
            //对象状态 -1非参赛角色（没在分组名单） 0-正常状态（在晋级线以上） 1-代表有危险（在晋级线以下） 2-被淘汰
            itemRes.setState(actorItem.getStatus());
            //距离上一名
            itemRes.setOffsetScore(actorItem.getRank() != 1 ? actorItem.getPreScore() - actorItem.getScore() : 0);
            itemRes.setSort(0);
            itemRes.setRank(Convert.toInt(actorItem.getRank(), 0));
            RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, rankId, now, actorItem.getPhaseId());
            itemRes.setCurRankName(rankingInfo.getRankingName());
            itemRes.setCurRankNameShow(rankingInfo.getRankingNameShow());
            itemRes.setCurRankExtJson(rankingInfo.getRankingExtjson());
            //N进X
            String passDesc = getPassDescContent(rankingInfo);
            itemRes.setPassDesc(passDesc);
            long leftSeconds = getPhaseLeftSeconds(rankingInfo, curPhaseInfo, now);
            itemRes.setLeftSeconds(leftSeconds);

            if (!itemRes.isShowHonorTitle()) {
                String topTitle = getLastPhaseTopTitle(actId, rankingInfo, memberId, actorItem.getPhaseId(), actorItem.getRank(), roleType);
                itemRes.setLastPhaseTopTitle(topTitle);
            }

            boolean inDrawLots = hdztPhaseConfigService.isInDrawLots(now.getTime(), curPhaseInfo);
            itemRes.setInDrawLots(inDrawLots);

            //公会小时榜(和前端协商,0的时候不展示)
            itemRes.setHourRank(0);
            ActorQueryItem dayRankQuery = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.DAY, now, false);
            if (dayRankQuery != null) {
                RankingInfo dayRankingInfo = hdztRankingThriftClient.queryRankConfig(actId, dayRankQuery.getRankingId());
                //在采集榜单事件内
                if (inHourRank(dayRankingInfo, now)) {
                    ActorInfoItem hourActor = hdztRankingThriftClient.queryActorRankingInfo(actId, dayRankQuery);
                    itemRes.setHourRank(Convert.toInt(hourActor.getRank()));
                }
            }

            //总榜荣耀值和排名
            ActorQueryItem queryTotalActorPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.TOTAL, now);
            if (queryTotalActorPara != null) {
                ActorInfoItem totalActorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, queryTotalActorPara);
                if (totalActorItem != null) {
                    itemRes.setTotalRank(Convert.toInt(totalActorItem.getRank(), 0));
                    itemRes.setTotalScore(totalActorItem.getScore());
                }

                if (StringUtil.isEmpty(itemRes.getLastPhaseTopTitle())) {
                    RankingInfo totalRankingInfo = hdztRankingThriftClient.queryRankConfig(actId, queryTotalActorPara.getRankingId()
                            , now, queryTotalActorPara.getPhaseId());
                    String topTitle = getLastPhaseTopTitle(totalRankingInfo, queryTotalActorPara.getPhaseId(), totalActorItem.getRank());
                    itemRes.setLastPhaseTopTitle(topTitle);
                }
            }

            // 日排行日荣耀值
            ActorQueryItem queryDayActorPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.DAY, now);
            if (queryDayActorPara != null) {
                ActorInfoItem dayActorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, queryDayActorPara);
                if (dayActorItem != null) {
                    itemRes.setDayRank(Convert.toInt(dayActorItem.getRank(), 0));
                    itemRes.setDayScore(dayActorItem.getScore());
                }
            }

            //获取日排名奖励积分
            ActorQueryItem queryAwardPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.AWARD, now);
            if (queryAwardPara != null) {
                int addScore = getDayRankAwardScore(actId, queryAwardPara.getRankingId(), queryAwardPara.getPhaseId(), itemRes.getDayRank());
                itemRes.setAddScore(addScore);
            }


            //pk信息
            PKInfo pkInfo = getPkInfo(actId, viewDefine, busiId, memberId, now, null);
            //结算中(抽签中优先级大于结算中)
            if (pkInfo != null && pkInfo.isInSettle() && !inDrawLots) {
                itemRes.setSettleStatus(1);
                return itemRes;
            }
            if (pkInfo != null) {
                itemRes.setPkInfo(pkInfo);
            }


            //分组排名 GroupInfo
            ActorQueryItem queryGroupPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.GROUP, now);
            if (queryGroupPara != null) {
                GroupInfo groupInfo = getGroupInfo(actId, queryGroupPara.getRankingId(), queryGroupPara.getPhaseId(), memberId, queryGroupPara.getDateStr(), queryAwardPara.getExtData());
                if (groupInfo != null) {
                    itemRes.setGroupName(groupInfo.getGroupName());
                    itemRes.setGroupRank(groupInfo.getRank());
                    itemRes.setGroupOffsetScore(groupInfo.getGroupOffsetScore());
                    itemRes.setPassDesc(groupInfo.getPassDesc());
                    itemRes.setRank(groupInfo.getRank());
                    //1、 晋级结算到新的阶段，当榜单第一名分数为0，所有人都是正常状态，排名为-- ;
                    //2、榜单第一名出现分数后，按照实际排名(有的有可能是0分,但是继承了上一阶段的排名)展示排名和危险状态
                    if (groupInfo.isAllScoreIsZero()) {
                        itemRes.setGroupRank(-1);
                        if (itemRes.getState() == 1) {
                            itemRes.setState(0);
                        }
                    }
                    //前端展示需要，榜单名称加上分组名称
                    //itemRes.setCurRankName(itemRes.getCurRankName() + groupInfo.getGroupName());
                    itemRes.setCurRankName(groupInfo.getGroupName());
                    itemRes.setCurRankNameShow(rankingInfo.getRankingNameShow() + groupInfo.getGroupName());
                }
            }

            //复活赛需要查询上一阶段信息
            if (curPhaseInfo != null && isResurrection(curPhaseInfo.getExtJson())) {
                ActorQueryItem prePara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.NORMAL, now, true);
                ActorInfoItem preActorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, prePara);
                if (preActorItem != null) {
                    itemRes.setPrePhaseState(preActorItem.getStatus());
                }
            }

            //最后一个阶段晋级结算展示top n (和最后一阶段n进x的n是同一个标识)
            int lastPhaseTopN = curPhaseInfo != null ? getLastPhaseTopN(rankingInfo, curPhaseInfo.getPhaseId()) : 0;
            itemRes.setLastPhaseTopN(lastPhaseTopN);

        }


        return itemRes;
    }


    private String replaceWhiteListNick(long actId, String member, String oldNick) {
        RankBuilderComponentAttr att = rankBuilderComponent.getUniqueComponentAttr(actId);
        if (att == null) {
            return oldNick;
        }
        if (att.getMemberNameCmptIndex() <= 0) {
            return oldNick;
        }
        String value = whitelistComponent.getConfigValue(actId, att.getMemberNameCmptIndex(), member);
        if (StringUtil.isNotBlank(value)) {
            return value;
        }

        return oldNick;
    }

    private String getLastPhaseTopTitle(long actId, RankingInfo rankingInfo, String memberId, long phaseId, long rank, int roleType) {
        String title = getLastPhaseTopTitle(rankingInfo, phaseId, rank);
        if (StringUtils.contains(title, ROLE_NAME_PLACEHOLDER)) {
            String roleName = getRoleName(actId, roleType, memberId);
            title = title.replace(ROLE_NAME_PLACEHOLDER, roleName);
        }

        return title;
    }

    private String getRoleName(long actId, int roleType, String memberId) {
        //TODO 特殊参赛厅展示成主持，线上临时应急用，活动结束后可删除
        String anchorRoleList = cacheService.getActAttrValue(actId, "anchorRoleList");
        if (StringUtil.isNotBlank(anchorRoleList) && memberId != null && anchorRoleList.contains(memberId)) {
            return "主持";
        }


        switch (roleType) {
            case 100:
                return "神豪";
            case 200:
                return "主持";
            case 400:
                return "公会";
            case 401:
            case 404:
                return "厅";
            case 700:
                return "家族";
            default:
                return "大大";
        }
    }

    @Override
    public void fillLayerBroadcastInfo(ActLayerViewDefine viewDefine, LayerBroadcastInfo target, List<LayerMemberItem> source, List<String> memberIds, Map<String, Object> ext) {
        target.getExtMemberItem().addAll(source);
    }

    private List<String> getMember(OnlineChannelInfo onlineChannel, MixedRoleConfig roleConfig) {
        if (roleConfig.roleType == RoleType.GUILD.getValue()) {
            return Collections.singletonList(String.valueOf(onlineChannel.getSid()));
        } else if (roleConfig.roleType == RoleType.HALL.getValue()) {
            return Collections.singletonList(onlineChannel.getSid() + StringUtil.UNDERSCORE + onlineChannel.getSsid());
        } else if (roleConfig.roleType == RoleType.ROOM.getValue()) {
            RoomInfo roomInfo = commonService.getRoomInfoBySsid(Convert.toLong(onlineChannel.getSsid()));
            if (roomInfo != null) {
                return Collections.singletonList(String.valueOf(roomInfo.getRoomId()));
            }
        } else if (roleConfig.roleType == RoleType.FAMILY.getValue()) {
            RoomInfo roomInfo = zhuiwanRoomInfoClient.roomInfoBySsid(onlineChannel.getSsid());
            if (roomInfo != null) {
                return Collections.singletonList(String.valueOf(roomInfo.getFamilyId()));
            }
        } else if (roleConfig.roleType == RoleType.ANCHOR.getValue()) {
            if (CollectionUtils.isEmpty(onlineChannel.getEffectAnchorId())) {
                return null;
            }
            return onlineChannel.getEffectAnchorId().stream().map(String::valueOf).collect(Collectors.toList());
        }

        return null;
    }

    @Data
    public static class MixedRoleConfig {

        protected Integer roleType;

        protected Integer roleId;

    }
}
