package com.yy.gameecology.activity.worker.web;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.LoginService;
import com.yy.gameecology.activity.service.OfferIpService;
import com.yy.gameecology.common.bean.LoginInfoBean;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.RequestUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName BaseController
 * <AUTHOR>
 * @Date 2019/10/31 16:09
 */
public class BaseController {
    protected static final Logger log = LoggerFactory.getLogger(BaseController.class);

    @Autowired
    private LoginService loginService;

    protected String MSG_CLOSE_HINT = "E505#系统维护中，请稍后再试！";

    protected String MSG_NOT_LOGIN = "未登录";

    @Autowired
    private CommonService commonService;

    @Autowired
    private  OfferIpService offerIpService;

    private Set<String> officeIpSet = null;

    // 工号 uid 开始
    public static final long WORK_ACCOUNT_UID_START = ********;
    // 工号 uid 结束
    public static final long WORK_ACCOUNT_UID_END = ********;

    /**
     * 根据 uri 判断是否请求被关闭，默认都是不关闭的
     */
    protected boolean isClosed(HttpServletRequest req) {
        String uri = "close_activity_uri#" + req.getRequestURI();
        return Const.isGeOneFlag(uri, 0);
    }

    /**
     * 若 ticket 存在，要么验证成功， 要么验证失败
     * 若无ticket，则改走验证cookie
     */
    public long getLoginYYUid(HttpServletRequest req, HttpServletResponse resp) {
        if(req == null || resp == null){
            return 0;
        }
        return loginService.getLoginYYUid(req,resp);
    }

    public long getLoginYYUid() {
        HttpServletRequest req = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        HttpServletResponse resp = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getResponse();
        return loginService.getLoginYYUid(req,resp);
    }

    protected String getRequestUrl(HttpServletRequest request) {
        String url = request.getRequestURL().toString();
        if (StringUtils.isNotBlank(request.getQueryString())) {
            url += "?" + request.getQueryString();
        }
        return url;
    }

    protected String getCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        String codeString = "";
        for (int i = 0; i < cookies.length; i++) {
            String name = cookies[i].getName();
            String value = cookies[i].getValue();
            codeString = name + ":" + value + ";";
        }
        return codeString;
    }


    /**
     * 先验cookie，若cookie验证失败再尝试验证ticket
     */
    protected long getLoginYYUidOld(HttpServletRequest req, HttpServletResponse resp) {
        return loginService.getLoginYYUidOld(req,resp);
    }


    /**
     * <p>总是通过 cookie 验证登录信息 </p>
     *
     * <AUTHOR>
     * @date 2017年6月20日 下午5:17:06
     */
    public LoginInfoBean getLoginInfoBean(HttpServletRequest request, HttpServletResponse response, boolean isPrint) {
        return loginService.getLoginInfoBean(request,response,isPrint);
    }

    /**
     * 环境检查：办公网 + 灰度状态 才 通过
     *
     * <AUTHOR>
     * @date 2019年9月17日 下午3:28:31
     */
    protected void checkEnvForGrey(HttpServletRequest request, long actId) throws Exception {
        // 本地直接通过
        if (SysEvHelper.isLocal() || SysEvHelper.isDev()) {
            return;
        }

        // 非本地要检查办公ip
        String ip = RequestUtil.getRealIp(request);
        if (!SysEvHelper.isLocal()) {
            if (!isOfficeIp(ip)) {
                throw new Exception("只允许办公网IP发起请求， your ip:" + ip);
            }
        }

        // 生产或预发要检查灰度标志
        if (SysEvHelper.isDeploy() || SysEvHelper.isYuFa()) {
            if (!commonService.isGrey(actId)) {
                throw new Exception("生产/预发不是灰度状态，不可以操作！");
            }
        }
    }


    /**
     * 判断是否办公网ip
     *
     * <AUTHOR>
     * @date 2019年9月17日 下午2:59:04
     */
    protected synchronized boolean isOfficeIp(String ip) throws Exception {
        return offerIpService.isOfferIp(ip) || isInnerIp(ip);
    }

    /**
     * A类地址：10.0.0.0~************** （10.0.0.0/8）
     * B类地址：**********~**************（*********/12）
     * C类地址：***********~***************（***********/16）
     **/
    protected boolean isInnerIp(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return false;
        }
        boolean isInnerIp = ip.startsWith("10.") || ip.startsWith("172.16") || ip.startsWith("172.31") || ip.startsWith("192.168.");

        return isInnerIp;
    }

    /**
     * 检查 UID 是否为工号 UID
     * @param uid 待检查的 UID
     * @return 如果 UID 在工号 UID 范围内返回 true，否则返回 false
     */
    public static boolean isUIDWorkAccount(long uid) {
        return uid >= WORK_ACCOUNT_UID_START && uid <= WORK_ACCOUNT_UID_END;
    }
}
