package com.yy.gameecology.activity.client.thrift;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.yy.gameecology.common.annotation.Report;
import com.yy.thrift.verify_code.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022.03.14 11:06
 * 短信验证码client
 * 注意: 只申请了俩个接口权限
 * 	SRequestSmsCodeResp UVCRequestSmsCodeByMobile(1:SRequestSmsCode req),
 *  SVerifySmsCodeResp  UVCVerifySmsCodeByMobile(1:SVerifySmsCode req),
 *  其他接口均不可用!!!!!!!!!
 */
@Component
public class VerifyCodeClient {

    //udb sa
    public static final String APP_ID = "1241146055";

    public static final String APP_SECRET = "6e84b0f1_9e46";

    private static final Logger log = LoggerFactory.getLogger(VerifyCodeClient.class);

    @Reference(protocol = "nythrift", owner = "__", url = "${thrift.service-agent.url}", timeout = 30000, parameters = {"threads", "10"}, cluster = "failover")
    private verifycode_service.Iface proxy = null;

    @Reference(protocol = "nythrift", owner = "__", url = "${thrift.service-agent.url}", timeout = 30000, parameters = {"threads", "10"}, cluster = "failover"
            , retries = 2)
    private verifycode_service.Iface readProxy = null;

    public verifycode_service.Iface getProxy() {
        return proxy;
    }

    public verifycode_service.Iface getReadProxy() {
        return proxy;
    }

    @Report
    public SRequestSmsCodeResp sendSmsVerifyCodeByUid(long uid, String smsInfo, int expireTime) {
        SRequestSmsCode req = new SRequestSmsCode();
        req.setAuth(auth());
        req.setAppid(APP_ID);
        req.setAccount(String.valueOf(uid));
        req.setSms_info(smsInfo);
        req.setContext(StringUtils.EMPTY);
        req.setService_id("1");
        req.setBakmobile(0);
        req.setExpire_time(expireTime);
        req.setExtrainfo(MapUtils.EMPTY_MAP);
        try {

            SRequestSmsCodeResp resp = getProxy().UVCRequestSmsCodeByYyuid(req);
            if (resp == null || resp.getRescode() != 0) {
                log.error("sendSmsVerifyCodeByUid error,req:{},resp:{}", req, resp);
            }
            log.info("sendSmsVerifyCodeByUid response:{}", JSON.toJSONString(resp));
            return resp;
        } catch (Exception e) {
            log.error("sendVerifyCode exception:", e);
        }

        return null;
    }
    @Report
    public SVerifySmsCodeResp verifySmsCodeByUid(long uid, String code) {
        SVerifySmsCode req = new SVerifySmsCode();
        req.setAuth(auth());
        req.setAppid(APP_ID);
        req.setAccount(String.valueOf(uid));
        req.setContext(StringUtils.EMPTY);
        req.setService_id("1");
        req.setCode(code);
        req.setBakmobile(0);
        req.setExtrainfo(MapUtils.EMPTY_MAP);
        try {
            SVerifySmsCodeResp resp = getProxy().UVCVerifySmsCodeByYyuid(req);
            if (resp == null || resp.getRescode() != 0) {
                log.error("verifySmsCodeByUid error,req:{},rsp:{}", req, resp);
            }
            log.info("verifySmsCodeByUid response:{}", resp);
            return resp;
        } catch (Exception e) {
            log.error("verifySmsCodeByUid exception:", e);
        }

        return null;
    }

    /**
     *
     * @param mobile
     * @param smsInfo
     * @param expireTime 过期时间 单位:秒
     * @return
     */
    @Report
    public SRequestSmsCodeResp sendSmsVerifyCodeByMobile(String mobile, String smsInfo, int expireTime) {
        SRequestSmsCode req = new SRequestSmsCode();
        req.setAuth(auth());
        req.setAppid(APP_ID);
        req.setAccount(mobile);
        req.setSms_info(smsInfo);
        req.setContext(StringUtils.EMPTY);
        req.setService_id("1");
        req.setBakmobile(0);
        req.setExpire_time(expireTime);
        req.setExtrainfo(MapUtils.EMPTY_MAP);
        try {
            SRequestSmsCodeResp resp = getProxy().UVCRequestSmsCodeByMobile(req);
            if (resp == null || resp.getRescode() != 0) {
                log.error("sendSmsVerifyCodeByMobile error,req:{},rsp:{}", req, resp);
            }
            log.info("sendSmsVerifyCodeByMobile response:{}", JSON.toJSONString(resp));
            return resp;
        } catch (Exception e) {
            log.error("sendVerifyCode exception:", e);
        }
        return null;
    }
    @Report
    public SVerifySmsCodeResp verifySmsCodeByMobile(String mobile, String code) {
        SVerifySmsCode req = new SVerifySmsCode();
        req.setAuth(auth());
        req.setAppid(APP_ID);
        req.setAccount(mobile);
        req.setContext(StringUtils.EMPTY);
        req.setService_id("1");
        req.setCode(code);
        req.setBakmobile(0);
        req.setExtrainfo(MapUtils.EMPTY_MAP);
        try {
            SVerifySmsCodeResp resp = getProxy().UVCVerifySmsCodeByMobile(req);
            if (resp == null || resp.getRescode() != 0) {
                log.error("verifySmsCodeByMobile error,req:{},rsp:{}", req, resp);
            }
            log.info("verifySmsCodeByUid response:{}", resp);
            return resp;
        } catch (Exception e) {
            log.error("verifySmsCodeByUid exception:", e);
        }

        return null;
    }

    public AuthorizeMsg auth() {
        AuthorizeMsg auth = new AuthorizeMsg();
        auth.setAuthUser(APP_ID);
        auth.setAuthKey(APP_SECRET);
        auth.setKeyvalue(ImmutableMap.of("auth-type", "4"));
        return auth;
    }
}
