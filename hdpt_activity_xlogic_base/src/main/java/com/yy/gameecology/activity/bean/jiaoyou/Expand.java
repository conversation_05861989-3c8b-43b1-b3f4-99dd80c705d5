package com.yy.gameecology.activity.bean.jiaoyou;

/**
 * <AUTHOR> 2019/9/2
 */
public class Expand {

    private String target;
    private String targetName;
    private long targetPos;
    private String targetNick;
    private String sendNick;
    private int wayOfPlay;
    private int grabLoveType;
    private long guestSignSid;
    private ComboInfo comboInfo;
    private long enemySid;
    private long enemySsid;
    /**
     * 相亲玩法
     */
    private int grabLoveSubType;
    private boolean isWeekGift;

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public long getTargetPos() {
        return targetPos;
    }

    public void setTargetPos(long targetPos) {
        this.targetPos = targetPos;
    }

    public String getTargetNick() {
        return targetNick;
    }

    public void setTargetNick(String targetNick) {
        this.targetNick = targetNick;
    }

    public String getSendNick() {
        return sendNick;
    }

    public void setSendNick(String sendNick) {
        this.sendNick = sendNick;
    }

    public int getWayOfPlay() {
        return wayOfPlay;
    }

    public void setWayOfPlay(int wayOfPlay) {
        this.wayOfPlay = wayOfPlay;
    }

    public int getGrabLoveType() {
        return grabLoveType;
    }

    public void setGrabLoveType(int grabLoveType) {
        this.grabLoveType = grabLoveType;
    }

    public long getGuestSignSid() {
        return guestSignSid;
    }

    public void setGuestSignSid(long guestSignSid) {
        this.guestSignSid = guestSignSid;
    }

    public ComboInfo getComboInfo() {
        return comboInfo;
    }

    public void setComboInfo(ComboInfo comboInfo) {
        this.comboInfo = comboInfo;
    }

    public long getEnemySid() {
        return enemySid;
    }

    public void setEnemySid(long enemySid) {
        this.enemySid = enemySid;
    }

    public long getEnemySsid() {
        return enemySsid;
    }

    public void setEnemySsid(long enemySsid) {
        this.enemySsid = enemySsid;
    }

    public int getGrabLoveSubType() {
        return grabLoveSubType;
    }

    public void setGrabLoveSubType(int grabLoveSubType) {
        this.grabLoveSubType = grabLoveSubType;
    }

    public boolean isWeekGift() {
        return isWeekGift;
    }

    public void setWeekGift(boolean weekGift) {
        isWeekGift = weekGift;
    }
}
