package com.yy.gameecology.activity.bean.actlayer;


import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;

import java.util.Date;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-12-16 14:07
 **/
public class ShowTopTitleConfig {
    @ComponentAttrField(labelText = "需要展示的topN")
    private int topN;

    @ComponentAttrField(labelText = "总展示开始时间")
    private Date startTime;

    @ComponentAttrField(labelText = "总展示结束时间")
    private Date endTime;

    @ComponentAttrField(labelText = "展示Title")
    private String title;

    @ComponentAttrField(labelText = "一天前N秒展示", remark = "当dataModel=0时生效")
    private int dayBeginSeconds;

    @ComponentAttrField(labelText = "取数据模式", remark = "0->取前一天的 1->取当天的,默认0")
    private int dataModel = 0;

    @ComponentAttrField(labelText = "展示开始时间", remark = "当dateModel=1时生效,格式 HHmmss")
    private String startShowTime;

    @ComponentAttrField(labelText = "展示结束时间", remark = "当dateModel=1时生效,格式 HHmmss")
    private String endShowTime;

    public int getTopN() {
        return topN;
    }

    public void setTopN(int topN) {
        this.topN = topN;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getDayBeginSeconds() {
        return dayBeginSeconds;
    }

    public void setDayBeginSeconds(int dayBeginSeconds) {
        this.dayBeginSeconds = dayBeginSeconds;
    }

    public int getDataModel() {
        return dataModel;
    }

    public void setDataModel(int dataModel) {
        this.dataModel = dataModel;
    }

    public String getStartShowTime() {
        return startShowTime;
    }

    public void setStartShowTime(String startShowTime) {
        this.startShowTime = startShowTime;
    }

    public String getEndShowTime() {
        return endShowTime;
    }

    public void setEndShowTime(String endShowTime) {
        this.endShowTime = endShowTime;
    }
}
