package com.yy.gameecology.activity.service;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.thrift.hdztranking.Rank;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2019/11/26
 */
public interface ActService {

    /**
     * 送礼事件
     *
     * @param sendGiftEvent 三模板送礼事件
     */
    void onSendGiftEvent(SendGiftEvent sendGiftEvent);

    /**
     * 活动中台抽奖回调事件
     *
     * @param lotteryMsg
     */
    default void onLotteryAwardEvent(HdztAwardLotteryMsg lotteryMsg) {}

    /**
     * 活动尾灯的taskID-新活动必须定义
     * @return
     */
    default long getTailLightTaskId() { return 0;}

    /**
     * 活动尾灯的开始packageId（一级尾灯packageId），活动尾灯的packageId要配置连续递增-新活动必须定义
     * @return
     */

    default long getTailLightStartPackageId() { return 0;}

    /**
     * 活动尾灯的的最大等级，避免利用接口刷礼物发放--新活动必须定义
     * @return
     */
    default long getTailLightMaxLevel() { return 0;}

    /**
     * 中控的榜单--一些特殊的榜单处理
     * @param actId
     * @param rankId
     * @param phaseId
     * @param dateStr
     * @param count
     * @param pointedMember
     * @param ext
     * @return
     */
    default List<Rank> queryRankCache(long actId, long rankId, long phaseId, String dateStr, long count, String pointedMember, Map<String, String> ext){
        return Lists.newArrayList();
    }

}
