package com.yy.gameecology.activity.service;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.yy.ent.commons.yypclient.exception.BusinessException;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.RankInfo;
import com.yy.gameecology.activity.bean.rank.BabyRankItem;
import com.yy.gameecology.activity.bean.rank.RankItemUserAnchor;
import com.yy.gameecology.activity.bean.rank.RankValueItemBase;
import com.yy.gameecology.activity.bean.rank.UserRankItem;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.hdztranking.BatchRankingItem;
import com.yy.thrift.hdztranking.QueryRankingRequest;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 主榜 + 唯一CP
 *
 * <AUTHOR>
 * @date 2022/4/26 19:36
 **/
@Service
public class TopCPService {
    private static final String POINT_MEMBER_KEY = "pointMember";

    private static final String EMPTY = "EMPTY";

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private CommonService commonService;

    @Autowired
    private HdztRankServiceUseOne handleByUseOne;

    /**
     * 主榜 + 唯一CP
     * 1. 根据主榜查询榜单Top
     * 2. 获取主榜配置的唯一化CP榜,查询对应主播的TopCP
     * <p>
     * pointMember逻辑
     * 1. 主播视角
     * 2. 用户视角
     **/
    public RankInfo queryRankWithTopCP(GetRankReq rankReq) {
        long actId = rankReq.getActId();
        Clock clock = new Clock();
        RankInfo rankInfo = new RankInfo();
        //填充榜单配置信息
        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfigByCache(rankReq.getActId(), rankReq.getRankId(), rankReq.getPhaseId());
        if (rankingInfo == null) {
            log.warn("rankingInfo not found,actId:{},rankId:{}", rankReq.getActId(), rankReq.getRankId());
            return rankInfo;
        }

        String uniqCpRankIdStr = rankingInfo.getExtData().get("tb_ranking_config:uniq_cp_rank_id");
        if (StringUtil.isEmpty(uniqCpRankIdStr)) {
            log.warn("uniqCpRankId not found,actId:{},rankId:{}", rankReq.getActId(), rankReq.getRankId());
            return rankInfo;
        }
        long uniqCpRankId = Convert.toLong(uniqCpRankIdStr, 0);

        rankInfo.setCurrentTime(commonService.getNow(actId).getTime());
        rankInfo.setRankName(rankingInfo.getRankingName());
        clock.tag();

        rankReq.setShowType(1);

        // 这里设置的一定是当前主播
        // 主播视角：该主播与送礼最高的CP
        // 用户视角：该主播与该用户组成的CP
        // 基于当前用户和主播参数判断是哪种视角
        String pointMember = rankReq.getAnchorId();
        rankReq.setPointedMember(pointMember);
        boolean isUserView = isUserView(rankReq);

        // 查询主榜的榜单TopN
        rankInfo = handleByUseOne.handelByUseOne(rankReq, rankingInfo, rankInfo, Maps.newHashMap());
        clock.tag();

//        if (CollectionUtils.isEmpty(rankInfo.getList())) {
//            return rankInfo;
//        }

        // 获取主榜各主播对应的top1 CP
        List<RankValueItemBase> userItems = rankInfo.getList();
        Map<String, String> userItemIdMap = buildRequestUniqCpData(rankingInfo, rankReq, userItems, rankInfo.getPointedMember(), uniqCpRankIdStr, isUserView);
        Map<String, String> data = hdztRankingThriftClient.invokeRetry(200, actId, userItemIdMap, "queryUniqCp", 2);
        clock.tag();

        // 构造cp榜
        List<Rank> cpRank = convertToCpRank(data);
        rankReq.setRankId(uniqCpRankId);
        List<Object> cpRankData = handleByUseOne.fillRankingInfo(rankReq, cpRank, Maps.newHashMap());
        RankInfo cpRankInfo = new RankInfo();
        cpRankInfo.setList(cpRankData);
        clock.tag();

        // 构造响应列表

        List<RankItemUserAnchor> topCPItems = cpRankInfo.getList();
        Map<Long, RankItemUserAnchor> topCPItemMap = buildCPItemMap(topCPItems);
        RankItemUserAnchor pointCPItem = topCPItemMap.get(-1L);
        List<RankItemUserAnchor> newList = buildNewList(topCPItemMap, userItems);
        rankInfo.setList(newList);

        Object pointRankMember = null;
        // 主播视角
        if (rankInfo.getPointedMember() != null && !isUserView) {
            BabyRankItem babyRankItem = (BabyRankItem) rankInfo.getPointedMember();
            Long key = Convert.toLong(babyRankItem.getKey());
            pointCPItem = topCPItemMap.get(key);
            if (pointCPItem == null) {
                // 主播未上榜
                pointCPItem = new RankItemUserAnchor();
                pointCPItem.setBabyRankItem(babyRankItem);
            }
        }

        // 用户视角
        if (pointCPItem != null) {
            RankItemUserAnchor rankItemUserAnchor = new RankItemUserAnchor();
            rankItemUserAnchor.setUserRankItem(pointCPItem.getUserRankItem());
            rankItemUserAnchor.setRank(pointCPItem.getRank());
            rankItemUserAnchor.setBabyRankItem(pointCPItem.getBabyRankItem());
            BabyRankItem babyRankItem = (BabyRankItem) rankInfo.getPointedMember();
            if (babyRankItem != null) {
                rankItemUserAnchor.getBabyRankItem().setRank(babyRankItem.getRank());
                rankItemUserAnchor.getBabyRankItem().setValue(babyRankItem.getValue());
                pointRankMember = rankItemUserAnchor;
            }
        }

        if (StringUtil.isNotBlank(pointMember) && rankReq.getLoginUid() > 0) {
            rankInfo.setPointedMember(pointRankMember);
        } else {
            rankInfo.setPointedMember(null);
        }

        log.info("queryRankWithTopCP done {}", clock.tag());

        return rankInfo;
    }

    private Map<Long, RankItemUserAnchor> buildCPItemMap(List<RankItemUserAnchor> topCPItems) {
        Map<Long, RankItemUserAnchor> topCPItemMap = Maps.newHashMap();
        for (RankItemUserAnchor topCPItem : topCPItems) {
            if (Objects.equals(topCPItem.getItemDesc(), POINT_MEMBER_KEY)) {
                topCPItemMap.put(-1L, topCPItem);
                continue;
            }
            topCPItemMap.put(topCPItem.getBabyRankItem().getUid(), topCPItem);
        }

        return topCPItemMap;
    }

    private List<RankItemUserAnchor> buildNewList(Map<Long, RankItemUserAnchor> topCPItemMap, List<RankValueItemBase> userItems) {
        List<RankItemUserAnchor> newList = new ArrayList<>(userItems.size());
        for (RankValueItemBase userRankItem : userItems) {
            Long key = Convert.toLong(userRankItem.getKey());
            if (!topCPItemMap.containsKey(key)) {
                continue;
            }
            RankItemUserAnchor rankItemUserAnchor = new RankItemUserAnchor();
            UserRankItem userItem = topCPItemMap.get(key).getUserRankItem();
            BabyRankItem babyItem = (BabyRankItem) userRankItem;
            if (userItem == null || babyItem == null) {
                continue;
            }
            rankItemUserAnchor.setUserRankItem(userItem);
            rankItemUserAnchor.setRank(userItem.getRank());
            rankItemUserAnchor.setBabyRankItem(babyItem);
            newList.add(rankItemUserAnchor);
        }

        return newList;
    }

    private Map<String, String> buildRequestUniqCpData(RankingInfo rankingInfo, GetRankReq rankReq
            , List<RankValueItemBase> userItems, Object pointMember, String uniqCpRankIdStr, boolean isUserView) {
        String queryDateStr = rankingInfo.getTimeKey() == 0 ? "" : rankReq.getDateStr();
        Map<String, String> userItemIdMap = userItems.stream().collect(
                Collectors.toMap(RankValueItemBase::getKey, RankValueItemBase::getKey, (oldValue, newValue) -> newValue)
        );
        if (pointMember != null) {
            addPointMember(pointMember, userItemIdMap);
        }
        userItemIdMap.put("actId", "" + rankReq.getActId());
        userItemIdMap.put("rankId", uniqCpRankIdStr);
        userItemIdMap.put("dayStr", queryDateStr);
        userItemIdMap.put("phaseId", rankReq.getPhaseId() + "");

        // 如果是用户视角，这里需要直接构造好：主播|用户
        if (isUserView) {
            String currentUserPair = rankReq.getAnchorId() + "|" + rankReq.getLoginUid();
            userItemIdMap.put(POINT_MEMBER_KEY, currentUserPair);
        }
        return userItemIdMap;
    }

    private void addPointMember(Object pointMember, Map<String, String> userItemIdMap) {
        if (pointMember != null) {
            RankValueItemBase pointMemberItem = (RankValueItemBase) pointMember;
            userItemIdMap.put(pointMemberItem.getKey(), pointMemberItem.getKey());
        }
    }

    private boolean isUserView(GetRankReq rankReq) {
        return !Objects.equals(rankReq.getLoginUid() + "", rankReq.getAnchorId());
    }

    /**
     * 查询cp榜top
     * 这里不需要处理pointMember
     **/
    public RankInfo queryCPRankTop(GetRankReq rankReq) {
        long actId = rankReq.getActId();
        Clock clock = new Clock();
        RankInfo rankInfo = new RankInfo();
        //填充榜单配置信息
        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(rankReq.getActId(), rankReq.getRankId(), rankReq.getPhaseId());
        if (rankingInfo == null) {
            log.warn("rankingInfo not found,actId:{},rankId:{}", rankReq.getActId(), rankReq.getRankId());
            return rankInfo;
        }

        String uniqCpRankIdStr = rankingInfo.getExtData().get("tb_ranking_config:uniq_cp_rank_id");
        if (StringUtil.isEmpty(uniqCpRankIdStr)) {
            log.warn("uniqCpRankId not found,actId:{},rankId:{}", rankReq.getActId(), rankReq.getRankId());
            return rankInfo;
        }
        long uniqCpRankId = Convert.toLong(uniqCpRankIdStr, 0);

        rankReq.setShowType(1);
        rankReq.setPointedMember("");
        clock.tag();

        rankInfo = handleByUseOne.handelByUseOne(rankReq, rankingInfo, rankInfo, Maps.newHashMap());
        clock.tag();
        if (CollectionUtils.isEmpty(rankInfo.getList())) {
            return rankInfo;
        }

        // 获取最高CP
        List<RankValueItemBase> userItems = rankInfo.getList();
        Map<String, String> userItemIdMap = buildRequestUniqCpData(rankingInfo, rankReq, userItems, null, uniqCpRankIdStr, false);
        Map<String, String> data = hdztRankingThriftClient.invokeRetry(200, actId, userItemIdMap, "queryUniqCp", 2);
        clock.tag();
        // 构造cp榜
        List<Rank> cpRank = convertToCpRank(data);
        rankReq.setRankId(uniqCpRankId);
        List<Object> cpRankData = handleByUseOne.fillRankingInfo(rankReq, cpRank, Maps.newHashMap());
        RankInfo cpRankInfo = new RankInfo();
        cpRankInfo.setList(cpRankData);
        clock.tag();

        List<RankItemUserAnchor> topCPItems = cpRankInfo.getList();
        Map<Long, RankItemUserAnchor> topCPItemMap = buildCPItemMap(topCPItems);
        List<RankItemUserAnchor> newList = buildNewList(topCPItemMap, userItems);
        rankInfo.setList(newList);
        log.info("queryCPRankTop done {}", clock.tag());

        return rankInfo;
    }

    private List<Rank> convertToCpRank(Map<String, String> data) {
        List<Rank> cpRank = new ArrayList<>();
        for (Map.Entry<String, String> entry : data.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            Rank rank = new Rank();
            rank.setRank(0);
            String member = value.split("-")[0];
            rank.setMember(member);
            rank.setScore(Convert.toLong(value.split("-")[1], 0));
            rank.setItemDesc(key);
            cpRank.add(rank);
        }

        return cpRank;
    }

    /**
     * 查询cp榜top列表
     **/
    public Map<Long, RankItemUserAnchor> listCPRankTop(GetRankReq rankReq) throws BusinessException {
        Clock clock = new Clock();
        long actId = rankReq.getActId();
        // 填充榜单配置信息
        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(rankReq.getActId(), rankReq.getRankId(), rankReq.getPhaseId());
        if (rankingInfo == null) {
            log.warn("rankingInfo not found,actId:{},rankId:{}", rankReq.getActId(), rankReq.getRankId());
            return Maps.newHashMap();
        }

        String uniqCpRankIdStr = rankingInfo.getExtData().get("tb_ranking_config:uniq_cp_rank_id");
        if (StringUtil.isEmpty(uniqCpRankIdStr)) {
            log.warn("uniqCpRankId not found,actId:{},rankId:{}", rankReq.getActId(), rankReq.getRankId());
            return Maps.newHashMap();
        }
        long uniqCpRankId = Convert.toLong(uniqCpRankIdStr, 0);

        // 必须制定的是天
        String queryDateStr = rankReq.getDateStr();
        Date queryDate = DateUtil.getDate(queryDateStr, DateUtil.PATTERN_TYPE2);
        if (queryDate == null) {
            throw new BusinessException("时间格式错误:" + queryDateStr, 500);
        }

        List<String> hourStrs = getQueryDateStr(queryDateStr, queryDate, rankingInfo.getCalBeginTime());
        if (hourStrs == null) {
            return Maps.newHashMap();
        }
        clock.tag();
        Map<Long, Rank> rankMap = queryBatchRankingWithCache(hourStrs, rankReq);
        clock.tag();


        // 响应数据
        // key --> 时间点
        // value --> 主播id|CP用户-用户分数
        Map<String, String> data = queryUniqCpListWithCache(actId, rankReq.getPhaseId(), uniqCpRankIdStr, rankMap);
        clock.tag();
        // 构造cp榜
        List<Rank> cpRank = convertToCpRank(data);

        List<Map.Entry<String, String>> entryList = new ArrayList<>(data.entrySet());
        for (Map.Entry<String, String> entry : entryList) {
            Rank rank = new Rank();
            rank.setRank(0);
            rank.setMember(entry.getValue().split("-")[0]);
            rank.setScore(Convert.toLong(entry.getValue().split("-")[1], 0));
            rank.setItemDesc(entry.getKey());
            cpRank.add(rank);
        }

        rankReq.setRankId(uniqCpRankId);
        List<Object> cpRankData = handleByUseOne.fillRankingInfo(rankReq, cpRank, Maps.newHashMap());
        clock.tag();

        Map<Long, RankItemUserAnchor> result = new LinkedHashMap<>();
        for (Object cpRankDatum : cpRankData) {
            RankItemUserAnchor item = (RankItemUserAnchor) cpRankDatum;
            Rank rank = rankMap.get(Convert.toLong(item.getItemDesc()));
            item.getBabyRankItem().setValue(rank.getScore());
            result.put(Convert.toLong(item.getItemDesc()), item);
        }

        log.info("listCPRankTop done {}", clock.tag());

        return result;
    }

    private List<String> getQueryDateStr(String queryDateStr, Date queryDate, long beginTime) {
        boolean isToday = DateUtil.getDayBeginTime(new Date()) == queryDate;
        int endHour = 23;
        if (isToday) {
            int hour = DateUtil.getHours();
            endHour = hour;
        }
        if (endHour < 0) {
            return null;
        }

        List<String> result = new ArrayList<>();
        for (int index = 0; index <= endHour; index++) {
            String hourStr = queryDateStr + index / 10 + "" + index % 10;
            if (DateUtil.getDate(hourStr, DateUtil.PATTERN_TYPE7).getTime() < beginTime) {
                continue;
            }

            result.add(hourStr);
        }

        return result;
    }

    private LoadingCache<Long, Rank> cache = CacheBuilder.newBuilder()
            .maximumSize(256)
            .expireAfterWrite(300, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, Rank>() {
                @Override
                public Rank load(Long aLong) {
                    Rank rank = new Rank();
                    rank.setMember(EMPTY);
                    return rank;
                }
            });

    private Map<Long, Rank> queryBatchRankingWithCache(List<String> hourStrs, GetRankReq rankReq) {
        Map<Long, Rank> result = Maps.newHashMap();
        String currentHour = DateUtil.format(commonService.getNow(rankReq.getActId()), DateUtil.PATTERN_TYPE7);
        List<String> notExistHours = new ArrayList<>();
        for (String hourStr : hourStrs) {
            // 当前小时的数据实时查询
            if (Objects.equals(currentHour, hourStr)) {
                notExistHours.add(hourStr);
                continue;
            }
            Long key = Convert.toLong(hourStr);
            try {
                Rank rank = cache.get(key);
                if (Objects.equals(EMPTY, rank.getMember())) {
                    notExistHours.add(hourStr);
                    continue;
                }
                result.put(key, rank);
            } catch (ExecutionException e) {
                notExistHours.add(hourStr);
            }
        }
        if (CollectionUtils.isEmpty(notExistHours)) {
            return result;
        }

        Map<Long, Rank> remoteResult = queryBatchRanking(notExistHours, rankReq);
        for (Map.Entry<Long, Rank> entry : remoteResult.entrySet()) {
            if (entry.getValue() != null) {
                cache.put(entry.getKey(), entry.getValue());
                result.put(entry.getKey(), entry.getValue());
            }
        }

        return result;
    }

    private Map<Long, Rank> queryBatchRanking(List<String> hourStrs, GetRankReq rankReq) {
        Map<String, QueryRankingRequest> batchQueryRequestMap = Maps.newHashMap();
        for (String hourStr : hourStrs) {
            QueryRankingRequest request = new QueryRankingRequest();
            request.setActId(rankReq.getActId());
            request.setRankingId(rankReq.getRankId());
            request.setRankingCount(1);
            request.setPhaseId(rankReq.getPhaseId());
            request.setDateStr(hourStr);
            request.setRankType("1");
            batchQueryRequestMap.put(hourStr, request);
        }
        Map<String, BatchRankingItem> rankingItemMap = hdztRankingThriftClient.queryBatchRanking(batchQueryRequestMap, Maps.newHashMap());
        Map<Long, Rank> resultMap = Maps.newHashMap();
        for (String hourStr : hourStrs) {
            resultMap.put(Convert.toLong(hourStr), CollectionUtils.isEmpty(rankingItemMap.get(hourStr).getData()) ? null : rankingItemMap.get(hourStr).getData().get(0));
        }

        return new LinkedHashMap<>(resultMap);
    }

    private LoadingCache<String, String> topCPCache = CacheBuilder.newBuilder()
            .maximumSize(256)
            .expireAfterWrite(300, TimeUnit.SECONDS)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String aLong) {
                    return EMPTY;
                }
            });

    private Map<String, String> queryUniqCpListWithCache(long actId, long phaseId, String uniqCpRankIdStr, Map<Long, Rank> rankMap) {
        // key --> 时间点
        // value --> 对应该阶段的第一名主播
        Map<String, String> requestData = Maps.newHashMap();
        Map<String, String> resultData = Maps.newHashMap();
        String currentHour = DateUtil.format(commonService.getNow(actId), DateUtil.PATTERN_TYPE7);
        for (Map.Entry<Long, Rank> entry : rankMap.entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            String hourStr = entry.getKey() + "";
            // 当前小时的数据实时查询
            if (Objects.equals(currentHour, hourStr)) {
                requestData.put(hourStr, entry.getValue().getMember());
                continue;
            }
            try {
                String cacheCPData = topCPCache.get(entry.getKey() + "");
                if (Objects.equals(cacheCPData, EMPTY)) {
                    requestData.put(entry.getKey() + "", entry.getValue().getMember());
                    continue;
                }
                resultData.put(entry.getKey() + "", cacheCPData);
            } catch (Exception ex) {
            }
        }

        if (MapUtils.isNotEmpty(requestData)) {
            requestData.put("actId", "" + actId);
            requestData.put("rankId", uniqCpRankIdStr);
            requestData.put("phaseId", "" + phaseId);
            Map<String, String> data = hdztRankingThriftClient.invokeRetry(200, actId, requestData, "queryUniqCpList", 2);
            for (Map.Entry<String, String> entry : data.entrySet()) {
                if (entry.getValue() != null) {
                    topCPCache.put(entry.getKey(), entry.getValue());
                    resultData.put(entry.getKey(), entry.getValue());
                }
            }
        }

        return resultData;
    }
}
