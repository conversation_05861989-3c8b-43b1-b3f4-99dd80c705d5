package com.yy.gameecology.activity.bean.mq;

import com.yy.gameecology.activity.bean.mq.hdzk.HdzkBaseEvent;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 轮次结算完成事件
 */
@Getter
@Setter
public class HdzkAovRoundSettledEvent extends HdzkBaseEvent {

    public HdzkAovRoundSettledEvent() {
        super(HdzkBaseEvent.AOV_ROUND_SETTLED_EVENT_URI);
    }

    protected long phaseId;

    protected int roundNum;

    protected String roundName;

    /**
     * 明确获胜晋级的队伍ID
     */
    protected List<Long> advancedTeamIds;

    /**
     * 通过补位晋级的队伍ID
     */
    protected List<Long> substituteTeamIds;

    /**
     * 被淘汰的队伍ID（不包含空节点）
     */
    protected List<Long> eliminatedTeamIds;

    protected Date settleTime;
}
