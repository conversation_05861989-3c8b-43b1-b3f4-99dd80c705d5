package com.yy.gameecology.activity.bean;


/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-12-16 17:17
 **/
public class CarveYCoinConfig {
    /**
     * anchorAvg#均分模式  瓜分Y币=总Y币数/当日完成5级任务的主持人数
     */
    private String carveMode;

    /**
     * 任务榜单ID
     */
    private long rankId;

    /**
     * 任务阶段id
     */
    private long phaseId;

    /**
     * 完成任务最小资格
     */
    private long minTaskId;

    /**
     * 瓜分的总Y币
     */
    private int totalYCoins;

    /**
     * 备注
     */
    private String remark;

    public String getCarveMode() {
        return carveMode;
    }

    public void setCarveMode(String carveMode) {
        this.carveMode = carveMode;
    }

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public long getMinTaskId() {
        return minTaskId;
    }

    public void setMinTaskId(long minTaskId) {
        this.minTaskId = minTaskId;
    }

    public int getTotalYCoins() {
        return totalYCoins;
    }

    public void setTotalYCoins(int totalYCoins) {
        this.totalYCoins = totalYCoins;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
