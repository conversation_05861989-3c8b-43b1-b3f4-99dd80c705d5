package com.yy.gameecology.activity.bean;

import java.util.Date;

/**
 * desc:展示“结算中”状态配置
 *
 * @createBy 曾文帜
 * @create 2020-11-20 16:13
 **/
public class SettleViewConfig {

    private long rankId;

    private long phaseId;

    private String dayCode;

    private Date startTime;

    private Date endTime;

    /**
     * 备注
     */
    private String dest;


    /**
     * 如果设置成true，规则命中后，还需要redis设置值才展示成结算中，通常用于手工全局设置，例如要手工设置pk信息的时候，就需要doubleCheck
     */
    private boolean doubleCheck;

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public String getDayCode() {
        return dayCode;
    }

    public void setDayCode(String dayCode) {
        this.dayCode = dayCode;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getDest() {
        return dest;
    }

    public void setDest(String dest) {
        this.dest = dest;
    }

    public boolean isDoubleCheck() {
        return doubleCheck;
    }

    public void setDoubleCheck(boolean doubleCheck) {
        this.doubleCheck = doubleCheck;
    }
}
