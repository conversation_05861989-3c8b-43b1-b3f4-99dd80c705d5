package com.yy.gameecology.activity.bean.wzry;

import com.google.common.collect.Sets;
import lombok.Data;

import java.util.Set;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-24 18:05
 **/
@Data
public class GameResult {
    Integer winnerTeam;

    int state;
    /**
     * 比赛胜利者的 seat_team
     */
    Set<String> winnerSeat = Sets.newHashSet();

    /**
     * 参与了游戏的成员
     * seat_team
     */
    Set<String> inGameSeat = Sets.newHashSet();


    //---游戏异常结束状态 "battle_status":500,"dynamic_info":{"battle_game_room_info":{"status":300}}}]}
    int battleStatus;

    int roomStatus;

    /**
     * 赛宝返回的原始数据
     */
    String gameResult;
}
