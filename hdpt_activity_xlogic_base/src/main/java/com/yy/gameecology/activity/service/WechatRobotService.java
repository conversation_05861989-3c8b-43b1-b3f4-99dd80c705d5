package com.yy.gameecology.activity.service;

import com.yy.gameecology.common.bean.WechatRobotMsg;
import com.yy.gameecology.common.consts.Const;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR> 2019/12/4
 */
@Service
public class WechatRobotService {

    private static final Logger log = LoggerFactory.getLogger(WechatRobotService.class);

    private final RestTemplate restTemplate = new RestTemplate();
    private final HttpHeaders headers = new HttpHeaders();

    {
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
    }

    public static final String ACTIVITY_ROBOT_WEBHOOK = "activity.robot.webhook";

    public static final String SYSTEM_ROBOT_WEBHOOK = "system.robot.webhook";

    /**
     * 帮助文档: https://work.weixin.qq.com/help?person_id=1&doc_id=13376&from=search&helpType=
     *
     * @param robotKey 机器人key, 从Const.PM中匹配webhook
     * @param msg      消息体
     */
    @Async
    public void notify(String robotKey, WechatRobotMsg msg) {
        String webhook = Const.GEPM.getParamValue(robotKey, null);
        if (StringUtils.isEmpty(webhook)) {
            log.error("[notify] 请配置机器人webhook key:{}", robotKey);
            return;
        }
        nofityContent(webhook, msg);
    }

    @Async
    public void nofityContent(String webhook, WechatRobotMsg msg) {
        try {
            if (StringUtils.isEmpty(webhook)) {
                log.error("[notify] 请配置机器人webhook:{}", webhook);
                return;
            }
            String resp = restTemplate.postForObject(webhook, new HttpEntity<>(msg, headers), String.class);
            // 成功: {"errcode":0,"errmsg":"ok"} 暂忽略失败
            // 一分钟只能发20次, 更好的做法是放到队列中限流执行
            log.info("[notify] webhook:{}, msg:{}, resp:{}", webhook, msg, resp);
        } catch (Exception e) {
            log.error("[notify] err:{}, robotKey:{}, msg:{}", e.getMessage(), webhook, msg, e);
        }
    }

}
