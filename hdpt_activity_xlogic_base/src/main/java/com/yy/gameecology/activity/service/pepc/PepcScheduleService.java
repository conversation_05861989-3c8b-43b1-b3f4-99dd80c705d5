package com.yy.gameecology.activity.service.pepc;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.mq.hdzk.PepcGameEndEvent;
import com.yy.gameecology.activity.bean.mq.hdzk.PepcPhaseSettleEvent;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.dao.mysql.PepcDao;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.PepcConst;
import com.yy.gameecology.common.db.mapper.pepc.*;
import com.yy.gameecology.common.db.model.gameecology.pepc.*;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.MD5SHAUtil;
import com.yy.gameecology.common.utils.MyListUtils;
import com.yy.gameecology.hdzj.bean.pepc.PepcActInfoVo;
import com.yy.gameecology.hdzj.bean.pepc.PepcMyGameRoundItemVo;
import com.yy.gameecology.hdzj.bean.pepc.PepcMyGameRoundVo;
import com.yy.gameecology.hdzj.bean.pepc.PepcPhaseInfoVo;
import com.yy.gameecology.hdzj.element.component.attr.PepcPhaseComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.UserPaidGrowthComponentAttr;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-02 20:20
 **/
@Service
public class PepcScheduleService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PepcDao pepcDao;

    @Autowired
    private PepcTeamScheduleMapper pepcTeamScheduleMapper;
    @Autowired
    private PepcPhaseInfoMapper pepcPhaseInfoMapper;

    @Autowired
    private PepcTeamGroupService pepcTeamGroupService;

    @Autowired
    private PepcInitGamePhaseDataService pepcInitGamePhaseDataService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private PepcRankService pepcRankService;

    @Lazy
    @Autowired
    private PepcScheduleService myself;

    @Autowired
    private PepcGameMapper pepcGameMapper;

    @Autowired
    private PepcTeamMemberMapper pepcTeamMemberMapper;

    @Autowired
    private PepcGameTeamMapper pepcGameTeamMapper;

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private UserInfoService userInfoService;


    public void settleSchedule(PepcPhaseComponentAttr attr, long actId) {
        List<PepcPhaseInfo> pepcPhaseInfos = pepcDao.getAllPepcPhaseInfo(actId);
        //待结算
        Optional<PepcPhaseInfo> needSettlePhase = pepcPhaseInfos
                .stream()
                .filter(p -> p.getState().equals(PepcConst.PhaseInfoState.INIT_DATA_DONE)).findFirst();
        if (needSettlePhase.isEmpty()) {
            return;
        }
        PepcPhaseInfo pepcPhaseInfo = needSettlePhase.get();
        int allAmount = pepcTeamScheduleMapper.countPepcTeamSchedule(actId, pepcPhaseInfo.getPhaseId(), PepcConst.PepcTeamScheduleState.INIT, null);
        int completeRoundAmount = pepcTeamScheduleMapper.countPepcTeamSchedule(actId, pepcPhaseInfo.getPhaseId(), PepcConst.PepcTeamScheduleState.INIT, pepcPhaseInfo.getTotalRound());
        boolean roundComplete = allAmount > 0 && allAmount == completeRoundAmount;
        //确保该阶段所有小组都是最终状态才开始结算
        if (!roundComplete) {
            log.info("settleSchedule actId:{},phaseId:{},allAmount:{},roundComplete:{}", actId, pepcPhaseInfo.getPhaseId(), allAmount, completeRoundAmount);
            return;
        }


        Optional<Integer> maxPhaseId = pepcPhaseInfos.stream().map(PepcPhaseInfo::getPhaseId).max(Integer::compareTo);
        boolean lastPhase = maxPhaseId.get().equals(pepcPhaseInfo.getPhaseId());
        log.info("begin settleSchedule,actId:{},pahseId:{}", actId, pepcPhaseInfo.getPhaseId());
        if (!lastPhase) {
            //中间阶段，结算到下1个阶段
            settleToNextSchedule(attr, actId, pepcPhaseInfo);
        } else {
            //最后阶段，结算发奖
            settleLastSchedule(attr, actId, pepcPhaseInfo);
        }
    }

    public void settleToNextSchedule(PepcPhaseComponentAttr attr, long actId, PepcPhaseInfo pepcPhaseInfo) {

        PepcPhaseInfo nextPhaseInfo = pepcDao.getPepcPhaseInfo(actId, pepcPhaseInfo.getPhaseId() + 1);

        List<PepcTeamSchedule> schedules = pepcDao.getPepcTeamSchedule(actId, pepcPhaseInfo.getPhaseId());
        long totalGroup = schedules.stream().map(PepcTeamSchedule::getGroupCode).distinct().count();
        int advanceTotalSize = resolveAdvanceTeamAmount(schedules.size(), pepcPhaseInfo.getAdvanceRule());
        long groupAdvanceSize = advanceTotalSize / totalGroup;

        log.info("settleToNextSchedule,actId:{},advanceTotalSize:{},groupAdvanceSize:{}", actId, advanceTotalSize, groupAdvanceSize);

        //积分大于0的才能晋级
        List<PepcTeamSchedule> effectSchedules = schedules.stream().filter(p -> p.getScore() > 0).toList();
        Map<String, List<PepcTeamSchedule>> effectScheduleGroup = effectSchedules
                .stream()
                .collect(Collectors.groupingBy(PepcTeamSchedule::getGroupCode));

        //最终能够晋级的队伍
        List<PepcTeamSchedule> advancePepcTeamSchedule = Lists.newArrayList();
        for (String group : effectScheduleGroup.keySet()) {
            List<PepcTeamSchedule> list = effectScheduleGroup.get(group);
            List<PepcTeamSchedule> sortList = list.stream()
                    .sorted(Comparator.comparingLong(PepcTeamSchedule::getScore).reversed())
                    .collect(Collectors.toList());
            List<PepcTeamSchedule> advance = MyListUtils.subList(sortList, 0, (int) groupAdvanceSize);
            advancePepcTeamSchedule.addAll(advance);
        }


        Set<Long> advanceTeamId = advancePepcTeamSchedule.stream().map(PepcTeamSchedule::getTeamId).collect(Collectors.toSet());
        //淘汰的队伍
        List<PepcTeamSchedule> elePepcTeamSchedule = schedules
                .stream()
                .filter(p -> !advanceTeamId.contains(p.getTeamId()))
                .toList();


        List<Long> advancePepcScheduleId = advancePepcTeamSchedule.stream().map(PepcTeamSchedule::getId).toList();
        Set<Long> advancePepcTeamId = advancePepcTeamSchedule.stream().map(PepcTeamSchedule::getTeamId).collect(Collectors.toSet());
        List<Long> elePcpcScheduleId = elePepcTeamSchedule.stream().map(PepcTeamSchedule::getId).toList();

        List<PepcTeam> pepcTeams = pepcDao.getAllTeam(actId);
        List<PepcTeam> advanceTeam = pepcTeams.stream().filter(p -> advancePepcTeamId.contains(p.getId())).toList();
        boolean nextPhaseIsLast = nextPhaseInfo.getPhaseId().equals(attr.getLastPhaseId());
        List<PepcTeamGroup> advanceTeamGroup = PepcTeamGroupService.divideTeamsIntoGroups(attr, advanceTeam, nextPhaseInfo.getPhaseId(), nextPhaseIsLast);


        myself.saveSettlePhaseData(actId, pepcPhaseInfo, advancePepcScheduleId, elePcpcScheduleId, advanceTeamGroup, nextPhaseInfo);

    }

    @Transactional(rollbackFor = Exception.class)
    public void saveSettlePhaseData(long actId, PepcPhaseInfo pepcPhaseInfo,
                                    List<Long> advancePepcScheduleId, List<Long> elePcpcScheduleId,
                                    List<PepcTeamGroup> advanceTeamGroup,
                                    PepcPhaseInfo nextPhaseInfo) {
        //---- 保存晋级数据


        //更新阶段数据
        int phaseUpdateRes = pepcPhaseInfoMapper.updatePepcPhaseInfoStatus(actId, pepcPhaseInfo.getPhaseId(), PepcConst.PhaseInfoState.SETTLE, PepcConst.PhaseInfoState.INIT_DATA_DONE);
        if (phaseUpdateRes <= 0) {
            log.error("update phase state failed,is already settlle?actId:{} phaseId:{}", actId, pepcPhaseInfo.getPhaseId());
            return;
        }
        //更新当前赛程表晋级状态 pepc_team_schedule ---> state
        if (CollectionUtils.isNotEmpty(advancePepcScheduleId)) {
            pepcTeamScheduleMapper.updateStatus(actId, advancePepcScheduleId, PepcConst.PepcTeamScheduleState.ADVANCE, PepcConst.PepcTeamScheduleState.INIT);
        }
        if (CollectionUtils.isNotEmpty(elePcpcScheduleId)) {
            pepcTeamScheduleMapper.updateStatus(actId, elePcpcScheduleId, PepcConst.PepcTeamScheduleState.ELIMINATE, PepcConst.PepcTeamScheduleState.INIT);
        }
        //插入新分组数据 pepc_team_group
        pepcDao.saveTeamGroup(advanceTeamGroup);

        //如果有下1个阶段赛程，则标记可以初始化下1个赛程数据
        if (nextPhaseInfo != null) {
            pepcPhaseInfoMapper.updatePepcPhaseInfoStatus(actId, nextPhaseInfo.getPhaseId(), PepcConst.PhaseInfoState.NEED_INIT_DATA, PepcConst.PhaseInfoState.INIT);
        }

        //发布结算成功事件，用于发奖等
        PepcPhaseSettleEvent pepcPhaseSettleEvent = new PepcPhaseSettleEvent();
        pepcPhaseSettleEvent.setActId(actId);
        pepcPhaseSettleEvent.setSeq("phase_settle_" + actId + "_" + pepcPhaseInfo.getPhaseId());
        pepcPhaseSettleEvent.setPhaseId(pepcPhaseInfo.getPhaseId());
        pepcPhaseSettleEvent.setLastPhase(nextPhaseInfo == null);
        kafkaService.sendHdzkCommonEvent(pepcPhaseSettleEvent);
    }

    private int resolveAdvanceTeamAmount(int teamCount, String rule) {
        String[] ruleData = rule.split("\\|");
        String type = ruleData[0];
        //暂时只支持这种类型
        if (!Const.ONESTR.equals(type)) {
            throw new RuntimeException("not support advanceType:" + type);
        }

        String ruleConfig = ruleData[1];
        Map<Integer, Integer> ruleMap = JSON.parseObject(ruleConfig, Map.class);
        int advancingTeams = 0;
        // 遍历规则映射
        for (Map.Entry<Integer, Integer> entry : ruleMap.entrySet()) {
            int threshold = Convert.toInt(entry.getKey(), 0);
            int advancing = entry.getValue();
            // 如果参赛队伍数量大于等于最小参赛队伍门槛
            if (teamCount >= threshold) {
                advancingTeams = advancing;
            }
        }
        return advancingTeams;
    }

    public void settleLastSchedule(PepcPhaseComponentAttr attr, long actId, PepcPhaseInfo pepcPhaseInfo) {

        List<PepcTeamSchedule> schedules = pepcDao.getPepcTeamSchedule(actId, pepcPhaseInfo.getPhaseId());
        List<PepcTeamSchedule> joinGameTeam = schedules
                .stream()
                .filter(p -> p.getScore() > 0).sorted(Comparator.comparing(PepcTeamSchedule::getScore).reversed())
                .toList();

        List<PepcTeamSchedule> needAward = Lists.newArrayList();
        for (int i = 0; i < joinGameTeam.size(); i++) {
            PepcTeamSchedule schedule = joinGameTeam.get(i);
            int rank = i + 1;
            if (attr.getAwardInfos().stream().anyMatch(p -> p.getRank() == rank)) {
                needAward.add(schedule);
            }
        }

        Set<Long> advanceTeamId = needAward.stream().map(PepcTeamSchedule::getTeamId).collect(Collectors.toSet());
        //淘汰的队伍
        List<PepcTeamSchedule> elePepcTeamSchedule = schedules
                .stream()
                .filter(p -> !advanceTeamId.contains(p.getTeamId()))
                .toList();


        List<Long> advancePepcScheduleId = needAward.stream().map(PepcTeamSchedule::getId).toList();
        Set<Long> advancePepcTeamId = needAward.stream().map(PepcTeamSchedule::getTeamId).collect(Collectors.toSet());
        List<Long> elePcpcScheduleId = elePepcTeamSchedule.stream().map(PepcTeamSchedule::getId).toList();


        myself.saveSettlePhaseData(actId, pepcPhaseInfo, advancePepcScheduleId, elePcpcScheduleId, null, null);
    }

    public void settleGameResult(PepcPhaseComponentAttr attr, PepcGameEndEvent event) {
        PepcGame pepcGame = pepcDao.getPepcGameById(event.getActId(), event.getGameId());
        if (pepcGame == null) {
            log.error("settleGameResult pepcGame is null,actId:{},gameId:{}", event.getActId(), event.getGameId());
            return;
        }
        if (PepcConst.GameState.CLOSE == event.getState()) {
            List<PepcGameMember> members = pepcDao.getPepcGameMemberByGameId(event.getActId(), event.getGameId());
            List<PepcGameMember> hasResultMember = members.stream().filter(p -> p.getState().equals(PepcConst.GameMemberState.RESULTED)).toList();
            if (CollectionUtils.isEmpty(hasResultMember)) {
                log.error("settleGameResult hasResultMember empty:{}", JSON.toJSONString(event));
                return;
            }

            Map<Long, List<PepcGameMember>> teamMemberMap = hasResultMember.stream().collect(Collectors.groupingBy(PepcGameMember::getTeamId));
            Set<Long> settleTeamId = Sets.newHashSet();
            for (Long teamId : teamMemberMap.keySet()) {
                List<PepcGameMember> teamMember = teamMemberMap.get(teamId);
                if (CollectionUtils.isEmpty(teamMember)) {
                    continue;
                }
                int teamRank = teamMember.get(0).getRank();
                int teamScore = attr.getTeamRankScore().getOrDefault(teamRank, 0);

                Optional<Integer> totalElimination = teamMember.stream().map(p -> Convert.toInt(p.getElimination(), 0)).reduce(Integer::sum);
                int oriScore = teamScore + (totalElimination.orElse(0));
                long totalScore = oriScore * attr.getScoreExtraDigits() + totalElimination.orElse(0);

                String sourceSeq = "addScore:" + pepcGame.getId() + "_" + teamId;
                String md5Seq = MD5SHAUtil.getMD5(sourceSeq);
                log.info("saveGameTeamScore,actId:{},gameId:{},teamId:{},soureSeq:{},targetSeq:{}", pepcGame.getActId(), pepcGame.getId(), teamId, sourceSeq, md5Seq);
                myself.saveGameTeamScore("join:" + md5Seq, attr, pepcGame, Lists.newArrayList(teamId), totalScore, 1);

                settleTeamId.add(teamId);
            }

            //没参加游戏的队伍
            List<Long> notJoinGameTeamId = members.stream().map(PepcGameMember::getTeamId).filter(p -> !settleTeamId.contains(p)).toList();
            if (CollectionUtils.isNotEmpty(notJoinGameTeamId)) {
                myself.saveGameTeamScore("notJoin:" + pepcGame.getId(), attr, pepcGame, notJoinGameTeamId, 0L, 0);
            }


        }
        //TODO 看用户如果没有进入h5,最终是不是这个状态
        else if (PepcConst.GameState.NO_RESULT == event.getState() || PepcConst.GameState.CANCEL_DESTROYED == event.getState()) {
            List<PepcGameMember> members = pepcDao.getPepcGameMemberByGameId(event.getActId(), event.getGameId());
            List<Long> notJoinGameTeamId = members.stream().map(PepcGameMember::getTeamId).toList();
            if (CollectionUtils.isNotEmpty(notJoinGameTeamId)) {
                myself.saveGameTeamScore("notJoin:" + pepcGame.getId(), attr, pepcGame, notJoinGameTeamId, 0L, 0);
            }
        } else {
            log.error("settleGameResult game status error,actId:{},gameId:{},state:{}", event.getActId(), event.getGameId(), event.getState());
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void saveGameTeamScore(String seq, PepcPhaseComponentAttr attr, PepcGame pepcGame, List<Long> teamIds, long totalScore, int addGameCount) {
        log.info("begin saveGameTeamScore,gameId:{},teamsId:{},score:{},gameCount:{}", pepcGame.getId(), JSON.toJSONString(teamIds), totalScore, addGameCount);
        boolean firstSet = commonDataDao
                .hashValueSetNX(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), "saveGtsSeq", seq, DateUtil.format(new Date()));
        if (!firstSet) {
            log.warn("saveGameTeamScore failed,game already saveGameTeamScore,actId:{},gameId:{}", attr.getActId(), pepcGame.getId());
            return;
        }
        long teamRes = pepcGameTeamMapper.updateGameTeamScore(pepcGame.getActId(), pepcGame.getId(), teamIds, totalScore);
        long schRes = pepcTeamScheduleMapper.addGameScore(pepcGame.getActId(), pepcGame.getGroupCode(), pepcGame.getPhaseId(), teamIds, totalScore, addGameCount);
        log.info("begin saveGameTeamScore,gameId:{},teamsId:{},score:{},gameCount:{},teamRes:{},schRes:{}"
                , pepcGame.getId(), JSON.toJSONString(teamIds), totalScore, addGameCount, teamRes, schRes);

    }


    public @NotNull PepcActInfoVo getPepcActInfoVo(Long actId, Date now, PepcPhaseComponentAttr attr, long uid) {
        PepcActInfoVo actInfoVo = new PepcActInfoVo();
        actInfoVo.setServerTime(now.getTime());
        actInfoVo.setSignStartTime(attr.getSignupStartTime().getTime());
        actInfoVo.setSignEndTime(attr.getSignupEndTime().getTime());
        actInfoVo.setAwards(attr.getAwards());
        actInfoVo.setNextActTime(attr.getNextActTime());

        actInfoVo.setShowAppointment(now.after(attr.getSignupEndTime()));
        actInfoVo.setAppointment(pepcDao.countPepcSubscribe(actId, uid) > 0);
        actInfoVo.setSign(pepcDao.countPepcTeamMember(actId, uid) > 0);
        actInfoVo.setPreActId(attr.getPreActId());

        int actState = pepcTeamGroupService.getActState(attr);
        actInfoVo.setState(actState);

        //控制分组完成后，不再显示上期活动的直播回放数据
        if(actState == PepcConst.ActState.GROUP_COMPLETED){
            actInfoVo.setPreActId(0);
        }

        var phaseInfos = pepcDao.getAllPepcPhaseInfo(actId);
        Optional<PepcPhaseInfo> pepcPhaseInfo = phaseInfos.stream().filter(p -> now.after(p.getStartTime()) && now.before(p.getEndTime())).findFirst();
        int curPhaseId = pepcPhaseInfo.isEmpty() ? -1 : pepcPhaseInfo.get().getPhaseId();
        actInfoVo.setCurPhaseId(curPhaseId);

        PepcTeamMember teamMember = pepcDao.getPepcTeamMember(actId, uid);
        if (teamMember != null) {
            var schedule = pepcDao.getPepcTeamSchedule(actId, curPhaseId, teamMember.getTeamId());
            actInfoVo.setCurGroupCode(schedule == null ? "" : schedule.getGroupCode());
        }

        actInfoVo.setRankActId(pepcRankService.getRankActId(attr));

        List<PepcPhaseInfoVo> phaseInfoVoList = Lists.newArrayList();
        for (PepcPhaseInfo item : phaseInfos) {
            PepcPhaseInfoVo vo = new PepcPhaseInfoVo();
            vo.setName(item.getPhaseName());
            vo.setPhaseId(item.getPhaseId());
            vo.setStartTime(item.getStartTime().getTime());
            vo.setEndTime(item.getEndTime().getTime());
            phaseInfoVoList.add(vo);
        }
        actInfoVo.setContests(phaseInfoVoList);
        return actInfoVo;
    }

    public PepcMyGameRoundVo getPepcMyGameRoundVo(PepcPhaseComponentAttr attr, Date now, long actId, long uid) {
        PepcMyGameRoundVo vo = new PepcMyGameRoundVo();

        List<PepcGameMember> gameMembers = pepcDao.getPepcGameMember(actId, uid);
        if (CollectionUtils.isEmpty(gameMembers)) {
            return vo;
        }

        List<PepcMyGameRoundItemVo> roundRes = Lists.newArrayList();

        List<Long> gameIds = gameMembers.stream().map(PepcGameMember::getGameId).toList();
        List<Long> teamIds = gameMembers.stream().map(PepcGameMember::getTeamId).toList();
        List<PepcGame> pepcGames = pepcGameMapper.selectPepcGameByGameIds(actId, gameIds);
        List<PepcGameTeam> pepcGameTeams = pepcGameTeamMapper.selectPepcGameTeamByGameId(actId, gameIds);
        Map<Long, List<PepcGameTeam>> gameTeamMap = pepcGameTeams.stream()
                .collect(Collectors.groupingBy(PepcGameTeam::getGameId));


        List<Long> uids = pepcGameTeams.stream().map(PepcGameTeam::getCaptainUid).distinct().toList();
        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(uids, Template.unknown);

        for (PepcGame game : pepcGames) {
            PepcMyGameRoundItemVo itemVo = new PepcMyGameRoundItemVo();
            itemVo.setGameId(game.getId());
            itemVo.setGroupCode(game.getGroupCode());
            itemVo.setPhaseId(game.getPhaseId());
            Optional<PepcPhaseComponentAttr.PepcPhaseConfig> phaseConfig = attr.getPhaseConfig()
                    .stream()
                    .filter(p -> p.getPhaseId().equals(game.getPhaseId()))
                    .findFirst();
            phaseConfig.ifPresent(pepcPhaseConfig -> itemVo.setPhaseName(pepcPhaseConfig.getPhaseName()));
            itemVo.setRound(game.getRound());
            itemVo.setStartTime(game.getStartTime().getTime());
            itemVo.setModuleId(game.getModuleId());

            itemVo.setState(calMyGameState(now, attr, game));

            List<PepcGameTeam> teams = gameTeamMap.get(game.getId());
            for (PepcGameTeam team : teams) {
                var userInfoVo = userInfoVoMap.get(team.getCaptainUid());
                if (userInfoVo != null) {
                    itemVo.addHeader(userInfoVo.getAvatarUrl());
                }
            }

            roundRes.add(itemVo);
        }


        vo.setRound(roundRes);
        return vo;
    }

    public int calMyGameState(Date now, PepcPhaseComponentAttr attr, PepcGame game) {
        int state = PepcConst.MyGameState.NOT_STARTED;
        if (PepcConst.GameState.FINAL_STATE.contains(game.getState())) {
            return PepcConst.MyGameState.CLOSE;
        }
        Date soonToStartBegin = DateUtil.addMinutes(game.getStartTime(), attr.getShowMyGameStateSoonToStartBegin());
        Date playingBegin = DateUtil.addMinutes(game.getStartTime(), attr.getShowMyGameStatePlayingBegin());
        Date settleBegin = DateUtil.addMinutes(game.getStartTime(), attr.getShowMyGameStateSettleBegin());

        if (now.after(soonToStartBegin) && now.before(playingBegin)) {
            state = PepcConst.MyGameState.SOON_TO_START;
        } else if (now.after(playingBegin) && now.before(settleBegin)) {
            state = PepcConst.MyGameState.PLAY_ING;
        } else if (now.after(settleBegin)) {
            state = PepcConst.MyGameState.SETTLE;
        }

        return state;

    }


}
