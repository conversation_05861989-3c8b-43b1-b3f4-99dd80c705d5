package com.yy.gameecology.activity.config.svcsdk;

import com.yy.gameecology.activity.config.proto.ProtoCodec;
import com.yy.gameecology.activity.config.proto.ProtoHandlerMapping;
import com.yy.gameecology.activity.config.proto.ProtoHandlerWrapper;
import com.yy.gameecology.activity.service.SvcSDKService;
import com.yy.gameecology.common.mymsg.Header;
import com.yy.gameecology.common.utils.AESUtil;
import com.yy.jserverlib.codec.Messager;
import com.yy.protocol.pb.GameecologyActivity.GameEcologyMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.nio.ByteBuffer;

/**
 * <AUTHOR> 2020/7/22
 */
public class MessageInvoker {

    private static final Logger log = LoggerFactory.getLogger(MessageInvoker.class);

    private static final int MAX_MASK = 0xff;

    @Lazy
    @Autowired
    private SvcSDKService svcSDKService;

    private ProtoHandlerMapping protoHandlerMapping;

    private ProtoCodec protoCodec;

    public MessageInvoker(ProtoHandlerMapping protoHandlerMapping, ProtoCodec protoCodec) {
        this.protoHandlerMapping = protoHandlerMapping;
        this.protoCodec = protoCodec;
    }

    /**
     * 解码, 找到处理器, 处理, 不抛异常
     *
     * @param message
     */
    public void invoke(SvcSDKMessage message) {

        try {
            //消息协议为yyp
            ByteBuffer buffer = ByteBuffer.wrap(message.getData());
            if (buffer.remaining() < Header.HEADER_LENGTH) {
                //消息不完整
                log.warn("SvcSDKMessage header is incomplete! uid:{} sid:{} data:{}", message.getUid(), message.getSid(), AESUtil.byte2hex(message.getData()));
                return;
            }

            Header header = Messager.getMessager().read(buffer, Header.class);
            if (buffer.remaining() < header.getSize() - Header.HEADER_LENGTH) {
                log.warn("SvcSDKMessage body is incomplete! uid:{} sid:{} data:{}", message.getUid(), message.getSid(), AESUtil.byte2hex(message.getData()));
                return;
            }
            if (header.getCode() != Header.DEFAULT_RES_CODE) {
                log.warn("SvcSDKMessage code is incorrect! uid:{} sid:{} header:{}", message.getUid(), message.getSid(), header);
                return;
            }

            long max_uri = header.getUri() & MAX_MASK;
            long min_uri = header.getUri() >> 8;

            log.info("SvcSDKMessage uid:{} sid:{} uri:{}({})", message.getUid(), message.getSid(), min_uri, max_uri);

//            if (SvcSDKService.PB_MAX_TYPE == max_uri) {
                //序列化方式为pb
                ProtoHandlerWrapper protoHandler = protoHandlerMapping.findHandler((int) min_uri);
                if (protoHandler == null) {
                    log.warn("SvcSDKMessage protoHandler not found! uid:{} sid:{} uri:{}", message.getUid(), message.getSid(), min_uri);
                    return;
                }
                GameEcologyMsg req = protoCodec.decode(buffer);
                Object result = protoHandler.invoke(message, req);
                if (result instanceof GameEcologyMsg) {
                    GameEcologyMsg resp = ((GameEcologyMsg) result).toBuilder()
                            .setSeq(req.getSeq()).setVersion(req.getVersion()).build();
                svcSDKService.unicastUid(message.getUid(), resp);
                }

//            } else {
//                log.warn("SvcSDKMessage max_uri:{} is not implemented!", max_uri);
//            }

        } catch (Exception e) {
            log.error("SvcSDKMessage invoke uid:{} sid:{} data:{} err:{}", message.getUid(), message.getSid(),
                    AESUtil.byte2hex(message.getData()), e.getMessage(), e);
        }
    }

}
