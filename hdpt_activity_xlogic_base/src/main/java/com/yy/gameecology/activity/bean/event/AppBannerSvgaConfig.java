package com.yy.gameecology.activity.bean.event;

import com.alibaba.fastjson.JSONObject;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

import java.util.List;

/**
 * desc:TODO 待废弃，组件属性不应该放这里
 *
 * <AUTHOR>
 * @date 2023-01-05 20:31
 * 配置文档参考: http://jywiki.yy.com/docs/share/cross_event_push#akyqn0
 **/
@Deprecated
@Data
public class AppBannerSvgaConfig {
    /**
     * 1 -支持跳转 0 -不支持跳转 注：svga需要有跳转按钮
     */
    @ComponentAttrField(labelText = "是否支持跳转", remark = "1 -支持跳转 0 -不支持跳转")
    private int jump;
    /**
     * 高度
     */
    @ComponentAttrField(labelText = "高度")
    private int height;
    /**
     * 播放时间 单位：秒
     */
    @ComponentAttrField(labelText = "播放时间 单位：秒", remark = "播放总时长,若需要循环,填写循环n次的总时长")
    private int duration;
    /**
     *  循环播放次数, 0-无限循环(勿填0)
     */
    @ComponentAttrField(labelText = "循环播放次数", remark = "循环播放次数")
    private int loops;
    /**
     * svga文案区域key name, 已弃用,后续使用 contentLayers
     */
    @Deprecated
    @ComponentAttrField(labelText = "svga文案区域key name", remark = "已弃用,后续使用 contentLayers")
    private String contentKeyName;
    /**
     * svga点击按钮key name
     */
    @ComponentAttrField(labelText = "svga点击按钮key name")
    private String clickLayerName;
    /**
     * 横幅svga url
     */
    @ComponentAttrField(labelText = "不带围观按钮的横幅svga", remark = "不带围观按钮的svga")
    private String svgaURL;

    /**
     * 横幅svga url
     */
    @ComponentAttrField(labelText = "带围观按钮的横幅svga", remark = "带围观按钮的svga")
    private String jumpSvgaURL ;

    /**
     * 礼物滚屏url
     */
    @ComponentAttrField(labelText = "不带围观的礼物滚屏svga", remark = "当前端限制横幅时,前端将使用这个svga播放")
    private String miniURL;

    /**
     * 横幅svga url
     */
    @ComponentAttrField(labelText = "带围观按钮的礼物滚屏svga", remark = "带围观按钮的礼物滚屏svga,前端将使用这个svga播放")
    private String jumpMiniURL ;

    /**
     * 横幅文案样式
     * "contentLayers": [ // 替换所有key的文本
     * <p>
     * {"layerKey1": // svga key1
     * <p>
     * {
     * <p>
     * "text": "xxx送xxx", // 富文本消息，html格式 {uid:n} ：表示需要nick替换 {uid:an}：表示需要头像icon+nick {img}：表示需要使用imgs list按顺序取出替换
     * <p>
     * "imgs": ["wwww.sss.png","sssss.cccc.png"] // 客户端替换text文本中存在的{img}
     * <p>
     * }
     * <p>
     * }
     * <p>
     * ],
     */
    @ComponentAttrField(labelText = "横幅文案样式", remark = "示例: [{\"layerKey1\":{\"text\":\"xxx送xxx\",\"imgs\":[\"wwww.sss.png\",\"sssss.cccc.png\"]}}]," +
            "layerKey1-> svga key1,text-> 富文本消息，html格式 {uid:n} ：表示需要nick替换 {uid:an}：表示需要头像icon+nick {img}：表示需要使用imgs list按顺序取出替换" +
            "imgs -> 客户端替换text文本中存在的{img}")
    private String zjContentLayers;

    @ComponentAttrField(labelText = "图片key", remark = "示例: [{\"svgaImgKey\":\"##imgValue1##\"},{\"svgaImgKey2\":\"##imgValue2##\"}] 替换所有imgkey的文本")
    private String zjImgLayers;

    private List<JSONObject> contentLayers;

    private List<JSONObject> imgLayers;


    @ComponentAttrField(labelText = "昵称最长长度", remark = "默认5个")
    private int nameCountLimit = 5;


}
