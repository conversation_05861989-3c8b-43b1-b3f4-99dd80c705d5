package com.yy.gameecology.hdzj;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.RankingTimeEnd;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.model.gameecology.*;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.element.component.CpDiceLotteryComponent;
import com.yy.gameecology.hdzj.element.component.attr.CpDiceLotteryComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.CpDiceLottryDao;
import com.yy.gameecology.hdzj.element.component.dao.CpDiceLottryMockDao;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileReader;
import java.io.BufferedReader;
import java.io.IOException;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties", "classpath:env/local/application-inner.properties"})
public class CpDiceLotterytTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "3");
    }
    final long ACT_ID = 2025033003;

    @Autowired
    CpDiceLotteryComponent cpDiceLotteryComponent;

    @Autowired
    CpDiceLottryDao cpDiceLottryDao;

    @Autowired
    CpDiceLottryMockDao cpDiceLottryMockDao;

    @Test
    public void onRankingTimeEndTest() {
        RankingTimeEnd event = new RankingTimeEnd();
        event.setEndTime("2025-05-13 01:59:59");
        event.setRankId(18);
        event.setActId(ACT_ID);
        event.setSeq("20250221899");
//        var attr = cpDiceLotteryComponent.getUniqueComponentAttr(ACT_ID);
        var attr = new CpDiceLotteryComponentAttr();
        attr.setActId(ACT_ID);
        attr.setCmptUseInx(800);
        fillWithRestrictGift(attr);
        long uid = 2849216469L;
        long uid2 = 2849402288L;
        long count = 1;
        String seq = "20000526029";
        cpDiceLotteryComponent.updateUserLotteryState(attr,uid, uid2, count, seq);
    }

    private void fillWithRestrictGift(CpDiceLotteryComponentAttr attr){
        for (int i = 1; i < 5; i++) {
            CpDiceLotteryComponentAttr.RestrictGiftInfo r = new CpDiceLotteryComponentAttr.RestrictGiftInfo();
            r.setRewardTaskId(Convert.toLong(i));
            r.setAwardName("礼物:"+i);
            if (i == 1) {
                r.setNeedTicket(16);
                r.setUpLimit(150);
            }else if (i == 2) {
                r.setNeedTicket(40);
                r.setUpLimit(70);
            }else if (i == 3) {
                r.setNeedTicket(400);
                r.setUpLimit(10);
            }else  if (i == 4) {
                r.setNeedTicket(2000);
                r.setUpLimit(2);
            }
            attr.getStockReward().put(Convert.toLong(i),r);
        }

    }

    @Test
    public void drawTest() {
//        RankingTimeEnd event = new RankingTimeEnd();
//        event.setEndTime("2025-05-13 01:59:59");
//        event.setRankId(18);
//        event.setActId(ACT_ID);
//        event.setSeq("20250221899");
       var attr = cpDiceLotteryComponent.getUniqueComponentAttr(ACT_ID);
//        var attr = new CpDiceLotteryComponentAttr();
//        attr.setActId(ACT_ID);
//        attr.setCmptUseInx(800);
//        fillWithRestrictGift(attr);
//        long uid = 2849216469L;
//        long uid2 = 2849402288L;
//        long count = 1;
//        String seq = "20000526029";
       long uid = 1080293145L;
       long uid2 = 50013181L;

        var ticket = cpDiceLottryDao.getTicket(attr.getActId(), attr.getCmptUseInx(), uid, uid2);
        CpDiceLotteryInfo drawInfo = cpDiceLottryDao.getDrawInfo(attr.getActId(), attr.getCmptUseInx(), uid, uid2);
        Map<Long,Long> restrictInfo = cpDiceLotteryComponent.getRestrictBalanceCount(attr);
        long count = 30;
        var rspl = cpDiceLotteryComponent.continuousDraw(attr,drawInfo, count,ticket.getBalance());


        if (rspl == null) {
            log.info("get error return 400");
            return;
        }
        log.info("{}",rspl.toString());
        CpDiceLotteryInfo afterDrawInfo = cpDiceLottryDao.getDrawInfo(attr.getActId(), attr.getCmptUseInx(), uid, uid2);
        long start = drawInfo.getStep();
        if (start == 0) {
            start = 1;
        }
        long value = drawInfo.getRewardValue();
        Map<Long,Long> deductMap = new HashMap<>();
        deductMap.put(901L,0L);
        deductMap.put(902L,0L);
        deductMap.put(903L,0L);
        deductMap.put(904L,0L);
        var rsp =  rspl.getRewardList();
        for (int i = 0; i < rsp.size(); i++) {
            var r = rsp.get(i);
            if (r.getDicePoint() > 6) {
                log.info("err dice point over {}",r.toString());
            }
            if (((start+r.getDicePoint()-1)%30 +1)!= r.getStep()) {
                log.info("err step point over {}",r.toString());
            }else {
                start = r.getStep();
            }
            if (!r.getRewardInfo().getAwardName().equals(attr.getCpTaskPackageReward().get(r.getStep()).getAwardName())) {
                log.info("err reward config {} {}",r.toString(),attr.getCpTaskPackageReward().get(r.getStep()));
            }
            if (r.getRewardInfo().getTAwardPkgId() >= 901 && r.getRewardInfo().getTAwardPkgId() <= 904) {
                deductMap.computeIfPresent(r.getRewardInfo().getTAwardPkgId(), (k, v) -> v + 1);
            }
            value += r.getRewardInfo().getAwardAmount()*2;
        }
        if (value != afterDrawInfo.getRewardValue()) {
            log.info("err reward value point over {}",afterDrawInfo.toString());
        }
        Map<Long,Long> afterRestrictInfo = cpDiceLotteryComponent.getRestrictBalanceCount(attr);
        for (int i = 901; i <=904 ; i++) {
            log.info(" i:{} {} {} {}",i,restrictInfo.get(Convert.toLong(i)),afterRestrictInfo.get(Convert.toLong(i)), deductMap.get(Convert.toLong(i)));
            if (!afterRestrictInfo.get(Convert.toLong(i)).equals(restrictInfo.get(Convert.toLong(i)) - deductMap.get(Convert.toLong(i))) || afterRestrictInfo.get(Convert.toLong(i)) < 0) {
                log.info("err duct info   {}  {}  {} {}",i,restrictInfo.get(Convert.toLong(i)),afterRestrictInfo.get(Convert.toLong(i)), deductMap.get(Convert.toLong(i)));
            }
        }
        log.info("before {} {}", drawInfo,restrictInfo);
        log.info("after {} {}", afterDrawInfo,afterRestrictInfo);

    }

    @Test
    public void otherTest() {
//        RankingTimeEnd event = new RankingTimeEnd();
//        event.setEndTime("2025-05-13 01:59:59");
//        event.setRankId(18);
//        event.setActId(ACT_ID);
//        event.setSeq("20250221899");
        var attr = cpDiceLotteryComponent.getUniqueComponentAttr(ACT_ID);
//        var attr = new CpDiceLotteryComponentAttr();
//        attr.setActId(ACT_ID);
//        attr.setCmptUseInx(800);
//        fillWithRestrictGift(attr);
//        long uid = 2849216469L;
//        long uid2 = 2849402288L;
//        long count = 1;
//        String seq = "20000526029";
//        long uid = 50013181;
//        long uid2 = 2849402288L;
//
//       var rsp = cpDiceLotteryComponent.getCpList(null,null,ACT_ID,810);
////        var rsp = cpDiceLotteryComponent.getMapInfo(ACT_ID,810);
////        var rsp = cpDiceLotteryComponent.info(null,null, ACT_ID,810,uid,uid2);
//        log.info("{}",rsp.toString());

//        List<csvInfo> csvInfoList =  readDsv();

        long startId = 0 ;
        long pageSize = 20 ;
        long times = 0;
        List<Cmpt5155MockData> cmpt5155MockDataList = cpDiceLottryMockDao.selectNockDatas(startId,pageSize) ;
        while (CollectionUtils.isNotEmpty(cmpt5155MockDataList)) {
            startId = cmpt5155MockDataList.getLast().getIdx();
            for (int i = 0; i < cmpt5155MockDataList.size(); i++) {
                var csvInfo = cmpt5155MockDataList.get(i);
                long count = csvInfo.getAmount() /131400;
                if (count == 0 ){
                    continue;
                }
                String seq = String.format("m%d_%d",csvInfo.getUid(),csvInfo.getIdx());
                long ticket = cpDiceLotteryComponent.updateUserLotteryState(attr,csvInfo.getUid(), csvInfo.getAnchorId(), count, seq);
                Cmpt5155MockTicketResult result = new Cmpt5155MockTicketResult();
                result.setSeq(seq);
                result.setUid(csvInfo.getUid());
                result.setAnchorUid(csvInfo.getAnchorId());
                result.setAmount(csvInfo.getAmount());
                result.setTicketOut(Convert.toInt(ticket));
                result.setDatestr(csvInfo.getDatestr());
                cpDiceLottryMockDao.addTicketRecord(result);
                while (true) {
                    CpDiceLotteryTicket userTicket =  cpDiceLottryDao.getTicket(attr.getActId(),attr.getCmptUseInx(),  csvInfo.getUid(), csvInfo.getAnchorId());
                    if (userTicket.getBalance() == 0) {
                        break;
                    }
                    CpDiceLotteryInfo drawInfo = cpDiceLottryDao.getDrawInfo(attr.getActId(), attr.getCmptUseInx(), csvInfo.getUid(), csvInfo.getAnchorId());
                    if (drawInfo.getStep() == 0 ){
                        cpDiceLottryDao.userClick(ACT_ID, 810, csvInfo.getUid(), csvInfo.getAnchorId());
                    }
                    long draw = Math.min(userTicket.getBalance(),attr.getMaxConsume());
                    var ret = cpDiceLotteryComponent.continuousDraw(attr, drawInfo, draw, userTicket.getBalance());
                    var retList = ret.getRewardList();
                    List<Cmpt5155MockResult> l = new ArrayList<>(retList.size());
                    for (int j = 0; j < retList.size(); j++) {
                        Cmpt5155MockResult r =  new Cmpt5155MockResult();
                        r.setUid(csvInfo.getUid());
                        r.setAnchorUid(csvInfo.getAnchorId());
                        r.setGroupKey(seq);
                        r.setStep(Convert.toInt(retList.get(j).getStep()));
                        r.setRewardId(Convert.toInt(retList.get(j).getRewardInfo().getTAwardPkgId()));
                        r.setRewardName(retList.get(j).getRewardInfo().getAwardName());
                        r.setDatestr(csvInfo.getDatestr());
                        r.setRewardAmount(retList.get(j).getRewardInfo().getAwardAmount()*2);
                        l.add(r);
                    }
                    cpDiceLottryMockDao.addLotteryRecord(l);
                }
            }
            if (times >=  5 ) {
                break;
            }
            times = times + 1;
            cmpt5155MockDataList = cpDiceLottryMockDao.selectNockDatas(startId,pageSize) ;
        }
    }


    @Test
    public void noticeTest() {
//        var attr = cpDiceLotteryComponent.getUniqueComponentAttr(ACT_ID);
//        cpDiceLotteryComponent.doSyncComponentInfo(attr,"2025061801");
       var c =  cpDiceLotteryComponent.getMapInfo(ACT_ID,810);
        log.info("rsp1 {}",c);

    }

    @Test
    public void drawHttpTest() {
        var attr = cpDiceLotteryComponent.getUniqueComponentAttr(ACT_ID);
        Const.EXECUTOR_DELAY_GENERAL.schedule(() -> {
            var rsp1 =  cpDiceLotteryComponent.draw(null,null, ACT_ID, 810,1023722532,50013181);
            log.info("rsp1 {}",rsp1);
        },1, TimeUnit.MICROSECONDS);
        var rsp2 =  cpDiceLotteryComponent.draw(null,null, ACT_ID, 810,1023722532,50013181);
        log.info("rsp2 {}",rsp2);
        var rsp3 =  cpDiceLotteryComponent.draw(null,null, ACT_ID, 810,1023722532,50013181);
        log.info("rsp3 {}",rsp3);
        xSleep(5);
    }

    protected void xSleep(int seconds) {
        try {
            Thread.sleep(seconds * 1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

//    private List<csvInfo> readDsv() {
//        String csvFile = "D:\\datacsv\\songli.csv";
//        String line;
//        String csvSplitBy = ","; // 分隔符
//
//        try (BufferedReader br = new BufferedReader(new FileReader(csvFile))) {
//            // 读取表头（如果有）
//            if ((line = br.readLine()) != null) {
//                System.out.println("表头: " + line);
//            }
//
//            // 读取数据行
//            while ((line = br.readLine()) != null) {
//                String[] data = line.split(csvSplitBy);
//                log.info("uid: {}, uid2: {}, amount: {}",
//                        data[0], data[1], data[2]);
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }

    @Data
    public class csvInfo{
        private long uid;
        private long anchorUid;
        private long amount;

        public csvInfo(long uid, long anchorUid,long count) {
            this.uid = uid;
            this.anchorUid = anchorUid;
            this.amount = count;
        }
    }





}
