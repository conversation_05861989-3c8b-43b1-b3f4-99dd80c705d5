package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.CacheService;
import com.yy.gameecology.common.db.model.gameecology.HdzjActivity;
import com.yy.gameecology.common.db.model.gameecology.HdzjComponent;
import com.yy.gameecology.common.db.model.gameecology.HdzjComponentAttr;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import org.apache.commons.lang3.RandomUtils;
import org.assertj.core.util.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestComponent;
import org.springframework.dao.DuplicateKeyException;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/7/7
 */
@TestComponent
public class ComponentTestHelper extends BaseActComponent<ComponentAttr> {

    @Autowired
    private GameecologyDao gameecologyDao;

    @Autowired
    CacheService cacheService;

    @Autowired
    private ActRedisGroupDao redis;

    @Override
    public Long getComponentId() {
        return -9999L;
    }

    public HdzjComponent init() {
        return init(RandomUtils.nextInt(), RandomUtils.nextInt(), RandomUtils.nextInt());
    }

    public HdzjComponent init(long cpId) {
        return init(RandomUtils.nextInt(), cpId, RandomUtils.nextInt());
    }

    public HdzjComponent init(long activityId, long componentId, long index) {
        initActivity(activityId);

        return initComponent(activityId, componentId, index);
    }

    public void forceReloadActivityAndCmp(){
        cacheService.forceReloadActivityAndCmp();
    }

    public HdzjActivity initActivity(long activityId) {
        HdzjActivity activity = new HdzjActivity();
        activity.setActId(activityId);
        activity.setRemark("");
        activity.setTitle("");
        activity.setCtime(new Date());
        activity.setUtime(new Date());
        try {
            gameecologyDao.insert(HdzjActivity.class, activity);
        } catch (DuplicateKeyException e) {

        }
        return activity;
    }

    public HdzjComponent initComponent(long activityId, long componentId, long index) {
        HdzjComponent hc = new HdzjComponent();
        hc.setCmptUseInx(index);
        hc.setActId(activityId);
        hc.setCmptId(componentId);
        hc.setUtime(new Date());
        hc.setRemark("");
        hc.setExtjson("");
        hc.setStatus(1L);
        hc.setCtime(new Date());
        hc.setShowOrder(0L);
        hc.setCmptTitle("");
        gameecologyDao.insert(HdzjComponent.class, hc);
        return hc;
    }

    public void addComponentAttr(HdzjComponent hc, String name, String value) {
        addComponentAttr(hc.getActId(), hc.getCmptId(), hc.getCmptUseInx(), Maps.newHashMap(name, value));
    }

    public void addComponentAttr(HdzjComponent hc, Map<String, String> map) {
        addComponentAttr(hc.getActId(), hc.getCmptId(), hc.getCmptUseInx(), map);
    }

    public void addComponentAttr(long activityId, long componentId, long index, Map<String, String> map) {
        List<HdzjComponentAttr> collect = map.entrySet().stream()
                .map(e -> {
                    HdzjComponentAttr attr = new HdzjComponentAttr();
                    attr.setName(e.getKey());
                    attr.setCmptId(componentId);
                    attr.setCmptUseInx(index);
                    attr.setActId(activityId);
                    attr.setRemark("");
                    attr.setCtime(new Date());
                    attr.setValue(e.getValue());
                    attr.setUtime(new Date());
                    return attr;
                }).collect(Collectors.toList());

        gameecologyDao.batchInsert(HdzjComponentAttr.class, collect);
    }

    public void cleanCpRedis(ComponentAttr attr) {
        redis.delKeys(RedisConfigManager.OLD_ACT_GROUP_CODE,redis.keys(RedisConfigManager.OLD_ACT_GROUP_CODE, makeKey(attr, "*")));
    }

}
