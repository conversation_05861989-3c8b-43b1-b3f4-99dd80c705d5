package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.CacheService;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @since 2021/7/7
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class,webEnvironment = WebEnvironment.RANDOM_PORT,properties = {"group=2"})
@TestPropertySource(value = {"classpath:env/local/application.properties","classpath:env/local/group-setting-2.properties"})
public class BaseTest {

    @Autowired
    protected ComponentTestHelper helper;
    @Autowired
    protected ActRedisGroupDao redis;

    @Autowired
    protected CacheService cacheService;

    protected static final Long activityId = 2021083001L;

}
