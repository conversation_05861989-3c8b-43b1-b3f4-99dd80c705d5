package com.yy.gameecology.hdzj;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.hdzj.element.component.AnchorTeamComponent;
import com.yy.gameecology.hdzj.element.component.attr.AnchorTeamComponentAttr;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2022/9/6 15:28
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-4.properties"})
public class AnchorTeamComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "1");
    }

    @Autowired
    private AnchorTeamComponent anchorTeamComponent;

    @Test
    public void onRankingScoreChangedTest() {
        long actId = 2022094001L;
        long componentUseIndex = 400;
        AnchorTeamComponentAttr attr = anchorTeamComponent.getComponentAttr(actId, componentUseIndex);

        String eventJson = "{\"actId\":2022094001,\"actors\":{40001:\"1508086586\",40050:\"1450596749_2782949806\",40003:\"1507544887\",40004:\"1450596749\"},\"busiId\":400,\"ekey\":\"\",\"index\":0,\"itemCount\":10,\"itemId\":\"BB_LUCK_JF\",\"itemScore\":1000,\"member\":\"1508086586\",\"occurTime\":\"2022-10-14 17:15:00\",\"phaseId\":31,\"phaseScore\":6000,\"rankId\":32,\"rankScore\":6000,\"seq\":\"f32e98e2-2edb-41e5-9062-44c06819babe\",\"timeKey\":0,\"timestamp\":\"2022-10-14 17:15:00\",\"uri\":2002}";
        RankingScoreChanged event = JSON.parseObject(eventJson, RankingScoreChanged.class);
        event.setMember("2186284557");
        event.setSeq(UUID.randomUUID().toString());

        anchorTeamComponent.onRankingScoreChanged(event, attr);
    }

    @Test
    public void updateLayerTest() {
        long actId = 2022094001L;
        long componentUseIndex = 400;
        AnchorTeamComponentAttr attr = anchorTeamComponent.getComponentAttr(actId, componentUseIndex);

        String json = "{\"actId\":2022094001,\"actors\":{40001:\"2186284557\",4002021:\"2277022801\"},\"busiId\":400,\"ekey\":\"\",\"index\":0,\"itemCount\":10,\"itemId\":\"BB_ZD_ITEM\",\"itemScore\":1000,\"member\":\"2277022801\",\"occurTime\":\"2022-10-19 17:48:00\",\"phaseId\":33,\"phaseScore\":1000,\"rankId\":31,\"rankScore\":1000,\"seq\":\"0566f23f-0a0b-4055-8552-1fcdb49832c3\",\"timeKey\":0,\"timestamp\":\"2022-10-19 17:48:00\",\"uri\":2002}";
        RankingScoreChanged event = JSON.parseObject(json, RankingScoreChanged.class);
        anchorTeamComponent.onRankingScoreChanged(event, attr);
    }

    @Autowired
    private ActLayerInfoService actLayerInfoService;

    @Test
    public void layerTest() {
        long actId = 2022094001L;
        LayerBroadcastInfo info = actLayerInfoService.getLayerInfo(actId, 0L, 0L, "web");
        System.out.println(JSON.toJSONString(info));
    }

    @Test
    public void teamResultBroTest() {
        long actId = 2022094001L;
        long componentUseIndex = 400;
        AnchorTeamComponentAttr attr = anchorTeamComponent.getComponentAttr(actId, componentUseIndex);

        PhaseTimeEnd event = new PhaseTimeEnd();
        event.setRankId(31);
        event.setPhaseId(34);
        anchorTeamComponent.teamResultBro(event, attr);
    }
}
