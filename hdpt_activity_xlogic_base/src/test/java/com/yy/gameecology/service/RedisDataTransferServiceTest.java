package com.yy.gameecology.service;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.service.datatransfer.RedisDataTransferService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-03-23 18:23
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-2.properties"})
public class RedisDataTransferServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group","2");

    }

    @Autowired
    private RedisDataTransferService redisDataTransferService;

    @Test
    public void scanKeyTest(){
        redisDataTransferService.scanActRedisKey(2023021001L,true);
    }

    @Test
    public void scanKeyTest2(){
        redisDataTransferService.scanActRedisKey(2023021001L,true);
    }
}
