# 组件属性编码规范文档

## 概述

本文档基于对 `CPTaskChangeWelfareComponentAttr`、`CpTravelDiaryComponentAttr`、`DragonTreasureComponentAttr` 等组件属性类的分析，总结了组件属性的编码规范，特别是 `@ComponentAttrField` 注解的使用规范，为AI生成代码提供标准化指导。

## 1. 基础结构规范

### 1.1 继承关系
所有组件属性类必须继承 `ComponentAttr` 基类：

```java
public class XxxComponentAttr extends ComponentAttr {
    // 属性定义
}
```

### 1.2 类注解
使用 Lombok 注解简化代码：
- `@Data` - 用于简单的配置类
- `@Getter @Setter` - 用于需要更精细控制的类

```java
@Data
public class CpTravelDiaryComponentAttr extends ComponentAttr {
    // 字段定义
}

@Getter
@Setter  
public class CPTaskChangeWelfareComponentAttr extends ComponentAttr {
    // 字段定义
}
```

## 2. @ComponentAttrField 注解规范

### 2.1 基本属性注解
每个需要在界面展示的字段都必须使用 `@ComponentAttrField` 注解：

```java
@ComponentAttrField(labelText = "业务ID", remark = "交友500 聊天室 810")
private long busiId;

@ComponentAttrField(labelText = "任务榜单id", remark = "多个使用英文逗号隔开")
protected long rankId;
```

### 2.2 注解参数规范

#### 2.2.1 必填参数
- `labelText`: 字段的显示标签，必须提供有意义的中文描述

#### 2.2.2 可选参数
- `remark`: 字段说明信息，提供详细的使用说明
- `placeholder`: 输入框占位符文本
- `defaultValue`: 默认值
- `useDialog`: 是否使用弹窗框（0/1）
- `useTextarea`: 是否使用多行文本框（0/1）

### 2.3 下拉框数据源
使用 `dropDownSourceBeanClass` 指定下拉框数据源：

```java
@ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
protected long busiId;

@ComponentAttrField(labelText = "广播业务类型", dropDownSourceBeanClass = BizSource.class)
private Integer broBusiId;
```

### 2.4 日期字段配置
```java
@ComponentAttrField(
    labelText = "解秘截止时间", 
    remark = "格式： yyyy-MM-dd HH:mm:ss",
    dateShowFormat = "yyyy-MM-dd HH:mm:ss",
    dateValueFormat = "yyyy-MM-dd HH:mm:ss"
)
private String unlockEndTime = "";
```

## 3. 复杂数据类型规范

### 3.1 List 类型配置
使用 `subFields` 配置 List 类型字段：

```java
@ComponentAttrField(
    labelText = "累计任务配置", 
    remark = "多个逗号分隔", 
    subFields = {
        @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = TravelDiaryMissionConfig.class)
    }
)
private List<TravelDiaryMissionConfig> missions;
```

### 3.2 Map 类型配置

#### 3.2.1 简单 Map 配置
```java
@ComponentAttrField(
    labelText = "发奖配置", 
    subFields = {
        @SubField(labelText = "任务等级", fieldName = Constant.KEY1, type = Long.class, remark = "从1开始"),
        @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class)
    }
)
protected Map<Long, AwardAttrConfig> taskAwardAttrMap;
```

#### 3.2.2 复杂嵌套 Map 配置
```java
@ComponentAttrField(
    labelText = "任务配置",
    subFields = {
        @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "任务类型"),
        @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "任务等级"),
        @SubField(fieldName = Constant.VALUE, type = SunshineTaskConfig.class, labelText = "过任务配置")
    }
)
private Map<String, Map<Long, SunshineTaskConfig>> taskConfig;
```

#### 3.2.3 Map<K, List<V>> 类型配置
```java
@ComponentAttrField(
    labelText = "过任务广播配置",
    subFields = {
        @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "任务类型"),
        @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "任务等级"),
        @SubField(fieldName = Constant.MAP_LIST_VALUE, type = BroadcastConfig.class, labelText = "任务等级")
    }
)
private Map<String, Map<Long, List<BroadcastConfig>>> taskBanner;
```

### 3.3 Set 类型配置
```java
@ComponentAttrField(
    labelText = "礼物ID", 
    subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class), 
    remark = "多个使用英文逗号隔开"
)
protected Set<String> giftIds = Collections.emptySet();
```

## 4. SubField 注解规范

### 4.1 基本参数
- `fieldName`: 子字段名称，使用 Constant 类中的常量
- `type`: 子字段类型
- `labelText`: 子字段显示标签
- `remark`: 子字段说明
- `skip`: 是否跳过该字段（默认 false）

### 4.2 常用 fieldName 常量
```java
public class Constant {
    public final static String KEY1 = "key1";
    public final static String KEY2 = "key2"; 
    public final static String KEY3 = "key3";
    public final static String VALUE = "value";
    public final static String MAP_LIST_VALUE = "listValue";
    public final static String LIST_VALUE_TYPE = "valueType";
}
```

## 5. 配置Bean类规范

### 5.1 独立配置类
配置Bean类也需要使用 `@ComponentAttrField` 注解：

```java
@Data
public class TravelDiaryMissionConfig {
    @ComponentAttrField(labelText = "任务等级")
    private int level;

    @ComponentAttrField(labelText = "任务名称")
    private String levelName;

    @ComponentAttrField(labelText = "任务阈值")
    private long score;

    @ComponentAttrField(labelText = "广播类型，累计任务需配置", remark = "2-子频道广播 3-顶级频道下所有子厅广播/家族下面所有子厅 4-全模板")
    private Long broType;
}
```

### 5.2 奖励配置类
```java
@Data
public class AwardAttrConfig {
    @ComponentAttrField(labelText = "奖励奖池Id")
    protected Long tAwardTskId;

    @ComponentAttrField(labelText = "奖励奖包Id", remark = "填0，则是抽奖")
    protected Long tAwardPkgId;

    @ComponentAttrField(labelText = "奖励发放数量")
    protected Integer num;

    @ComponentAttrField(labelText = "奖励名称")
    protected String awardName;

    @ComponentAttrField(labelText = "单位")
    protected String unit;

    @ComponentAttrField(labelText = "奖励图标")
    protected String awardIcon;

    @ComponentAttrField(labelText = "奖励金额")
    protected long awardAmount;
}
```

## 6. 字段命名规范

### 6.1 通用字段命名
- `busiId`: 业务ID
- `rankId`: 榜单ID
- `phaseId`: 阶段ID
- `taskId`: 任务ID
- `packageId`: 奖包ID
- `actorId`: 角色ID

### 6.2 字段类型规范
- ID类字段使用 `long` 类型
- 开关类字段使用 `boolean` 类型
- 数量类字段使用 `long` 或 `int` 类型
- 名称描述类字段使用 `String` 类型
- 时间类字段使用 `Duration` 或 `String` 类型

## 7. 默认值设置规范

### 7.1 基本类型默认值
```java
@ComponentAttrField(labelText = "成员是否为房间", remark = "1:是语音房间，非1：是子频道")
private int roomFlag = 0;

@ComponentAttrField(labelText = "获取webdb信息的templateType", remark = "交友 1 聊天室 810")
private int templateType = 810;
```

### 7.2 集合类型默认值
```java
protected Set<String> giftIds = Collections.emptySet();
private List<String> openTime = Lists.newArrayList();
private Map<Long, Integer> userUniqExchPackageIds = ImmutableMap.of(92035L, 1, 92030L, 3);
```

## 8. 注释规范

### 8.1 字段注释
每个字段都应该有清晰的JavaDoc注释：

```java
/**
 * 成员是否为房间 - 1:是语音房间，非1：是子频道
 */
@ComponentAttrField(labelText = "成员是否为房间", remark = "1:是语音房间，非1：是子频道")
private int roomFlag = 0;
```

### 8.2 类注释
```java
/**
 * CP任务变更福利组件属性
 * 
 * <AUTHOR>
 * @date 2024-xx-xx
 */
@Data
public class CPTaskChangeWelfareComponentAttr extends ComponentAttr {
    // 字段定义
}
```

## 9. 最佳实践

### 9.1 字段分组
相关字段应该在代码中相邻放置，并用注释分组：

```java
// 基础配置
@ComponentAttrField(labelText = "业务ID")
private long busiId;

@ComponentAttrField(labelText = "榜单ID")
private long rankId;

// 奖励配置
@ComponentAttrField(labelText = "发奖奖池ID")
private long rewardTaskId;

@ComponentAttrField(labelText = "发奖奖包ID")
private long rewardPackageId;
```

### 9.2 访问修饰符
- 使用 `private` 作为默认访问修饰符
- 需要被子类访问时使用 `protected`
- 避免使用 `public` 字段

### 9.3 包导入
```java
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import lombok.Data;
```

## 10. 总结

遵循以上规范可以确保：
1. 组件属性定义的一致性和可维护性
2. 界面展示的标准化
3. 配置的可读性和易用性
4. 代码的可扩展性

在AI生成代码时，应严格按照这些规范进行组件属性类的设计和实现。
