PuzzleComponent中用到了UserInfoService方法获取用户信息，请总结这些常用的api，并生成文档到docs目录，用于下次AI生成组件代码，文档需要说明UserInfoService方法的完整包名，以便AI关联引用
-------------------------------------------------------------------------
PuzzleComponent中用到了CommonService类，请总结这些常用的api，并生成文档到docs目录，用于下次AI生成组件代码，文档需要说明CommonService方法的完整包名，以便AI关联引用
-------------------------------------------------------------------------
PuzzleComponent中用到了KafkaService类，请总结这些常用的api，并生成文档到docs目录，用于下次AI生成组件代码，文档需要说明KafkaService方法的完整包名，以便AI关联引用
-------------------------------------------------------------------------
PuzzleComponent中用到了CommonBroadCastService类，请总结这些常用的api，并生成文档到docs目录，用于下次AI生成组件代码，文档需要说明CommonBroadCastService方法的完整包名，以便AI关联引用
-------------------------------------------------------------------------
PuzzleComponent中用到了MemberInfoService类，请总结这些常用的api，并生成文档到docs/api目录，用于下次AI生成组件代码，文档需要说明MemberInfoService方法的完整包名，以便AI关联引用
-------------------------------------------------------------------------
PuzzleComponent中用到了Locker类，请总结这些常用的api，并生成文档到docs/api目录，用于下次AI生成组件代码，文档需要说明Locker方法的完整包名，以便AI关联引用
-------------------------------------------------------------------------
PuzzleComponent中用到了Locker类，请总结这些常用的api，并生成文档到docs/api目录，用于下次AI生成组件代码，文档需要说明Locker方法的完整包名，以便AI关联引用
-------------------------------------------------------------------------
PuzzleComponent 中

@HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
public void onTaskProgressChanged(TaskProgressChanged event, PuzzleComponentAttr attr)

的写法是监听任务变化事件的写写法，除了TaskProgressChanged外，还有继承BaseEvent的时间都可以这样写，请总结一下这些事件的使用方法，并列举项目中的所有事件，生成文档到docs/api目录，以便AI关联引用
-------------------------------------------------------------------------
PuzzleComponent中用到了hdztAwardServiceClient，请总结这些常用的api，并生成文档到docs/api目录，用于下次AI生成组件代码，文档需要说明hdztAwardServiceClient方法的完整包名，以便AI关联引用
-------------------------------------------------------------------------
PuzzleComponent中用到了CommonDataDao，请总结这些常用的api，并生成文档到docs/api目录，用于下次AI生成组件代码，文档需要说明CommonDataDao方法的完整包名，以便AI关联引用
-------------------------------------------------------------------------
common_data_dao_api.md文档个格式内容写的很好，common_service_api.md也按照这个格式内容来改写
ata_dao_api.md文档个格式内容写的很好，kafka_service_api.md也按照这个格式内容来改写
common_data_dao_api.md文档个格式内容写的很好，locker_api.md也按照这个格式内容来改写
common_data_dao_api.md文档个格式内容写的很好，memberinfo_service_api.md也按照这个格式内容来改写
common_data_dao_api.md文档个格式内容写的很好，userinfo_service_api.md也按照这个格式内容来改写
-------------------------------------------------------------------------
common_data_dao_api.md文档个格式内容写的很好，userinfo_service_api.md也按照这个格式内容来改写
-------------------------------------------------------------------------
PuzzleComponent中用到了CommonBroadCastService，请总结这些常用的api，并生成文档到docs/api目录，参考api_template.md这个文档格式来生成
-------------------------------------------------------------------------

HeadlineTimeComponent中用到了 hdztRankingThriftClient，请总结这些常用的api，并生成文档到docs/api目录，参考api_template.md这个文档格式来生成
-------------------------------------------------------------------------
CPTaskChangeWelfareComponentAttr、CpTravelDiaryComponentAttr、DragonTreasureComponentAttr是组件属性定义，请总结下组件属性的编码规范用法，特别是@ComponentAttrField注解的使用，形成为文档，为AI生成代码提供规范